export type Environment = 'development' | 'test' | 'production';

export interface EnvironmentConfig {
  supabaseUrl: string;
  supabaseAnonKey: string;
  environment: Environment;
  debug: boolean;
  apiBaseUrl: string;
}

const environments: Record<Environment, EnvironmentConfig> = {
  development: {
    supabaseUrl: "https://qotcxsnnuzaupxqjihsw.supabase.co",
    supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFvdGN4c25udXphdXB4cWppaHN3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0ODI0MjcsImV4cCI6MjA3MDA1ODQyN30.CwbwglK7q8A9Xctxm9OGv4Cyp-441uYAOXRx_TM6rTM",
    environment: 'development',
    debug: true,
    apiBaseUrl: '/api'
  },
  test: {
    supabaseUrl: "https://zflnlrhqhvjsiraqohce.supabase.co",
    supabaseAnonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpmbG5scmhxaHZqc2lyYXFvaGNlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyNDYxNTcsImV4cCI6MjA3MDgyMjE1N30.mdctSjpUpGA0Dg0oZJbYMb9YpZcHU9VAp2sqJ3R7ykM",
    environment: 'test',
    debug: true,
    apiBaseUrl: '/api'
  },
  production: {
    supabaseUrl: "REPLACE_WITH_PROD_SUPABASE_URL",
    supabaseAnonKey: "REPLACE_WITH_PROD_SUPABASE_ANON_KEY",
    environment: 'production',
    debug: false,
    apiBaseUrl: '/api'
  }
};

// Determine current environment based on hostname or other factors
function getCurrentEnvironment(): Environment {
  if (typeof window === 'undefined') return 'development';
  
  const hostname = window.location.hostname;
  /*
  // Treat Lovable project domains as development - IMPORTANT: includes lovable.app domains!
  if (
      hostname.includes('lovableproject.com') ||
      hostname.includes('lovable.app')) {  // This fixes the preview issue!
    return 'development';
  }
  */
  if (hostname.includes('test') || hostname.includes('staging') || hostname.includes('localhost') || 
      hostname.includes('127.0.0.1') ) {
    return 'test';
  }
  
  return 'production';
}

export const currentEnvironment = getCurrentEnvironment();
export const config = environments[currentEnvironment];

export function getEnvironmentConfig(env?: Environment): EnvironmentConfig {
  return environments[env || currentEnvironment];
}