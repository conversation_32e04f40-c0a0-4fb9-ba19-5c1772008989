@tailwind base;
@tailwind components;
@tailwind utilities;

/* TennisClub Booker Design System - Tennis-inspired colors and styling
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    /* Tennis-themed color palette */
    --tennis-green: 142 69% 32%;
    --tennis-green-light: 142 50% 45%;
    --tennis-green-dark: 142 80% 25%;
    --court-clay: 25 85% 65%;
    --court-white: 0 0% 98%;
    --net-gray: 220 13% 91%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: var(--tennis-green);
    --primary-foreground: 0 0% 98%;

    --secondary: var(--court-white);
    --secondary-foreground: var(--tennis-green);

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: var(--tennis-green-light);
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: var(--net-gray);
    --input: var(--net-gray);
    --ring: var(--tennis-green);
    
    /* Custom gradients and effects */
    --gradient-tennis: linear-gradient(135deg, hsl(var(--tennis-green)), hsl(var(--tennis-green-light)));
    --gradient-court: linear-gradient(180deg, hsl(var(--court-white)), hsl(var(--background)));
    --shadow-tennis: 0 10px 30px -10px hsl(var(--tennis-green) / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(222.2 84% 4.9% / 0.1);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Info semantic color (blue for highlights) */
    --info: 225 73% 57%;
    --info-foreground: 0 0% 100%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Info semantic color (blue for highlights) */
    --info: 225 73% 57%;
    --info-foreground: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}