import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { tenantResolver, type Club, type TenantContext } from '@/lib/tenant';

const TenantContext = createContext<TenantContext | undefined>(undefined);

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const [club, setClub] = useState<Club | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const resolveCurrentTenant = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const resolvedClub = await tenantResolver.resolveClub();
        setClub(resolvedClub);
        
        // Set tenant context for database operations if club is found
        if (resolvedClub) {
          console.log('Resolved club:', resolvedClub.name, resolvedClub.id);
        } else {
          console.log('No club resolved for current domain/path');
        }
      } catch (err) {
        console.error('Error resolving tenant:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    resolveCurrentTenant();
  }, []);

  const value: TenantContext = {
    club,
    isLoading,
    error,
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant() {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Hook to check if current context is meta admin
export function useIsMetaAdmin() {
  return tenantResolver.isMetaAdmin();
}

// Hook to build club-relative URLs
export function useClubUrl() {
  return (path: string = '') => tenantResolver.buildClubUrl(path);
}