import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider } from "@/components/ui/sidebar";
import { TenantProvider } from "@/contexts/TenantContext";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import Register from "./pages/Register";
import NotFound from "./pages/NotFound";
import AdminDashboardPage from "./pages/admin/AdminDashboardPage";
import AdminMembersPage from "./pages/admin/AdminMembersPage";
import AdminCourtsPage from "./pages/admin/AdminCourtsPage";
import AdminBookingsPage from "./pages/admin/AdminBookingsPage";
import AdminSettingsPage from "./pages/admin/AdminSettingsPage";
import AdminWorkServicesPage from "./pages/admin/AdminWorkServicesPage";
import AdminClubPage from "./pages/admin/AdminClubPage";
import MemberLayout from "./layouts/MemberLayout";
import TrainerLayout from "./layouts/TrainerLayout";
import MemberDashboardPage from "./pages/member/MemberDashboardPage";
import MemberBookingPage from "./pages/member/MemberBookingPage";
import MemberBookingsPage from "./pages/member/MemberBookingsPage";
import MemberProfilePage from "./pages/member/MemberProfilePage";
import MemberPaymentsPage from "./pages/member/MemberPaymentsPage";
import MemberTournamentsPage from "./pages/member/MemberTournamentsPage";
import MemberCoursesPage from "./pages/member/MemberCoursesPage";
import MemberStatsPage from "./pages/member/MemberStatsPage";
import TrainerDashboardPage from "./pages/trainer/TrainerDashboardPage";
import TrainerCoursesPage from "./pages/trainer/TrainerCoursesPage";
import TrainerParticipantsPage from "./pages/trainer/TrainerParticipantsPage";
import TrainerTournamentsPage from "./pages/trainer/TrainerTournamentsPage";
import TrainerBookingPage from "./pages/trainer/TrainerBookingPage";
import TrainerReportsPage from "./pages/trainer/TrainerReportsPage";
import MetaAdminDashboardPage from "./pages/meta-admin/MetaAdminDashboardPage";
import MetaAdminClubsPage from "./pages/meta-admin/MetaAdminClubsPage";
import MetaAdminUsersPage from "./pages/meta-admin/MetaAdminUsersPage";
import MetaAdminDomainsPage from "./pages/meta-admin/MetaAdminDomainsPage";
import MetaAdminBillingPage from "./pages/meta-admin/MetaAdminBillingPage";
import MetaAdminSupportPage from "./pages/meta-admin/MetaAdminSupportPage";
import MetaAdminContentPage from "./pages/meta-admin/MetaAdminContentPage";
import MetaAdminMonitoringPage from "./pages/meta-admin/MetaAdminMonitoringPage";
import MetaAdminSettingsPage from "./pages/meta-admin/MetaAdminSettingsPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <TenantProvider>
          <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/auth" element={<Auth />} />
          <Route path="/register" element={<Register />} />
          <Route path="/admin" element={<AdminDashboardPage />} />
          <Route path="/admin/members" element={<AdminMembersPage />} />
          <Route path="/admin/courts" element={<AdminCourtsPage />} />
          <Route path="/admin/bookings" element={<AdminBookingsPage />} />
          <Route path="/admin/tournaments" element={<NotFound />} />
          <Route path="/admin/communication" element={<NotFound />} />
          <Route path="/admin/management" element={<AdminClubPage />} />
          <Route path="/admin/finances" element={<NotFound />} />
          <Route path="/admin/work-services" element={<AdminWorkServicesPage />} />
          <Route path="/admin/settings" element={<AdminSettingsPage />} />
          
          <Route path="/member" element={<MemberLayout />}>
            <Route index element={<MemberDashboardPage />} />
            <Route path="booking" element={<MemberBookingPage />} />
            <Route path="bookings" element={<MemberBookingsPage />} />
            <Route path="statistiken" element={<MemberStatsPage />} />
            <Route path="profile" element={<MemberProfilePage />} />
            <Route path="payments" element={<MemberPaymentsPage />} />
            <Route path="tournaments" element={<MemberTournamentsPage />} />
            <Route path="courses" element={<MemberCoursesPage />} />
          </Route>

          <Route path="/trainer" element={<TrainerLayout />}>
            <Route index element={<TrainerDashboardPage />} />
            <Route path="courses" element={<TrainerCoursesPage />} />
            <Route path="participants" element={<TrainerParticipantsPage />} />
            <Route path="tournaments" element={<TrainerTournamentsPage />} />
            <Route path="booking" element={<TrainerBookingPage />} />
            <Route path="reports" element={<TrainerReportsPage />} />
          </Route>

          {/* Meta Admin Routes */}
        <Route path="/meta-admin" element={<MetaAdminDashboardPage />} />
        <Route path="/meta-admin/clubs" element={<MetaAdminClubsPage />} />
        <Route path="/meta-admin/clubs/new" element={<MetaAdminClubsPage />} />
        <Route path="/meta-admin/users/meta-admins" element={<MetaAdminUsersPage />} />
        <Route path="/meta-admin/domains" element={<MetaAdminDomainsPage />} />
        <Route path="/meta-admin/billing/plans" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/billing/subscriptions" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/billing/invoices" element={<MetaAdminBillingPage />} />
        <Route path="/meta-admin/content/email-templates" element={<MetaAdminContentPage />} />
        <Route path="/meta-admin/content/branding" element={<MetaAdminContentPage />} />
        <Route path="/meta-admin/monitoring/logs" element={<MetaAdminMonitoringPage />} />
        <Route path="/meta-admin/monitoring/performance" element={<MetaAdminMonitoringPage />} />
        <Route path="/meta-admin/support/tickets" element={<MetaAdminSupportPage />} />
        <Route path="/meta-admin/settings" element={<MetaAdminSettingsPage />} />

          {/* Path-based club routing */}
          <Route path="/c/:clubSlug/*" element={<Index />} />
          
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
          </Routes>
        </TenantProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
