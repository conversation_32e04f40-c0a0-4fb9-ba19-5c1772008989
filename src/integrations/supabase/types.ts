export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      accounts: {
        Row: {
          account_type: Database["public"]["Enums"]["account_type"]
          address_city: string | null
          address_full: string | null
          address_house_number: string | null
          address_postal_code: string | null
          address_street: string | null
          age_type_mismatch_flag: boolean | null
          billing_member_id: string | null
          birth_date: string | null
          club_id: string | null
          created_at: string
          email: string
          first_name: string
          id: string
          last_name: string
          membership_type: Database["public"]["Enums"]["membership_type"] | null
          phone: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          account_type?: Database["public"]["Enums"]["account_type"]
          address_city?: string | null
          address_full?: string | null
          address_house_number?: string | null
          address_postal_code?: string | null
          address_street?: string | null
          age_type_mismatch_flag?: boolean | null
          billing_member_id?: string | null
          birth_date?: string | null
          club_id?: string | null
          created_at?: string
          email: string
          first_name: string
          id?: string
          last_name: string
          membership_type?:
            | Database["public"]["Enums"]["membership_type"]
            | null
          phone: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          account_type?: Database["public"]["Enums"]["account_type"]
          address_city?: string | null
          address_full?: string | null
          address_house_number?: string | null
          address_postal_code?: string | null
          address_street?: string | null
          age_type_mismatch_flag?: boolean | null
          billing_member_id?: string | null
          birth_date?: string | null
          club_id?: string | null
          created_at?: string
          email?: string
          first_name?: string
          id?: string
          last_name?: string
          membership_type?:
            | Database["public"]["Enums"]["membership_type"]
            | null
          phone?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "accounts_billing_member_fk"
            columns: ["billing_member_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "accounts_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      activities: {
        Row: {
          approved_at: string | null
          approved_by: string | null
          club_id: string | null
          created_at: string
          created_by: string | null
          description: string | null
          hourly_rate: number | null
          id: string
          name: string
          requested_by: string | null
          status: string
        }
        Insert: {
          approved_at?: string | null
          approved_by?: string | null
          club_id?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          hourly_rate?: number | null
          id?: string
          name: string
          requested_by?: string | null
          status?: string
        }
        Update: {
          approved_at?: string | null
          approved_by?: string | null
          club_id?: string | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          hourly_rate?: number | null
          id?: string
          name?: string
          requested_by?: string | null
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_registrations: {
        Row: {
          activity_id: string
          id: string
          member_id: string
          notes: string | null
          registered_at: string
          status: string
        }
        Insert: {
          activity_id: string
          id?: string
          member_id: string
          notes?: string | null
          registered_at?: string
          status?: string
        }
        Update: {
          activity_id?: string
          id?: string
          member_id?: string
          notes?: string | null
          registered_at?: string
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_registrations_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "activity_registrations_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "members"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          club_id: string | null
          created_at: string
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          resource_id: string | null
          resource_type: string
          session_id: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          club_id?: string | null
          created_at?: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id?: string | null
          resource_type: string
          session_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          club_id?: string | null
          created_at?: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          resource_id?: string | null
          resource_type?: string
          session_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_agreements: {
        Row: {
          beneficiary_account_id: string
          id: string
          payer_account_id: string
          share_percent: number | null
          valid_from: string
          valid_to: string | null
        }
        Insert: {
          beneficiary_account_id: string
          id?: string
          payer_account_id: string
          share_percent?: number | null
          valid_from?: string
          valid_to?: string | null
        }
        Update: {
          beneficiary_account_id?: string
          id?: string
          payer_account_id?: string
          share_percent?: number | null
          valid_from?: string
          valid_to?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "billing_agreements_beneficiary_account_id_fkey"
            columns: ["beneficiary_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "billing_agreements_payer_account_id_fkey"
            columns: ["payer_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      bookings: {
        Row: {
          acting_account_id: string | null
          booked_for_account_id: string | null
          booking_date: string
          club_id: string | null
          court_id: string
          created_at: string
          created_by: string | null
          end_at: string | null
          end_time: string
          id: string
          partner_name: string | null
          player_name: string
          start_at: string | null
          start_time: string
          time_range: unknown | null
          updated_at: string
        }
        Insert: {
          acting_account_id?: string | null
          booked_for_account_id?: string | null
          booking_date: string
          club_id?: string | null
          court_id: string
          created_at?: string
          created_by?: string | null
          end_at?: string | null
          end_time: string
          id?: string
          partner_name?: string | null
          player_name: string
          start_at?: string | null
          start_time: string
          time_range?: unknown | null
          updated_at?: string
        }
        Update: {
          acting_account_id?: string | null
          booked_for_account_id?: string | null
          booking_date?: string
          club_id?: string | null
          court_id?: string
          created_at?: string
          created_by?: string | null
          end_at?: string | null
          end_time?: string
          id?: string
          partner_name?: string | null
          player_name?: string
          start_at?: string | null
          start_time?: string
          time_range?: unknown | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_acting_account_id_fkey"
            columns: ["acting_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_booked_for_account_id_fkey"
            columns: ["booked_for_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      club_memberships: {
        Row: {
          club_id: string
          id: string
          is_active: boolean
          joined_at: string
          role: string
          user_id: string
        }
        Insert: {
          club_id: string
          id?: string
          is_active?: boolean
          joined_at?: string
          role?: string
          user_id: string
        }
        Update: {
          club_id?: string
          id?: string
          is_active?: boolean
          joined_at?: string
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "club_memberships_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      club_subscriptions: {
        Row: {
          club_id: string
          created_at: string
          current_period_end: string | null
          current_period_start: string | null
          id: string
          plan_id: string
          status: string
          stripe_subscription_id: string | null
          trial_end: string | null
          updated_at: string
        }
        Insert: {
          club_id: string
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id: string
          status?: string
          stripe_subscription_id?: string | null
          trial_end?: string | null
          updated_at?: string
        }
        Update: {
          club_id?: string
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_id?: string
          status?: string
          stripe_subscription_id?: string | null
          trial_end?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "club_subscriptions_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_subscriptions_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "plans"
            referencedColumns: ["id"]
          },
        ]
      }
      clubs: {
        Row: {
          created_at: string
          custom_domain: string | null
          description: string | null
          id: string
          is_active: boolean
          logo_url: string | null
          name: string
          settings: Json | null
          slug: string
          subdomain: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          custom_domain?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          logo_url?: string | null
          name: string
          settings?: Json | null
          slug: string
          subdomain?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          custom_domain?: string | null
          description?: string | null
          id?: string
          is_active?: boolean
          logo_url?: string | null
          name?: string
          settings?: Json | null
          slug?: string
          subdomain?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      court_availability: {
        Row: {
          court_id: string
          created_at: string
          days_of_week: number[]
          end_time: string
          id: string
          start_time: string
        }
        Insert: {
          court_id: string
          created_at?: string
          days_of_week: number[]
          end_time: string
          id?: string
          start_time: string
        }
        Update: {
          court_id?: string
          created_at?: string
          days_of_week?: number[]
          end_time?: string
          id?: string
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "court_availability_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
        ]
      }
      court_blocks: {
        Row: {
          court_id: string
          created_at: string
          end_date: string
          end_time: string | null
          id: string
          reason: string
          start_date: string
          start_time: string | null
          updated_at: string
        }
        Insert: {
          court_id: string
          created_at?: string
          end_date: string
          end_time?: string | null
          id?: string
          reason: string
          start_date: string
          start_time?: string | null
          updated_at?: string
        }
        Update: {
          court_id?: string
          created_at?: string
          end_date?: string
          end_time?: string | null
          id?: string
          reason?: string
          start_date?: string
          start_time?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "court_blocks_court_id_fkey"
            columns: ["court_id"]
            isOneToOne: false
            referencedRelation: "courts"
            referencedColumns: ["id"]
          },
        ]
      }
      courts: {
        Row: {
          club_id: string | null
          court_group: string | null
          created_at: string
          id: string
          lock_reason: string | null
          locked: boolean
          number: number
          surface_type: string | null
          updated_at: string
        }
        Insert: {
          club_id?: string | null
          court_group?: string | null
          created_at?: string
          id?: string
          lock_reason?: string | null
          locked?: boolean
          number: number
          surface_type?: string | null
          updated_at?: string
        }
        Update: {
          club_id?: string | null
          court_group?: string | null
          created_at?: string
          id?: string
          lock_reason?: string | null
          locked?: boolean
          number?: number
          surface_type?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "courts_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      domains: {
        Row: {
          club_id: string
          created_at: string
          dns_verified_at: string | null
          domain_name: string
          domain_type: string
          id: string
          is_primary: boolean
          is_verified: boolean
          ssl_expires_at: string | null
          ssl_status: string
          updated_at: string
          verification_token: string | null
        }
        Insert: {
          club_id: string
          created_at?: string
          dns_verified_at?: string | null
          domain_name: string
          domain_type: string
          id?: string
          is_primary?: boolean
          is_verified?: boolean
          ssl_expires_at?: string | null
          ssl_status?: string
          updated_at?: string
          verification_token?: string | null
        }
        Update: {
          club_id?: string
          created_at?: string
          dns_verified_at?: string | null
          domain_name?: string
          domain_type?: string
          id?: string
          is_primary?: boolean
          is_verified?: boolean
          ssl_expires_at?: string | null
          ssl_status?: string
          updated_at?: string
          verification_token?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "domains_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      email_templates: {
        Row: {
          content: string
          created_at: string
          id: string
          is_active: boolean
          name: string
          subject: string
          template_type: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          is_active?: boolean
          name: string
          subject: string
          template_type: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          is_active?: boolean
          name?: string
          subject?: string
          template_type?: string
          updated_at?: string
        }
        Relationships: []
      }
      fee_assignments: {
        Row: {
          account_id: string | null
          amount_paid: number | null
          assigned_at: string
          created_at: string
          due_date: string | null
          fee_type_id: string
          id: string
          notes: string | null
          paid_at: string | null
          payment_method: string | null
          payment_reference: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          account_id?: string | null
          amount_paid?: number | null
          assigned_at?: string
          created_at?: string
          due_date?: string | null
          fee_type_id: string
          id?: string
          notes?: string | null
          paid_at?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          account_id?: string | null
          amount_paid?: number | null
          assigned_at?: string
          created_at?: string
          due_date?: string | null
          fee_type_id?: string
          id?: string
          notes?: string | null
          paid_at?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fee_assignments_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fee_assignments_fee_type_id_fkey"
            columns: ["fee_type_id"]
            isOneToOne: false
            referencedRelation: "fee_types"
            referencedColumns: ["id"]
          },
        ]
      }
      fee_categories: {
        Row: {
          color: string | null
          created_at: string
          display_name: string
          id: string
          is_active: boolean
          sort_order: number
          updated_at: string
          value: string
        }
        Insert: {
          color?: string | null
          created_at?: string
          display_name: string
          id?: string
          is_active?: boolean
          sort_order?: number
          updated_at?: string
          value: string
        }
        Update: {
          color?: string | null
          created_at?: string
          display_name?: string
          id?: string
          is_active?: boolean
          sort_order?: number
          updated_at?: string
          value?: string
        }
        Relationships: []
      }
      fee_type_membership_types: {
        Row: {
          created_at: string
          fee_type_id: string
          id: string
          membership_type_id: string
        }
        Insert: {
          created_at?: string
          fee_type_id: string
          id?: string
          membership_type_id: string
        }
        Update: {
          created_at?: string
          fee_type_id?: string
          id?: string
          membership_type_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fee_type_membership_types_fee_type_id_fkey"
            columns: ["fee_type_id"]
            isOneToOne: false
            referencedRelation: "fee_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fee_type_membership_types_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["id"]
          },
        ]
      }
      fee_types: {
        Row: {
          age_restrictions: Json | null
          amount: number
          billing_cycle: string
          category: string
          category_id: string | null
          created_at: string
          currency: string
          description: string | null
          id: string
          is_active: boolean
          membership_category:
            | Database["public"]["Enums"]["membership_category"]
            | null
          membership_type_id: string | null
          name: string
          requires_membership: boolean
          updated_at: string
          validity_period_days: number | null
        }
        Insert: {
          age_restrictions?: Json | null
          amount: number
          billing_cycle?: string
          category?: string
          category_id?: string | null
          created_at?: string
          currency?: string
          description?: string | null
          id?: string
          is_active?: boolean
          membership_category?:
            | Database["public"]["Enums"]["membership_category"]
            | null
          membership_type_id?: string | null
          name: string
          requires_membership?: boolean
          updated_at?: string
          validity_period_days?: number | null
        }
        Update: {
          age_restrictions?: Json | null
          amount?: number
          billing_cycle?: string
          category?: string
          category_id?: string | null
          created_at?: string
          currency?: string
          description?: string | null
          id?: string
          is_active?: boolean
          membership_category?:
            | Database["public"]["Enums"]["membership_category"]
            | null
          membership_type_id?: string | null
          name?: string
          requires_membership?: boolean
          updated_at?: string
          validity_period_days?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fee_types_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "fee_categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fee_types_membership_type_id_fkey"
            columns: ["membership_type_id"]
            isOneToOne: false
            referencedRelation: "membership_types"
            referencedColumns: ["value"]
          },
        ]
      }
      group_members: {
        Row: {
          account_id: string
          added_at: string
          group_id: string
          id: string
          role: string
        }
        Insert: {
          account_id: string
          added_at?: string
          group_id: string
          id?: string
          role?: string
        }
        Update: {
          account_id?: string
          added_at?: string
          group_id?: string
          id?: string
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_members_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_members_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          billing_member_id: string | null
          created_at: string
          id: string
          total_fee: number
          type: Database["public"]["Enums"]["membership_type"]
        }
        Insert: {
          billing_member_id?: string | null
          created_at?: string
          id?: string
          total_fee: number
          type: Database["public"]["Enums"]["membership_type"]
        }
        Update: {
          billing_member_id?: string | null
          created_at?: string
          id?: string
          total_fee?: number
          type?: Database["public"]["Enums"]["membership_type"]
        }
        Relationships: [
          {
            foreignKeyName: "groups_billing_member_fk"
            columns: ["billing_member_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      guardianships: {
        Row: {
          dependent_account_id: string
          guardian_account_id: string
          id: string
          valid_from: string
          valid_to: string | null
        }
        Insert: {
          dependent_account_id: string
          guardian_account_id: string
          id?: string
          valid_from?: string
          valid_to?: string | null
        }
        Update: {
          dependent_account_id?: string
          guardian_account_id?: string
          id?: string
          valid_from?: string
          valid_to?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guardianships_dependent_account_id_fkey"
            columns: ["dependent_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardianships_guardian_account_id_fkey"
            columns: ["guardian_account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      household_members: {
        Row: {
          account_id: string
          household_id: string
          id: string
          role: string
          valid_from: string
          valid_to: string | null
        }
        Insert: {
          account_id: string
          household_id: string
          id?: string
          role: string
          valid_from?: string
          valid_to?: string | null
        }
        Update: {
          account_id?: string
          household_id?: string
          id?: string
          role?: string
          valid_from?: string
          valid_to?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "household_members_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "household_members_household_id_fkey"
            columns: ["household_id"]
            isOneToOne: false
            referencedRelation: "households"
            referencedColumns: ["id"]
          },
        ]
      }
      households: {
        Row: {
          created_at: string | null
          id: string
          name: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string | null
        }
        Relationships: []
      }
      invites: {
        Row: {
          account_id: string
          created_at: string | null
          expires_at: string | null
          id: string
          method: string
          status: string | null
          target: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          expires_at?: string | null
          id?: string
          method: string
          status?: string | null
          target?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          expires_at?: string | null
          id?: string
          method?: string
          status?: string | null
          target?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invites_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      login_identities: {
        Row: {
          account_id: string
          created_at: string | null
          id: string
          is_primary: boolean | null
          type: string
          value: string | null
        }
        Insert: {
          account_id: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          type: string
          value?: string | null
        }
        Update: {
          account_id?: string
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          type?: string
          value?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "login_identities_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      members: {
        Row: {
          address: string
          annual_fee: number
          birth_date: string
          club_id: string | null
          created_at: string
          email: string
          first_name: string
          group_id: string | null
          iban: string
          id: string
          last_name: string
          membership_type: Database["public"]["Enums"]["membership_type"]
          payment_status: string
          phone: string
          updated_at: string
        }
        Insert: {
          address: string
          annual_fee: number
          birth_date: string
          club_id?: string | null
          created_at?: string
          email: string
          first_name: string
          group_id?: string | null
          iban: string
          id?: string
          last_name: string
          membership_type: Database["public"]["Enums"]["membership_type"]
          payment_status?: string
          phone: string
          updated_at?: string
        }
        Update: {
          address?: string
          annual_fee?: number
          birth_date?: string
          club_id?: string | null
          created_at?: string
          email?: string
          first_name?: string
          group_id?: string | null
          iban?: string
          id?: string
          last_name?: string
          membership_type?: Database["public"]["Enums"]["membership_type"]
          payment_status?: string
          phone?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_members_group"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "members_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_cancellations: {
        Row: {
          account_id: string | null
          effective_date: string
          group_id: string | null
          id: string
          notes: string | null
          processed_at: string | null
          processed_by: string | null
          reason: string | null
          requested_at: string
          status: Database["public"]["Enums"]["cancellation_status"]
          user_id: string
        }
        Insert: {
          account_id?: string | null
          effective_date: string
          group_id?: string | null
          id?: string
          notes?: string | null
          processed_at?: string | null
          processed_by?: string | null
          reason?: string | null
          requested_at?: string
          status?: Database["public"]["Enums"]["cancellation_status"]
          user_id: string
        }
        Update: {
          account_id?: string | null
          effective_date?: string
          group_id?: string | null
          id?: string
          notes?: string | null
          processed_at?: string | null
          processed_by?: string | null
          reason?: string | null
          requested_at?: string
          status?: Database["public"]["Enums"]["cancellation_status"]
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "membership_cancellations_account_id_fkey"
            columns: ["account_id"]
            isOneToOne: false
            referencedRelation: "accounts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "membership_cancellations_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      membership_types: {
        Row: {
          created_at: string
          description: string | null
          display_name: string
          id: string
          is_active: boolean
          is_group_type: boolean
          updated_at: string
          value: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          display_name: string
          id?: string
          is_active?: boolean
          is_group_type?: boolean
          updated_at?: string
          value: string
        }
        Update: {
          created_at?: string
          description?: string | null
          display_name?: string
          id?: string
          is_active?: boolean
          is_group_type?: boolean
          updated_at?: string
          value?: string
        }
        Relationships: []
      }
      meta_admin_permissions: {
        Row: {
          created_at: string
          expires_at: string | null
          granted_at: string
          granted_by: string
          id: string
          role: Database["public"]["Enums"]["meta_admin_role"]
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string | null
          granted_at?: string
          granted_by: string
          id?: string
          role: Database["public"]["Enums"]["meta_admin_role"]
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string | null
          granted_at?: string
          granted_by?: string
          id?: string
          role?: Database["public"]["Enums"]["meta_admin_role"]
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      plans: {
        Row: {
          created_at: string
          description: string | null
          features: Json
          id: string
          is_active: boolean
          limits: Json
          name: string
          price_monthly: number | null
          price_yearly: number | null
          sort_order: number
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean
          limits?: Json
          name: string
          price_monthly?: number | null
          price_yearly?: number | null
          sort_order?: number
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          features?: Json
          id?: string
          is_active?: boolean
          limits?: Json
          name?: string
          price_monthly?: number | null
          price_yearly?: number | null
          sort_order?: number
          updated_at?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          birth_date: string
          city: string
          created_at: string
          email: string
          first_name: string
          house_number: string
          id: string
          join_date: string
          last_name: string
          membership_category: Database["public"]["Enums"]["membership_category"]
          postal_code: string
          profile_image_url: string | null
          street: string
          updated_at: string
        }
        Insert: {
          birth_date: string
          city: string
          created_at?: string
          email: string
          first_name: string
          house_number: string
          id: string
          join_date?: string
          last_name: string
          membership_category: Database["public"]["Enums"]["membership_category"]
          postal_code: string
          profile_image_url?: string | null
          street: string
          updated_at?: string
        }
        Update: {
          birth_date?: string
          city?: string
          created_at?: string
          email?: string
          first_name?: string
          house_number?: string
          id?: string
          join_date?: string
          last_name?: string
          membership_category?: Database["public"]["Enums"]["membership_category"]
          postal_code?: string
          profile_image_url?: string | null
          street?: string
          updated_at?: string
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          created_at: string | null
          id: string
          permission: Database["public"]["Enums"]["app_permission"]
          role: Database["public"]["Enums"]["app_role"]
        }
        Insert: {
          created_at?: string | null
          id?: string
          permission: Database["public"]["Enums"]["app_permission"]
          role: Database["public"]["Enums"]["app_role"]
        }
        Update: {
          created_at?: string | null
          id?: string
          permission?: Database["public"]["Enums"]["app_permission"]
          role?: Database["public"]["Enums"]["app_role"]
        }
        Relationships: []
      }
      support_tickets: {
        Row: {
          assigned_to: string | null
          club_id: string | null
          created_at: string
          description: string
          id: string
          priority: string
          status: string
          subject: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          assigned_to?: string | null
          club_id?: string | null
          created_at?: string
          description: string
          id?: string
          priority?: string
          status?: string
          subject: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          assigned_to?: string | null
          club_id?: string | null
          created_at?: string
          description?: string
          id?: string
          priority?: string
          status?: string
          subject?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "support_tickets_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["id"]
          },
        ]
      }
      system_metrics: {
        Row: {
          id: string
          labels: Json | null
          metric_name: string
          metric_value: number
          recorded_at: string
        }
        Insert: {
          id?: string
          labels?: Json | null
          metric_name: string
          metric_value: number
          recorded_at?: string
        }
        Update: {
          id?: string
          labels?: Json | null
          metric_name?: string
          metric_value?: number
          recorded_at?: string
        }
        Relationships: []
      }
      system_settings: {
        Row: {
          created_at: string
          id: string
          key: string
          updated_at: string
          value: Json
        }
        Insert: {
          created_at?: string
          id?: string
          key: string
          updated_at?: string
          value: Json
        }
        Update: {
          created_at?: string
          id?: string
          key?: string
          updated_at?: string
          value?: Json
        }
        Relationships: []
      }
      user_roles: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          id: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role: Database["public"]["Enums"]["app_role"]
          user_id: string
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          id?: string
          role?: Database["public"]["Enums"]["app_role"]
          user_id?: string
        }
        Relationships: []
      }
      work_log_assignments: {
        Row: {
          id: string
          member_id: string
          work_log_id: string
        }
        Insert: {
          id?: string
          member_id: string
          work_log_id: string
        }
        Update: {
          id?: string
          member_id?: string
          work_log_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "work_log_assignments_member_id_fkey"
            columns: ["member_id"]
            isOneToOne: false
            referencedRelation: "members"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "work_log_assignments_work_log_id_fkey"
            columns: ["work_log_id"]
            isOneToOne: false
            referencedRelation: "work_logs"
            referencedColumns: ["id"]
          },
        ]
      }
      work_logs: {
        Row: {
          activity_id: string
          created_at: string
          date: string
          duration_hours: number
          id: string
        }
        Insert: {
          activity_id: string
          created_at?: string
          date: string
          duration_hours: number
          id?: string
        }
        Update: {
          activity_id?: string
          created_at?: string
          date?: string
          duration_hours?: number
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "work_logs_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      account_belongs_to_user: {
        Args: { _account_id: string; _user_id: string }
        Returns: boolean
      }
      effective_rights: {
        Args: { u: string }
        Returns: {
          account_id: string
        }[]
      }
      get_current_club_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_user_permissions: {
        Args: { _user_id: string }
        Returns: {
          permission: Database["public"]["Enums"]["app_permission"]
        }[]
      }
      get_user_roles: {
        Args: { _user_id: string }
        Returns: {
          role: Database["public"]["Enums"]["app_role"]
        }[]
      }
      has_meta_admin_role: {
        Args: {
          _role: Database["public"]["Enums"]["meta_admin_role"]
          _user_id: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: {
          _permission: Database["public"]["Enums"]["app_permission"]
          _user_id: string
        }
        Returns: boolean
      }
      has_role: {
        Args: {
          _role: Database["public"]["Enums"]["app_role"]
          _user_id: string
        }
        Returns: boolean
      }
      is_billing_member_for_group: {
        Args: { _group_id: string; _user_id: string }
        Returns: boolean
      }
      is_meta_admin: {
        Args: { _user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      account_type: "Member" | "Guest" | "Trainer" | "Admin"
      app_permission:
        | "members_view"
        | "members_create"
        | "members_edit"
        | "members_delete"
        | "courts_view"
        | "courts_book"
        | "courts_manage"
        | "courts_config"
        | "payments_view"
        | "payments_process"
        | "payments_manage"
        | "tournaments_view"
        | "tournaments_create"
        | "tournaments_manage"
        | "courses_view"
        | "courses_create"
        | "courses_manage"
        | "courses_participate"
        | "community_view"
        | "community_post"
        | "community_moderate"
        | "reports_view"
        | "reports_generate"
        | "reports_export"
        | "system_config"
        | "user_roles_manage"
        | "profile_edit_own"
        | "profile_view_others"
      app_role: "admin" | "trainer" | "mitglied" | "guest" | "super_admin"
      cancellation_status: "pending" | "approved" | "rejected"
      membership_category:
        | "Kind"
        | "Jugendlicher"
        | "Student"
        | "Erwachsener"
        | "Familie"
      membership_type:
        | "Child"
        | "Youth"
        | "Student"
        | "Adult"
        | "Couple"
        | "Family"
        | "Kinder"
        | "Jugendliche"
        | "Studenten"
        | "Erwachsene"
        | "Senioren"
        | "Paare"
        | "Familien"
      meta_admin_role:
        | "SUPER_ADMIN"
        | "SUPPORT_ADMIN"
        | "BILLING_ADMIN"
        | "READONLY"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      account_type: ["Member", "Guest", "Trainer", "Admin"],
      app_permission: [
        "members_view",
        "members_create",
        "members_edit",
        "members_delete",
        "courts_view",
        "courts_book",
        "courts_manage",
        "courts_config",
        "payments_view",
        "payments_process",
        "payments_manage",
        "tournaments_view",
        "tournaments_create",
        "tournaments_manage",
        "courses_view",
        "courses_create",
        "courses_manage",
        "courses_participate",
        "community_view",
        "community_post",
        "community_moderate",
        "reports_view",
        "reports_generate",
        "reports_export",
        "system_config",
        "user_roles_manage",
        "profile_edit_own",
        "profile_view_others",
      ],
      app_role: ["admin", "trainer", "mitglied", "guest", "super_admin"],
      cancellation_status: ["pending", "approved", "rejected"],
      membership_category: [
        "Kind",
        "Jugendlicher",
        "Student",
        "Erwachsener",
        "Familie",
      ],
      membership_type: [
        "Child",
        "Youth",
        "Student",
        "Adult",
        "Couple",
        "Family",
        "Kinder",
        "Jugendliche",
        "Studenten",
        "Erwachsene",
        "Senioren",
        "Paare",
        "Familien",
      ],
      meta_admin_role: [
        "SUPER_ADMIN",
        "SUPPORT_ADMIN",
        "BILLING_ADMIN",
        "READONLY",
      ],
    },
  },
} as const
