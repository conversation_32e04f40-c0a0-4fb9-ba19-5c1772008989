import { useEffect, useState, useCallback } from "react";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { cn } from "@/lib/utils";

const Register = () => {
  const { toast } = useToast();

  // Mitglied states
  const [title, setTitle] = useState<string>("");
  const [gender, setGender] = useState<string>("");
  const [nationality, setNationality] = useState<string>("");
  const [mFirstName, setMFirstName] = useState("");
  const [mLastName, setMLastName] = useState("");
  const [mBirthDate, setMBirthDate] = useState<Date | undefined>();
  const [street, setStreet] = useState("");
  const [houseNumber, setHouseNumber] = useState("");
  const [postalCode, setPostalCode] = useState("");
  const [city, setCity] = useState("");
  const [phone, setPhone] = useState("");
  const [mEmail, setMEmail] = useState("");
  const [mEmailConfirm, setMEmailConfirm] = useState("");
  const [mPassword, setMPassword] = useState("");
  const [loadingMember, setLoadingMember] = useState(false);

  // Antragsteller-Auswahl & Mitgliedschaftstypen
  const [applicantType, setApplicantType] = useState<'self' | 'child' | 'other'>('self');
  const [membershipTypes, setMembershipTypes] = useState<{ id: string; value: string; display_name: string }[]>([]);
  const [selectedMembershipType, setSelectedMembershipType] = useState<string>("");
  const [membershipLoading, setMembershipLoading] = useState<boolean>(true);
  const [membershipError, setMembershipError] = useState<string | null>(null);
  const MEMBERSHIP_CACHE_KEY = 'membership_types_cache_v1';
  const sleep = (ms: number) => new Promise((res) => setTimeout(res, ms));
  const loadMembershipTypes = useCallback(async (attempt: number = 1): Promise<void> => {
    setMembershipError(null);
    if (attempt === 1) setMembershipLoading(true);
    try {
      console.log('[Register] Lade Mitgliedschaftsformen, Versuch', attempt);
      const { data, error } = await supabase
        .from('membership_types')
        .select('id, value, display_name')
        .eq('is_active', true)
        .order('display_name', { ascending: true });

      if (error || !data) {
        console.error('[Register] Fehler beim Laden', error);
        if (attempt < 3) {
          await sleep(300 * attempt);
          return loadMembershipTypes(attempt + 1);
        }
        setMembershipLoading(false);
        setMembershipError(error?.message || 'Laden fehlgeschlagen.');
        return;
      }

      console.log('[Register] Mitgliedschaftsformen geladen:', data?.length);
      setMembershipTypes(data as any);
      setMembershipLoading(false);
      try { localStorage.setItem(MEMBERSHIP_CACHE_KEY, JSON.stringify(data)); } catch {}
    } catch (e) {
      console.error('[Register] Unerwarteter Fehler', e);
      setMembershipLoading(false);
      setMembershipError('Unerwarteter Fehler');
    }
  }, []);

  // Gast states (gleichen Felder wie Mitglied)
  const [gTitle, setGTitle] = useState<string>("");
  const [gGender, setGGender] = useState<string>("");
  const [gNationality, setGNationality] = useState<string>("");
  const [gFirstName, setGFirstName] = useState("");
  const [gLastName, setGLastName] = useState("");
  const [gBirthDate, setGBirthDate] = useState<Date | undefined>();
  const [gStreet, setGStreet] = useState("");
  const [gHouseNumber, setGHouseNumber] = useState("");
  const [gPostalCode, setGPostalCode] = useState("");
  const [gCity, setGCity] = useState("");
  const [gPhone, setGPhone] = useState("");
  const [gEmail, setGEmail] = useState("");
  const [gEmailConfirm, setGEmailConfirm] = useState("");
  const [gPassword, setGPassword] = useState("");
  const [loadingGuest, setLoadingGuest] = useState(false);

  useEffect(() => {
    document.title = "Registrierung | TennisVerein";
    const meta = document.querySelector('meta[name="description"]');
    if (meta) meta.setAttribute("content", "Registrierung: Mitglied werden oder Gast-Account erstellen.");
  }, []);

  // Aktive Mitgliedschaftsformen laden (öffentlich) mit Cache & Retry
  useEffect(() => {
    try {
      const cached = localStorage.getItem(MEMBERSHIP_CACHE_KEY);
      if (cached) {
        const parsed = JSON.parse(cached);
        if (Array.isArray(parsed) && parsed.length) {
          setMembershipTypes(parsed);
          setMembershipLoading(false);
        }
      }
    } catch {}
    loadMembershipTypes();
  }, [loadMembershipTypes]);

  const signup = async (

    email: string,
    password: string,
    accountType: "member" | "guest",
    meta?: Record<string, any>
  ) => {
    const redirectUrl = `${window.location.origin}/`;
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          account_type: accountType,
          ...meta,
        },
      },
    });
    return { error };
  };

  const handleMemberSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    if (mEmail !== mEmailConfirm) {
      toast({
        variant: "destructive",
        title: "E-Mail stimmt nicht überein",
        description: "Bitte geben Sie die gleiche E-Mail-Adresse in beiden Feldern ein.",
      });
      return;
    }
    if (!selectedMembershipType) {
      toast({
        variant: "destructive",
        title: "Mitgliedschaftsform fehlt",
        description: "Bitte wählen Sie eine Mitgliedschaftsform aus.",
      });
      return;
    }
    setLoadingMember(true);
    const { error } = await signup(mEmail, mPassword, "member", {
      title,
      gender,
      nationality,
      first_name: mFirstName,
      last_name: mLastName,
      birth_date: mBirthDate ? format(mBirthDate, "yyyy-MM-dd") : undefined,
      street,
      house_number: houseNumber,
      postal_code: postalCode,
      city,
      phone,
      applicant_type: applicantType,
      membership_category: selectedMembershipType,
    });
    if (error) {
      toast({
        variant: "destructive",
        title: "Registrierung fehlgeschlagen",
        description:
          error.message.includes("User already registered")
            ? "Ein Benutzer mit dieser E-Mail-Adresse existiert bereits. Bitte melden Sie sich an."
            : error.message,
      });
    } else {
      toast({
        title: "Mitgliedsregistrierung erfolgreich",
        description: "Bitte E-Mail bestätigen. Zahlungsprozess folgt später.",
      });
    }
    setLoadingMember(false);
  };

  const handleGuestSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (gEmail !== gEmailConfirm) {
      toast({
        variant: "destructive",
        title: "E-Mail stimmt nicht überein",
        description: "Bitte geben Sie die gleiche E-Mail-Adresse in beiden Feldern ein.",
      });
      return;
    }

    setLoadingGuest(true);
    const { error } = await signup(gEmail, gPassword, "guest", {
      title: gTitle,
      gender: gGender,
      nationality: gNationality,
      first_name: gFirstName,
      last_name: gLastName,
      birth_date: gBirthDate ? format(gBirthDate, "yyyy-MM-dd") : undefined,
      street: gStreet,
      house_number: gHouseNumber,
      postal_code: gPostalCode,
      city: gCity,
      phone: gPhone,
    });
    if (error) {
      toast({
        variant: "destructive",
        title: "Registrierung fehlgeschlagen",
        description:
          error.message.includes("User already registered")
            ? "Ein Benutzer mit dieser E-Mail-Adresse existiert bereits. Bitte melden Sie sich an."
            : error.message,
      });
    } else {
      toast({
        title: "Gastkonto erstellt",
        description: "Bitte E-Mail bestätigen; danach direkter Zugang zum Gastbereich.",
      });
    }
    setLoadingGuest(false);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <main className="pt-24 pb-16">
        <section className="container mx-auto px-4 max-w-6xl">
          <header className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold tracking-tight">Registrierung</h1>
            <p className="text-muted-foreground mt-2">Werden Sie Mitglied oder erstellen Sie einen Gast-Account.</p>
          </header>

          <Tabs defaultValue="member" className="w-full">
            <TabsList className="grid w-full max-w-md grid-cols-2">
              <TabsTrigger value="member">Mitglied</TabsTrigger>
              <TabsTrigger
                value="guest"
                className="bg-[hsl(var(--info))] text-[hsl(var(--info-foreground))] data-[state=active]:bg-[hsl(var(--info))] data-[state=active]:text-[hsl(var(--info-foreground))]"
              >
                Gast-Account
              </TabsTrigger>
            </TabsList>

            <TabsContent value="member" className="mt-8">
              <form onSubmit={handleMemberSignup} className="space-y-8" aria-label="Mitglied Registrierung">
                {/* Antragsteller & Mitgliedschaft */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Antragsdaten</h3>
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Für wen wird der Antrag gestellt?</Label>
                      <RadioGroup value={applicantType} onValueChange={(val) => setApplicantType(val as 'self' | 'child' | 'other')} className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="self" id="app-self" />
                          <Label htmlFor="app-self">Ich selbst</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="child" id="app-child" />
                          <Label htmlFor="app-child">Mein Kind</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="other" id="app-other" />
                          <Label htmlFor="app-other">Andere Person</Label>
                        </div>
                      </RadioGroup>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Mitgliedschaftsform</Label>
                      <Select value={selectedMembershipType} onValueChange={setSelectedMembershipType}>
                        <SelectTrigger disabled={membershipLoading || membershipTypes.length === 0}>
                          <SelectValue placeholder={membershipLoading ? "Lädt..." : "Mitgliedschaft wählen"} />
                        </SelectTrigger>
                        <SelectContent className="z-50 bg-background">
                          {membershipTypes.length > 0 ? (
                            membershipTypes.map((mt) => (
                              <SelectItem key={mt.id} value={mt.value}>
                                {mt.display_name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="-" disabled>
                              Keine Optionen verfügbar
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      {membershipLoading && (
                        <p className="text-sm text-muted-foreground">Lade Mitgliedschaftsformen...</p>
                      )}
                      {membershipError && (
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-destructive">Konnte nicht laden.</span>
                          <Button type="button" variant="outline" size="sm" onClick={() => loadMembershipTypes()}>
                            Erneut versuchen
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Persönliche Daten */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Persönliche Daten</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Titel</Label>
                      <Select value={title} onValueChange={setTitle}>
                        <SelectTrigger>
                          <SelectValue placeholder="Bitte wählen" />
                        </SelectTrigger>
                        <SelectContent className="z-50 bg-background">
                          <SelectItem value="Herr">Herr</SelectItem>
                          <SelectItem value="Frau">Frau</SelectItem>
                          <SelectItem value="Divers">Divers</SelectItem>
                          <SelectItem value="Dr.">Dr.</SelectItem>
                          <SelectItem value="Prof.">Prof.</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Geschlecht</Label>
                      <Select value={gender} onValueChange={setGender}>
                        <SelectTrigger>
                          <SelectValue placeholder="Bitte wählen" />
                        </SelectTrigger>
                        <SelectContent className="z-50 bg-background">
                          <SelectItem value="männlich">Männlich</SelectItem>
                          <SelectItem value="weiblich">Weiblich</SelectItem>
                          <SelectItem value="divers">Divers</SelectItem>
                          <SelectItem value="keine-angabe">Keine Angabe</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Nationalität</Label>
                      <Input value={nationality} onChange={(e) => setNationality(e.target.value)} placeholder="z. B. Deutsch" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="mFirstName" className="text-sm font-medium">Vorname</Label>
                      <Input id="mFirstName" value={mFirstName} onChange={(e) => setMFirstName(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mLastName" className="text-sm font-medium">Nachname</Label>
                      <Input id="mLastName" value={mLastName} onChange={(e) => setMLastName(e.target.value)} required />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Geburtsdatum</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {mBirthDate ? format(mBirthDate, "dd.MM.yyyy", { locale: de }) : "Datum auswählen"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-50 bg-background" align="start">
                          <Calendar
                            mode="single"
                            selected={mBirthDate}
                            onSelect={setMBirthDate}
                            disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                            initialFocus
                            className={cn("p-3 pointer-events-auto")}
                            captionLayout="dropdown-buttons"
                            fromYear={1920}
                            toYear={new Date().getFullYear()}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-sm font-medium">Telefonnummer</Label>
                      <Input id="phone" type="tel" value={phone} onChange={(e) => setPhone(e.target.value)} />
                    </div>
                  </div>
                </div>

                {/* Adresse */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Adresse</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="md:col-span-3 space-y-2">
                      <Label htmlFor="street" className="text-sm font-medium">Straße</Label>
                      <Input id="street" value={street} onChange={(e) => setStreet(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="houseNumber" className="text-sm font-medium">Hausnummer</Label>
                      <Input id="houseNumber" value={houseNumber} onChange={(e) => setHouseNumber(e.target.value)} required />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="postalCode" className="text-sm font-medium">Postleitzahl</Label>
                      <Input id="postalCode" value={postalCode} onChange={(e) => setPostalCode(e.target.value)} required />
                    </div>
                    <div className="md:col-span-2 space-y-2">
                      <Label htmlFor="city" className="text-sm font-medium">Stadt</Label>
                      <Input id="city" value={city} onChange={(e) => setCity(e.target.value)} required />
                    </div>
                  </div>
                </div>

                {/* Zugangsdaten */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Zugangsdaten</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="mEmail" className="text-sm font-medium">E-Mail Adresse</Label>
                      <Input id="mEmail" type="email" value={mEmail} onChange={(e) => setMEmail(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mEmailConfirm" className="text-sm font-medium">E-Mail Adresse bestätigen</Label>
                      <Input id="mEmailConfirm" type="email" value={mEmailConfirm} onChange={(e) => setMEmailConfirm(e.target.value)} required />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="mPassword" className="text-sm font-medium">Passwort</Label>
                      <Input id="mPassword" type="password" value={mPassword} onChange={(e) => setMPassword(e.target.value)} required />
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button type="submit" disabled={loadingMember} size="lg">
                    {loadingMember ? "Registrieren..." : "Als Mitglied registrieren"}
                  </Button>
                  <Button variant="ghost" asChild>
                    <Link to="/">Abbrechen</Link>
                  </Button>
                </div>
              </form>
            </TabsContent>

            <TabsContent value="guest" className="mt-8">
              <form onSubmit={handleGuestSignup} className="space-y-8" aria-label="Gast Registrierung">
                {/* Persönliche Daten */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Persönliche Daten</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Titel</Label>
                      <Select value={gTitle} onValueChange={setGTitle}>
                        <SelectTrigger>
                          <SelectValue placeholder="Bitte wählen" />
                        </SelectTrigger>
                        <SelectContent className="z-50 bg-background">
                          <SelectItem value="Herr">Herr</SelectItem>
                          <SelectItem value="Frau">Frau</SelectItem>
                          <SelectItem value="Divers">Divers</SelectItem>
                          <SelectItem value="Dr.">Dr.</SelectItem>
                          <SelectItem value="Prof.">Prof.</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Geschlecht</Label>
                      <Select value={gGender} onValueChange={setGGender}>
                        <SelectTrigger>
                          <SelectValue placeholder="Bitte wählen" />
                        </SelectTrigger>
                        <SelectContent className="z-50 bg-background">
                          <SelectItem value="männlich">Männlich</SelectItem>
                          <SelectItem value="weiblich">Weiblich</SelectItem>
                          <SelectItem value="divers">Divers</SelectItem>
                          <SelectItem value="keine-angabe">Keine Angabe</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Nationalität</Label>
                      <Input value={gNationality} onChange={(e) => setGNationality(e.target.value)} placeholder="z. B. Deutsch" />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gFirstName" className="text-sm font-medium">Vorname</Label>
                      <Input id="gFirstName" value={gFirstName} onChange={(e) => setGFirstName(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gLastName" className="text-sm font-medium">Nachname</Label>
                      <Input id="gLastName" value={gLastName} onChange={(e) => setGLastName(e.target.value)} required />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Geburtsdatum</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            type="button"
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {gBirthDate ? format(gBirthDate, "dd.MM.yyyy", { locale: de }) : "Datum auswählen"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 z-50 bg-background" align="start">
                          <Calendar
                            mode="single"
                            selected={gBirthDate}
                            onSelect={setGBirthDate}
                            disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                            initialFocus
                            className={cn("p-3 pointer-events-auto")}
                            captionLayout="dropdown-buttons"
                            fromYear={1920}
                            toYear={new Date().getFullYear()}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gPhone" className="text-sm font-medium">Telefonnummer</Label>
                      <Input id="gPhone" type="tel" value={gPhone} onChange={(e) => setGPhone(e.target.value)} />
                    </div>
                  </div>
                </div>

                {/* Adresse */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Adresse</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="md:col-span-3 space-y-2">
                      <Label htmlFor="gStreet" className="text-sm font-medium">Straße</Label>
                      <Input id="gStreet" value={gStreet} onChange={(e) => setGStreet(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gHouseNumber" className="text-sm font-medium">Hausnummer</Label>
                      <Input id="gHouseNumber" value={gHouseNumber} onChange={(e) => setGHouseNumber(e.target.value)} required />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gPostalCode" className="text-sm font-medium">Postleitzahl</Label>
                      <Input id="gPostalCode" value={gPostalCode} onChange={(e) => setGPostalCode(e.target.value)} required />
                    </div>
                    <div className="md:col-span-2 space-y-2">
                      <Label htmlFor="gCity" className="text-sm font-medium">Stadt</Label>
                      <Input id="gCity" value={gCity} onChange={(e) => setGCity(e.target.value)} required />
                    </div>
                  </div>
                </div>

                {/* Zugangsdaten */}
                <div className="space-y-6 p-6 border rounded-lg bg-card">
                  <h3 className="text-lg font-semibold">Zugangsdaten</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gEmail" className="text-sm font-medium">E-Mail Adresse</Label>
                      <Input id="gEmail" type="email" value={gEmail} onChange={(e) => setGEmail(e.target.value)} required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gEmailConfirm" className="text-sm font-medium">E-Mail Adresse bestätigen</Label>
                      <Input id="gEmailConfirm" type="email" value={gEmailConfirm} onChange={(e) => setGEmailConfirm(e.target.value)} required />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="gPassword" className="text-sm font-medium">Passwort</Label>
                      <Input id="gPassword" type="password" value={gPassword} onChange={(e) => setGPassword(e.target.value)} required />
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 pt-4">
                  <Button type="submit" disabled={loadingGuest} size="lg" className="bg-[hsl(var(--info))] text-[hsl(var(--info-foreground))] hover:bg-[hsl(var(--info))]/90">
                    {loadingGuest ? "Registrieren..." : "Gast-Account erstellen"}
                  </Button>
                  <Button variant="ghost" asChild>
                    <Link to="/">Abbrechen</Link>
                  </Button>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </section>
      </main>
    </div>
  );
};

export default Register;
