import AdminLayout from "@/components/admin/AdminLayout";
import BookingsList from "@/components/admin/BookingsList";
import BookingRules from "@/components/admin/BookingRules";
import BookingCalendar from "@/components/admin/BookingCalendar";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";

const AdminBookingsPage = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Buchungsverwaltung</h1>
          <p className="text-muted-foreground">Verwalte alle Buchungen und konfiguriere Buchungsregeln</p>
        </div>
        
        <Tabs defaultValue="bookings" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="bookings">Buchungen</TabsTrigger>
            <TabsTrigger value="calendar">Kalenderansicht</TabsTrigger>
            <TabsTrigger value="rules">Buchungsregeln</TabsTrigger>
          </TabsList>
          
          <TabsContent value="bookings" className="space-y-0">
            <BookingsList />
          </TabsContent>
          
          <TabsContent value="calendar" className="space-y-0">
            <BookingCalendar />
          </TabsContent>
          
          <TabsContent value="rules" className="space-y-0">
            <BookingRules />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminBookingsPage;