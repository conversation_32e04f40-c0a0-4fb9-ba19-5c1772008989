import AdminLayout from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Settings, Shield, Database, Bell, Mail, Euro, User } from "lucide-react";
import { FeeManagement } from "@/components/admin/FeeManagement";

const AdminSettingsPage = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Systemeinstellungen</h1>
          <p className="text-muted-foreground">Verwalte allgemeine Einstellungen und Systemkonfiguration</p>
        </div>

        <Tabs defaultValue="general" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">Allgemein</TabsTrigger>
            <TabsTrigger value="security">Sicherheit</TabsTrigger>
            <TabsTrigger value="booking">Buchungen</TabsTrigger>
            <TabsTrigger value="notifications">Benachrichtigungen</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Allgemeine Einstellungen
                  </CardTitle>
                  <CardDescription>
                    Grundlegende Vereinseinstellungen
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="club-name">Vereinsname</Label>
                    <Input id="club-name" defaultValue="TennisVerein" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="club-email">Vereins-E-Mail</Label>
                    <Input id="club-email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="club-phone">Telefon</Label>
                    <Input id="club-phone" defaultValue="+49 123 456789" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="board-chairman">1. Vorstandsvorsitzende/r</Label>
                    <Input 
                      id="board-chairman" 
                      placeholder="Name des 1. Vorstandsvorsitzenden" 
                      defaultValue=""
                    />
                  </div>
                  <Button className="w-full">Speichern</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Sicherheitseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Zugriff und Berechtigungen verwalten
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Zwei-Faktor-Authentifizierung</Label>
                      <p className="text-sm text-muted-foreground">
                        Zusätzliche Sicherheit für Admin-Konten
                      </p>
                    </div>
                    <Switch />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Session-Timeout</Label>
                      <p className="text-sm text-muted-foreground">
                        Automatische Abmeldung nach Inaktivität
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="session-duration">Session-Dauer (Minuten)</Label>
                    <Input id="session-duration" type="number" defaultValue="60" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="booking" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Buchungseinstellungen
                  </CardTitle>
                  <CardDescription>
                    Konfiguration des Buchungssystems
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="booking-window">Buchungsfenster (Tage)</Label>
                    <Input id="booking-window" type="number" defaultValue="14" />
                    <p className="text-sm text-muted-foreground">
                      Wie viele Tage im Voraus können Buchungen gemacht werden
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="max-bookings">Max. Buchungen pro Mitglied</Label>
                    <Input id="max-bookings" type="number" defaultValue="3" />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Online-Buchungen aktiviert</Label>
                      <p className="text-sm text-muted-foreground">
                        Mitglieder können online buchen
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Benachrichtigungen
                  </CardTitle>
                  <CardDescription>
                    E-Mail und System-Benachrichtigungen
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Buchungsbestätigungen</Label>
                      <p className="text-sm text-muted-foreground">
                        E-Mail bei neuen Buchungen
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Stornierungsbenachrichtigungen</Label>
                      <p className="text-sm text-muted-foreground">
                        E-Mail bei Buchungsstornierungen
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>System-Warnungen</Label>
                      <p className="text-sm text-muted-foreground">
                        E-Mail bei kritischen Systemereignissen
                      </p>
                    </div>
                    <Switch defaultChecked />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

        </Tabs>

        {/* Save All Button */}
        <div className="flex justify-end">
          <Button size="lg" className="bg-tennis-green hover:bg-tennis-green-dark">
            <Mail className="mr-2 h-4 w-4" />
            Alle Einstellungen speichern
          </Button>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminSettingsPage;