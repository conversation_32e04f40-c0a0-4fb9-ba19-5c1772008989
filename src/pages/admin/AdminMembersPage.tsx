import { useState, useEffect } from "react";
import AdminLayout from "@/components/admin/AdminLayout";
import MemberManagement from "@/components/admin/MemberManagement";
import AccountManagement from "@/components/admin/AccountManagement";
import RoleManagement from "@/components/admin/RoleManagement";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

const AdminMembersPage = () => {
  const [activeRoles, setActiveRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, isLoading: authLoading, isAdmin } = useAuth();

  useEffect(() => {
    if (!authLoading && user) {
      fetchActiveRoles();
    }
  }, [authLoading, user]);

  const fetchActiveRoles = async () => {
    try {
      const { data, error } = await supabase
        .from("accounts")
        .select("account_type")
        .not("account_type", "is", null);

      if (error) throw error;

      // Get unique roles
      const uniqueRoles = [...new Set(data?.map(account => account.account_type) || [])];
      setActiveRoles(uniqueRoles);
    } catch (error) {
      console.error("Error fetching roles:", error);
    } finally {
      setLoading(false);
    }
  };

  const getRolePlural = (role: string): string => {
    const pluralMap: { [key: string]: string } = {
      "Member": "Mitglieder",
      "Guest": "Gäste", 
      "Trainer": "Trainer",
      "Admin": "Admins"
    };
    return pluralMap[role] || `${role}s`;
  };

  // Define the desired order of tabs
  const tabOrder = ["Member", "Guest", "Trainer", "Admin"];
  const orderedRoles = tabOrder.filter(role => activeRoles.includes(role));

  // Show loading while auth is being established
  if (authLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Authentifizierung wird geladen...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // Show error state if not authenticated
  if (!user) {
    return (
      <AdminLayout>
        <div className="flex flex-col items-center justify-center h-64 space-y-3">
          <div className="text-center">
            <p className="text-muted-foreground">
              Nicht authentifiziert. Bitte melden Sie sich an.
            </p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            <span>Lade Rollen...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const totalTabs = activeRoles.length + 1; // +1 for "Alle Benutzer"

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Alle Benutzer</h1>
            <p className="text-muted-foreground">Verwalte alle Benutzer des Vereins nach Rollen organisiert</p>
          </div>
          <RoleManagement />
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Benutzerverwaltung</CardTitle>
            <CardDescription>
              Erstelle, bearbeite und verwalte alle Benutzer mit verschiedenen Rollen und Mitgliedschaftstypen
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="w-full">
              <TabsList className={`grid w-full ${
                totalTabs === 2 ? 'grid-cols-2' :
                totalTabs === 3 ? 'grid-cols-3' :
                totalTabs === 4 ? 'grid-cols-4' :
                totalTabs === 5 ? 'grid-cols-5' :
                'grid-cols-6'
              }`}>
                <TabsTrigger value="all">Alle Benutzer</TabsTrigger>
                {orderedRoles.map((role) => (
                  <TabsTrigger key={role} value={role.toLowerCase()}>
                    {getRolePlural(role)}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <TabsContent value="all" className="mt-6">
                <AccountManagement />
              </TabsContent>
              
              {orderedRoles.map((role) => (
                <TabsContent key={role} value={role.toLowerCase()} className="mt-6">
                  {role === "Member" ? (
                    <MemberManagement />
                  ) : (
                    <AccountManagement defaultFilter={role} />
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminMembersPage;