import AdminLayout from "@/components/admin/AdminLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Plus, Users, Trophy, DollarSign, Crown, RefreshCw } from "lucide-react";
import MembershipTypeManagement from "@/components/admin/MembershipTypeManagement";
import TeamManagement from "@/components/admin/TeamManagement";
import { FeeManagement } from "@/components/admin/FeeManagement";
import { useSearchParams, Link } from "react-router-dom";

const AdminClubPage = () => {
  const [searchParams] = useSearchParams();
  const defaultTab = searchParams.get('tab') || 'membership-types';
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Vereinsverwaltung</h1>
            <p className="text-muted-foreground">Verwaltung von Altersklassen, Mitgliedschaftsformen, Mannschaften und Vereinsstrukturen</p>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Vereins-Management</CardTitle>
            <CardDescription>
              Zentrale Verwaltung aller vereinsrelevanten Strukturen und Kategorien
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue={defaultTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="membership-types">Mitgliedschaften</TabsTrigger>
                <TabsTrigger value="fees">Beiträge & Gebühren</TabsTrigger>
                <TabsTrigger value="teams">Mannschaften</TabsTrigger>
                <TabsTrigger value="schedule">Spielplan</TabsTrigger>
              </TabsList>
              
              <TabsContent value="membership-types" className="mt-6">
                <MembershipTypeManagement />
              </TabsContent>
              
              <TabsContent value="fees" className="mt-6">
                <FeeManagement />
              </TabsContent>
              
              <TabsContent value="teams" className="mt-6">
                <TeamManagement />
              </TabsContent>
              
              <TabsContent value="schedule" className="mt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Spielplan & Platzreservierungen</h3>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" className="text-muted-foreground">
                        <RefreshCw className="h-3 w-3 mr-2" />
                        HTV synchronisieren
                      </Button>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Neues Spiel
                      </Button>
                    </div>
                  </div>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">Anstehende Heimspiele</CardTitle>
                      <CardDescription>Automatische Platzblockierung für Mannschaftsspiele</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                          <div>
                            <div className="font-medium">Herren I vs. TC Musterstadt</div>
                            <div className="text-sm text-muted-foreground">Sonntag, 15. Sep 2024 - 10:00 Uhr</div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Platz 1-2</Badge>
                            <Badge className="bg-green-100 text-green-800">Heimspiel</Badge>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50">
                          <div>
                            <div className="font-medium">Damen I vs. SV Beispielort</div>
                            <div className="text-sm text-muted-foreground">Samstag, 21. Sep 2024 - 14:00 Uhr</div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Platz 3-4</Badge>
                            <Badge className="bg-green-100 text-green-800">Heimspiel</Badge>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50">
                          <div>
                            <div className="font-medium">Herren Ü50 vs. TC Vereinsheim</div>
                            <div className="text-sm text-muted-foreground">Sonntag, 22. Sep 2024 - 09:00 Uhr</div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">Auswärts</Badge>
                            <Badge className="bg-blue-100 text-blue-800">Auswärtsspiel</Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminClubPage;