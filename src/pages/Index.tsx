import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import Navigation from "@/components/Navigation";
import Hero from "@/components/Hero";
import { useTenant, useIsMetaAdmin } from "@/contexts/TenantContext";
import { tenantResolver } from "@/lib/tenant";

const Index = () => {
  const { clubSlug } = useParams();
  const { club, isLoading, error } = useTenant();
  const isMetaAdmin = useIsMetaAdmin();

  useEffect(() => {
    // If we're in a path-based route (/c/clubSlug), resolve the tenant
    if (clubSlug) {
      console.log('Path-based club access:', clubSlug);
    }
  }, [clubSlug]);

  // Show loading while tenant is being resolved
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Lade Vereinsdaten...</p>
        </div>
      </div>
    );
  }

  // Show error if tenant resolution failed
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Fehler beim Laden der Vereinsdaten</p>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  // Meta admin access - redirect to meta admin interface
  if (isMetaAdmin) {
    window.location.href = '/meta-admin';
    return null;
  }

  // Tenant-specific interface
  if (club) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <section id="home">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold mb-4">
                Willkommen bei {club.name}
              </h1>
              {club.description && (
                <p className="text-xl text-muted-foreground mb-6">
                  {club.description}
                </p>
              )}
              <div className="bg-muted p-4 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Tenant: {club.slug} | ID: {club.id}
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  // Default interface when no tenant is resolved
  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <section id="home">
        <Hero />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Multi-Tenant Tennis Platform</h2>
            <p className="text-muted-foreground mb-6">
              Diese Plattform unterstützt mehrere Tennisvereine. Besuchen Sie einen spezifischen Verein über:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto">
              <div className="bg-card p-6 rounded-lg border">
                <h3 className="font-semibold mb-2">Custom Domain</h3>
                <p className="text-sm text-muted-foreground">
                  club.example.com
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg border">
                <h3 className="font-semibold mb-2">Subdomain</h3>
                <p className="text-sm text-muted-foreground">
                  clubname.example.com
                </p>
              </div>
              <div className="bg-card p-6 rounded-lg border">
                <h3 className="font-semibold mb-2">Path-Based</h3>
                <p className="text-sm text-muted-foreground">
                  example.com/c/clubname
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Index;
