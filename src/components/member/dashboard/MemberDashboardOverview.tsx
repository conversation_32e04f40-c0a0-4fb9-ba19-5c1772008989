import { useEffect, useMemo, useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Megaphone, Users, Shield, User as UserIcon } from "lucide-react";
import { format, parse } from "date-fns";
import { de } from "date-fns/locale";

interface Booking {
  id: string;
  booking_date: string; // yyyy-MM-dd
  start_time: string;   // HH:mm:ss
  end_time: string;     // HH:mm:ss
  court_id: string;
  player_name: string;
  partner_name?: string | null;
  created_by?: string | null;
}

interface Court { id: string; number: number }

interface ClubNewsItem { title: string; date?: string; link?: string }

const MemberDashboardOverview = () => {
  const [userId, setUserId] = useState<string | null>(null);
  const [fullName, setFullName] = useState<string>("");
  const [upcoming, setUpcoming] = useState<Booking[]>([]);
  const [recent, setRecent] = useState<Booking[]>([]);
  const [courtsMap, setCourtsMap] = useState<Record<string, Court>>({});
  const [news, setNews] = useState<ClubNewsItem[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const load = async () => {
      try {
        const { data: userRes } = await supabase.auth.getUser();
        const uid = userRes.user?.id || null;
        setUserId(uid);

        // load profile to get the full name
        let name = "";
        if (uid) {
          const { data: profile } = await supabase
            .from("profiles")
            .select("first_name, last_name")
            .eq("id", uid)
            .maybeSingle();
          if (profile?.first_name || profile?.last_name) {
            name = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
          }
          const { data: rolesData } = await supabase.rpc('get_user_roles', { _user_id: uid });
          setRoles((rolesData || []).map((r: { role: string }) => r.role));
        }
        setFullName(name);

        // Load courts for number mapping
        const { data: courts } = await supabase
          .from("courts")
          .select("id, number")
          .order("number");
        const cMap: Record<string, Court> = {};
        (courts || []).forEach(c => { cMap[c.id] = c; });
        setCourtsMap(cMap);

        // Date boundaries
        const todayStr = format(new Date(), "yyyy-MM-dd");
        const pastSince = new Date();
        pastSince.setDate(pastSince.getDate() - 90);
        const pastStr = format(pastSince, "yyyy-MM-dd");

        // Upcoming (>= today)
        const { data: upcomingData } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", todayStr)
          .order("booking_date", { ascending: true });

        // Recent past (last 90 days)
        const { data: recentData } = await supabase
          .from("bookings")
          .select("*")
          .gte("booking_date", pastStr)
          .lte("booking_date", todayStr)
          .order("booking_date", { ascending: false });

        const belongsToUser = (b: Booking) => {
          if (!name) return false;
          return b.player_name === name || b.partner_name === name;
        };

        setUpcoming((upcomingData || []).filter(belongsToUser).slice(0, 5));
        setRecent((recentData || []).filter(belongsToUser));

        // News (optional via system_settings -> key = 'club_news', value: [{title, date, link}])
        const { data: settings } = await supabase
          .from("system_settings")
          .select("value")
          .eq("key", "club_news")
          .maybeSingle();
        if (settings?.value && Array.isArray(settings.value)) {
          setNews(settings.value as unknown as ClubNewsItem[]);
        }
      } finally {
        setLoading(false);
      }
    };

    load();
  }, []);

  // Realtime-Updates: Buchungen aktuell halten
  useEffect(() => {
    if (!userId && !fullName) return;

    const refresh = async () => {
      const todayStr = format(new Date(), "yyyy-MM-dd");
      const pastSince = new Date();
      pastSince.setDate(pastSince.getDate() - 90);
      const pastStr = format(pastSince, "yyyy-MM-dd");

      const { data: upcomingData } = await supabase
        .from("bookings")
        .select("*")
        .gte("booking_date", todayStr)
        .order("booking_date", { ascending: true });

      const { data: recentData } = await supabase
        .from("bookings")
        .select("*")
        .gte("booking_date", pastStr)
        .lte("booking_date", todayStr)
        .order("booking_date", { ascending: false });

      const belongs = (b: Booking) => {
        if (userId && b.created_by) return b.created_by === userId;
        if (!fullName) return false;
        return b.player_name === fullName || b.partner_name === fullName;
      };

      setUpcoming((upcomingData || []).filter(belongs).slice(0, 5));
      setRecent((recentData || []).filter(belongs));
    };

    const channel = supabase
      .channel('bookings-dashboard')
      .on('postgres_changes', { event: '*', schema: 'public', table: 'bookings' }, () => {
        refresh();
      })
      .subscribe();

    refresh();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId, fullName]);

  const frequentPartners = useMemo(() => {
    if (!fullName) return [] as { name: string; count: number }[];
    const counts = new Map<string, number>();
    for (const b of recent) {
      const other = b.player_name === fullName ? (b.partner_name || "") : (b.partner_name === fullName ? b.player_name : "");
      if (!other) continue;
      counts.set(other, (counts.get(other) || 0) + 1);
    }
    return Array.from(counts.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }, [recent, fullName]);

  const toDateTime = (b: Booking) => {
    // Combine booking_date and start_time to a JS Date
    // start_time format HH:mm:ss
    return parse(`${b.booking_date} ${b.start_time}`, "yyyy-MM-dd HH:mm:ss", new Date());
  };

  const nextMatches = useMemo(() => {
    return [...upcoming].sort((a, b) => toDateTime(a).getTime() - toDateTime(b).getTime());
  }, [upcoming]);

  return (
    <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-3">
      {/* Meine nächsten Matches */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4" /> Meine nächsten Matches
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Laden…</p>
          ) : nextMatches.length === 0 ? (
            <p className="text-sm text-muted-foreground">Keine kommenden Matches</p>
          ) : (
            <ul className="space-y-2">
              {nextMatches.slice(0, 5).map((m) => {
                const other = m.player_name === fullName ? (m.partner_name || "-") : m.player_name;
                const court = courtsMap[m.court_id]?.number;
                return (
                  <li key={m.id} className="text-sm">
                    <div className="flex items-center justify-between">
                      <span className="truncate mr-2">{other}</span>
                      <span className="text-muted-foreground">
                        {format(toDateTime(m), "EEE dd.MM HH:mm", { locale: de })}
                        {typeof court === 'number' ? ` · P${court}` : ""}
                      </span>
                    </div>
                  </li>
                );
              })}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Vereinsnews */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Megaphone className="h-4 w-4" /> News vom Verein
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Laden…</p>
          ) : news.length === 0 ? (
            <p className="text-sm text-muted-foreground">Keine News vorhanden</p>
          ) : (
            <ul className="space-y-2">
              {news.slice(0, 5).map((n, idx) => (
                <li key={idx} className="text-sm">
                  <div className="flex items-center justify-between">
                    <span className="truncate mr-2">{n.title}</span>
                    {n.date && (
                      <span className="text-muted-foreground">{format(new Date(n.date), "dd.MM.")}</span>
                    )}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      {/* Mein Profil (oben rechts) */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <UserIcon className="h-4 w-4" /> Mein Profil
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <p className="text-sm text-muted-foreground">Laden…</p>
          ) : (
            <div className="space-y-2 text-sm">
              <div>
                <div className="text-muted-foreground">Name</div>
                <div className="font-medium">{fullName || '-'}</div>
              </div>
              <div>
                <div className="text-muted-foreground">Accounttyp</div>
                <div className="font-medium">
                  {roles.length ? roles.map((r) => r.charAt(0).toUpperCase() + r.slice(1)).join(', ') : '-'}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Mannschaft - volle Breite */}
      <Card className="md:col-span-2 xl:col-span-3">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Shield className="h-4 w-4" /> Meine Mannschaft
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Mannschaftsdaten werden in Kürze verfügbar.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberDashboardOverview;
