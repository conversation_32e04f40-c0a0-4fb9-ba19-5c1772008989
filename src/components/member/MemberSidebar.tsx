
import { useEffect, useMemo, useState } from "react";
import { Calendar, User, CreditCard, Trophy, Users, Home, BarChart3, MessageSquare, Layers } from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { supabase } from "@/integrations/supabase/client";

// Locally untyped client for new tables until types are regenerated
const sbAny = supabase as any;

const memberItems = [
  { title: "Dashboard", url: "/member", icon: Home },
  { title: "Plätze buchen", url: "/member/booking", icon: Calendar },
  { title: "Meine Buchungen", url: "/member/bookings", icon: Calendar },
  { title: "Statistiken", url: "/member/statistiken", icon: BarChart3 },
  { title: "Mein Profil", url: "/member/profile", icon: User },
  { title: "Zahlungen", url: "/member/payments", icon: CreditCard },
  { title: "Turniere", url: "/member/tournaments", icon: Trophy },
  { title: "Kurse", url: "/member/courses", icon: Users },
];

type TeamNav = { id: string; name: string };

const MemberSidebar = () => {
  const location = useLocation();
  const currentPath = location.pathname;

  const [teams, setTeams] = useState<TeamNav[]>([]);
  const [loadingTeams, setLoadingTeams] = useState(false);
  const [openTeamId, setOpenTeamId] = useState<string | null>(null);

  const isActive = (path: string) => currentPath === path;

  useEffect(() => {
    const loadTeams = async () => {
      setLoadingTeams(true);
      const { data: userRes } = await supabase.auth.getUser();
      if (!userRes.user) {
        setTeams([]);
        setLoadingTeams(false);
        return;
      }
      // Dank RLS liefert SELECT nur Teams, in denen der User Mitglied ist
      const { data, error } = await sbAny
        .from("teams")
        .select("id, name")
        .order("name", { ascending: true });

      if (error) {
        console.error("Error loading member teams:", error);
        setTeams([]);
      } else {
        setTeams((data || []).map((t: any) => ({ id: t.id, name: t.name })));
      }
      setLoadingTeams(false);
    };

    loadTeams();

    // Optional: Realtime für team_members / teams hinzufügen, später wenn gewünscht
  }, []);

  const getNavCls = ({ isActive }: { isActive: boolean }) =>
    isActive ? "bg-muted text-primary font-medium" : "hover:bg-muted/50";

  const teamLinks = (team: TeamNav) => ([
    { title: "Übersicht", search: `?teamId=${team.id}&section=overview`, icon: Layers },
    { title: "Mannschaft", search: `?teamId=${team.id}&section=team`, icon: Users },
    { title: "Spiele", search: `?teamId=${team.id}&section=games`, icon: Trophy },
    { title: "Training", search: `?teamId=${team.id}&section=training`, icon: Calendar },
    { title: "Nachrichten", search: `?teamId=${team.id}&section=messages`, icon: MessageSquare },
  ]);

  return (
    <Sidebar className="w-60">
      <SidebarContent>
        <div className="p-4">
          <h2 className="font-bold text-lg text-tennis-green">
            Mitgliederbereich
          </h2>
        </div>

        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {memberItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <NavLink
                      to={item.url}
                      end={item.url === "/member"}
                      className={getNavCls}
                    >
                      <item.icon className="mr-2 h-4 w-4" />
                      <span>{item.title}</span>
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {loadingTeams ? (
          <div className="px-4 py-2 text-sm text-muted-foreground">Lade Mannschaften…</div>
        ) : teams.length > 0 ? (
          <SidebarGroup>
            <SidebarGroupLabel>Mannschaften</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {teams.map((team) => (
                  <SidebarMenuItem key={team.id}>
                    <button
                      className="w-full text-left px-2 py-1.5 rounded hover:bg-muted/50 font-medium"
                      onClick={() => setOpenTeamId(prev => prev === team.id ? null : team.id)}
                    >
                      {team.name}
                    </button>

                    {openTeamId === team.id && (
                      <div className="mt-1 ml-2 flex flex-col gap-1">
                        {teamLinks(team).map((link) => (
                          <NavLink
                            key={link.title}
                            to={`/member${link.search}`}
                            className={({ isActive }) =>
                              isActive ? "px-2 py-1.5 rounded bg-muted text-primary" : "px-2 py-1.5 rounded hover:bg-muted/50"
                            }
                          >
                            <div className="flex items-center">
                              <link.icon className="mr-2 h-4 w-4" />
                              <span>{link.title}</span>
                            </div>
                          </NavLink>
                        ))}
                      </div>
                    )}
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ) : (
          <div className="px-4 py-2 text-sm text-muted-foreground">
            Keine Mannschaften vorhanden.
          </div>
        )}
      </SidebarContent>
    </Sidebar>
  );
};

export default MemberSidebar;
