import { useEffect, useState } from "react";
import { CalendarIcon } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { DayView } from "@/components/booking/DayView";
import { CompactWeekView } from "@/components/booking/CompactWeekView";
import { BookingDetailsDialog, type BookingDetails } from "@/components/booking/BookingDetailsDialog";
import { toast } from "sonner";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface Booking {
  id: string;
  booking_date: string;
  start_time: string;
  end_time: string;
  court_id: string;
  player_name: string;
  partner_name?: string;
  created_at: string;
  updated_at: string;
}

const MemberWeekCalendar = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [courts, setCourts] = useState<Court[]>([]);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [showBookingDialog, setShowBookingDialog] = useState(false);
  const [selectedCourtId, setSelectedCourtId] = useState<string | null>(null);
  const [selectedSlotTime, setSelectedSlotTime] = useState<string | null>(null);
  const [selectedSlotDate, setSelectedSlotDate] = useState<Date | null>(null);

  // 08:00–22:00 in 60-min Slots
  const timeSlots: TimeSlot[] = Array.from({ length: 15 }, (_, i) => ({
    time: `${String(8 + i).padStart(2, "0")}:00`,
    available: true,
  }));

  useEffect(() => {
    fetchData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [{ data: courtsData, error: courtsErr }, { data: bookingsData, error: bookingsErr }] = await Promise.all([
        supabase.from("courts").select("*").order("number"),
        supabase.from("bookings").select("*").order("booking_date", { ascending: true }),
      ]);

      if (courtsErr) throw courtsErr;
      if (bookingsErr) throw bookingsErr;

      setCourts(courtsData || []);
      setBookings(bookingsData || []);
    } catch (error) {
      console.error("Fehler beim Laden der Buchungsdaten:", error);
      toast.error("Fehler beim Laden der Buchungsdaten");
    } finally {
      setLoading(false);
    }
  };

  const getSlotStatus = (courtId: string, time: string, date?: Date): string => {
    const targetDate = date || selectedDate;
    if (!targetDate) return "available";

    const dateString = format(targetDate, "yyyy-MM-dd");
    const booking = bookings.find(
      (b) => b.court_id === courtId && b.booking_date === dateString && b.start_time === time + ":00"
    );

    return booking ? "booked" : "available";
  };

  const getBookedPlayerNames = (courtId: string, time: string, date?: Date): string[] => {
    const targetDate = date || selectedDate;
    const dateString = format(targetDate, "yyyy-MM-dd");
    const booking = bookings.find(
      (b) => b.court_id === courtId && b.booking_date === dateString && b.start_time === time + ":00"
    );

    if (!booking) return [];
    const players = [booking.player_name];
    if (booking.partner_name) players.push(booking.partner_name);
    return players;
  };

  const handleSlotSelect = (courtId: string, time: string, date?: Date) => {
    setSelectedCourtId(courtId);
    setSelectedSlotTime(time);
    setSelectedSlotDate(date ?? selectedDate);
    setShowBookingDialog(true);
  };

  const handleConfirmBooking = async (details: BookingDetails) => {
    try {
      const { data: auth } = await supabase.auth.getUser();
      const userId = auth?.user?.id;

      if (!userId) {
        toast.error("Bitte melde dich an, um eine Buchung zu erstellen.");
        return;
      }

      let playerName = "Unbekannter Spieler";
      if (userId) {
        const { data: profile } = await supabase
          .from("profiles")
          .select("first_name,last_name,email")
          .eq("id", userId)
          .maybeSingle();
        if (profile?.first_name || profile?.last_name) {
          playerName = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
        } else if (auth.user.email) {
          playerName = auth.user.email;
        }
      }

      if (!selectedCourtId) {
        toast.error("Kein Platz ausgewählt");
        return;
      }

      const partnerStr = details.partners.map((p) => p.label).join(", ");

      // Eigenen Account ermitteln (Standard: Selbstbuchung)
      const { data: account } = await supabase
        .from("accounts")
        .select("id")
        .eq("user_id", userId)
        .limit(1)
        .maybeSingle();

      // Start/End als Timestamp (timestamptz)
      const makeDateTime = (date: Date, time: string) => {
        const [h, m] = time.split(":").map(Number);
        const dt = new Date(date);
        dt.setHours(h || 0, m || 0, 0, 0);
        return dt.toISOString();
      };
      const startAt = makeDateTime(details.date, details.startTime);
      const endAt = makeDateTime(details.date, details.endTime);

      const payload: any = {
        court_id: selectedCourtId,
        booking_date: format(details.date, "yyyy-MM-dd"),
        start_time: `${details.startTime}:00`,
        end_time: `${details.endTime}:00`,
        player_name: playerName,
        partner_name: partnerStr || null,
        created_by: userId,
        start_at: startAt,
        end_at: endAt,
      };

      const actingId = (details as any)?.actingAccountId ?? account?.id;
      const forId = (details as any)?.bookedForAccountId ?? actingId;

      if (actingId) {
        (payload as any).acting_account_id = actingId;
        (payload as any).booked_for_account_id = forId || actingId;
      }

      const { error } = await supabase.from("bookings").insert([payload]);
      if (error) throw error;

      toast.success(`Platz gebucht am ${format(details.date, "dd.MM.yyyy", { locale: de })} um ${details.startTime}`);
      setShowBookingDialog(false);
      fetchData();
    } catch (e: any) {
      console.error(e);
      toast.error(e?.message || "Fehler bei der Buchung");
    }
  };

  useEffect(() => {
    document.title = `${courts.length > 6 ? "Buchungskalender Tag" : "Buchungskalender Woche"} | Mitglieder`;
  }, [courts.length]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Lade Buchungskalender…</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <CalendarIcon className="h-5 w-5" />
        <h2 className="text-lg font-semibold">{courts.length > 6 ? "Buchungskalender – Tag" : "Buchungskalender – Woche"}</h2>
      </div>
      {courts.length > 6 ? (
        <DayView
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      ) : (
        <CompactWeekView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      )}

      <BookingDetailsDialog
        open={showBookingDialog}
        onOpenChange={setShowBookingDialog}
        courtNumber={courts.find((c) => c.id === selectedCourtId)?.number}
        defaultDate={selectedSlotDate || selectedDate}
        defaultStartTime={selectedSlotTime || "08:00"}
        defaultEndTime={selectedSlotTime ? (() => { const [h,m] = selectedSlotTime.split(":").map(Number); const end = new Date(); end.setHours(h||0); end.setMinutes((m||0)+60); return `${String(end.getHours()).padStart(2,"0")}:${String(end.getMinutes()).padStart(2,"0")}` })() : "09:00"}
        onConfirm={handleConfirmBooking}
      />
    </div>
  );
};

export default MemberWeekCalendar;
