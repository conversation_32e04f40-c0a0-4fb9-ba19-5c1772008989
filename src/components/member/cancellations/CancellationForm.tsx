
import { useEffect, useMemo, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

type Mode = "individual" | "group";

interface CancellationFormProps {
  mode: Mode;
  groupId?: string;
  className?: string;
}

interface CancellationRow {
  id: string;
  user_id: string;
  account_id: string | null;
  group_id: string | null;
  effective_date: string;
  reason: string | null;
  status: "pending" | "approved" | "rejected";
  requested_at: string;
}

const formatDate = (d: string | Date) => {
  const date = typeof d === "string" ? new Date(d) : d;
  return date.toLocaleDateString();
};

export default function CancellationForm({ mode, groupId, className }: CancellationFormProps) {
  const { user } = useAuth();
  const { toast } = useToast();

  const [effectiveDate, setEffectiveDate] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [myAccountId, setMyAccountId] = useState<string | null>(null);
  const [rows, setRows] = useState<CancellationRow[]>([]);

  const isGroupMode = mode === "group";

  // Load user's account (if exists)
  useEffect(() => {
    const loadAccount = async () => {
      if (!user || isGroupMode) return;
      const { data, error } = await supabase
        .from("accounts")
        .select("id")
        .eq("user_id", user.id)
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error("Error loading account:", error);
        return;
      }
      setMyAccountId(data?.id ?? null);
    };
    loadAccount();
  }, [user, isGroupMode]);

  // Load my cancellations
  const loadCancellations = async () => {
    if (!user) return;
    let query = supabase
      .from("membership_cancellations")
      .select("*")
      .order("requested_at", { ascending: false });

    if (isGroupMode) {
      if (!groupId) return;
      query = query.eq("group_id", groupId);
    } else {
      query = query.is("group_id", null);
    }

    const { data, error } = await query;
    if (error) {
      console.error("Error loading cancellations:", error);
      toast({
        title: "Fehler",
        description: "Kündigungen konnten nicht geladen werden.",
        variant: "destructive",
      });
      return;
    }
    setRows((data as CancellationRow[]) || []);
  };

  useEffect(() => {
    loadCancellations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, groupId, mode]);

  const canSubmit = useMemo(() => {
    return !!effectiveDate && !!user && (!isGroupMode || !!groupId);
  }, [effectiveDate, user, isGroupMode, groupId]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setLoading(true);
    try {
      const payload: any = {
        user_id: user.id,
        effective_date: effectiveDate,
        reason: reason || null,
      };
      if (isGroupMode) {
        payload.group_id = groupId!;
      } else {
        if (myAccountId) payload.account_id = myAccountId;
      }

      const { error } = await supabase.from("membership_cancellations").insert(payload);
      if (error) throw error;

      toast({
        title: "Gespeichert",
        description: isGroupMode
          ? "Gruppenkündigung wurde eingereicht."
          : "Ihre Kündigung wurde eingereicht.",
      });

      setEffectiveDate("");
      setReason("");
      await loadCancellations();
    } catch (err: any) {
      console.error(err);
      toast({
        title: "Fehler",
        description: err.message || "Kündigung konnte nicht gespeichert werden.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const onDelete = async (id: string) => {
    if (!confirm("Kündigung wirklich zurückziehen?")) return;
    try {
      const { error } = await supabase
        .from("membership_cancellations")
        .delete()
        .eq("id", id);
      if (error) throw error;
      toast({ title: "Zurückgezogen", description: "Die Kündigung wurde entfernt." });
      await loadCancellations();
    } catch (err: any) {
      toast({
        title: "Fehler",
        description: err.message || "Kündigung konnte nicht entfernt werden.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{isGroupMode ? "Gruppenkündigung" : "Mitgliedschaft kündigen"}</CardTitle>
        <CardDescription>
          {isGroupMode
            ? "Als Rechnungsverantwortliche/r können Sie die Mitgliedschaft Ihrer Gruppe kündigen."
            : "Reichen Sie hier Ihre persönliche Mitgliedschaftskündigung ein."}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <form onSubmit={onSubmit} className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="effective_date">Wirksam ab</Label>
            <Input
              id="effective_date"
              type="date"
              value={effectiveDate}
              onChange={(e) => setEffectiveDate(e.target.value)}
              required
            />
          </div>
          <div className="md:col-span-2 space-y-2">
            <Label htmlFor="reason">Grund (optional)</Label>
            <Textarea
              id="reason"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder="Optional: kurzer Hinweis"
            />
          </div>
          <div className="md:col-span-3 flex justify-end">
            <Button type="submit" disabled={!canSubmit || loading}>
              {loading ? "Speichern..." : isGroupMode ? "Gruppenkündigung einreichen" : "Kündigung einreichen"}
            </Button>
          </div>
        </form>

        <div className="space-y-3">
          <h4 className="text-sm font-medium text-muted-foreground">Meine Kündigungen</h4>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Datum eingereicht</TableHead>
                <TableHead>Wirksam ab</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Grund</TableHead>
                <TableHead className="text-right">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {rows.map((r) => (
                <TableRow key={r.id}>
                  <TableCell>{formatDate(r.requested_at)}</TableCell>
                  <TableCell>{formatDate(r.effective_date)}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        r.status === "approved"
                          ? "default"
                          : r.status === "pending"
                          ? "secondary"
                          : "destructive"
                      }
                    >
                      {r.status === "approved"
                        ? "Genehmigt"
                        : r.status === "pending"
                        ? "Ausstehend"
                        : "Abgelehnt"}
                    </Badge>
                  </TableCell>
                  <TableCell className="max-w-[280px] truncate" title={r.reason || ""}>
                    {r.reason || "—"}
                  </TableCell>
                  <TableCell className="text-right">
                    {r.status === "pending" && (
                      <Button variant="ghost" size="sm" onClick={() => onDelete(r.id)}>
                        Zurückziehen
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
              {rows.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5} className="text-center text-muted-foreground">
                    Keine Kündigungen vorhanden.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
