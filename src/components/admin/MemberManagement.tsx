import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Users } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";

interface Member {
  id: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  address: string;
  email: string;
  phone: string;
  iban: string;
  membership_type: "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien";
  annual_fee: number;
  payment_status: string;
  group_id?: string;
}

interface MembershipType {
  id: string;
  name: string;
  description: string;
  annual_fee: number;
  age_category: 'Jugend' | 'Erwachsene' | 'Senioren';
  benefits: string[];
}

const MemberManagement = () => {
  const [members, setMembers] = useState<Member[]>([]);
  const [membershipTypes, setMembershipTypes] = useState<MembershipType[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    birth_date: "",
    address: "",
    email: "",
    phone: "",
    iban: "",
    membership_type: "" as "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien" | "",
    payment_status: "unpaid"
  });

  useEffect(() => {
    fetchMembers();
    fetchMembershipTypes();
  }, []);

  const fetchMembers = async () => {
    try {
      const { data, error } = await supabase
        .from("members")
        .select("*")
        .order("last_name", { ascending: true });

      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch members",
        variant: "destructive",
      });
    }
  };

  const fetchMembershipTypes = async () => {
    try {
      // Default membership types based on existing system
      const defaultTypes: MembershipType[] = [
        {
          id: 'kinder',
          name: 'Kinder',
          description: 'Mitgliedschaft für Kinder (6-13 Jahre)',
          annual_fee: 120,
          age_category: 'Jugend',
          benefits: ['Kostenlose Platznutzung', 'Kindertraining', 'Teilnahme an Kinderturnieren']
        },
        {
          id: 'jugendliche',
          name: 'Jugendliche',
          description: 'Mitgliedschaft für Jugendliche (14-17 Jahre)',
          annual_fee: 180,
          age_category: 'Jugend',
          benefits: ['Kostenlose Platznutzung', 'Jugendtraining', 'Teilnahme an Jugendturnieren']
        },
        {
          id: 'studenten',
          name: 'Studenten',
          description: 'Mitgliedschaft für Studenten (18-27 Jahre)',
          annual_fee: 240,
          age_category: 'Erwachsene',
          benefits: ['Kostenlose Platznutzung', 'Teilnahme an Turnieren', 'Studentenrabatt']
        },
        {
          id: 'erwachsene',
          name: 'Erwachsene',
          description: 'Mitgliedschaft für Erwachsene (18+ Jahre)',
          annual_fee: 480,
          age_category: 'Erwachsene',
          benefits: ['Kostenlose Platznutzung', 'Teilnahme an Turnieren', 'Stimmrecht']
        },
        {
          id: 'senioren',
          name: 'Senioren',
          description: 'Mitgliedschaft für Senioren (50+ Jahre)',
          annual_fee: 360,
          age_category: 'Senioren',
          benefits: ['Kostenlose Platznutzung', 'Seniorentraining', 'Teilnahme an Turnieren']
        },
        {
          id: 'paare',
          name: 'Paare',
          description: 'Mitgliedschaft für Paare (2 Erwachsene)',
          annual_fee: 800,
          age_category: 'Erwachsene',
          benefits: ['Kostenlose Platznutzung für beide Partner', 'Paartraining', 'Zwei Stimmrechte']
        },
        {
          id: 'familien',
          name: 'Familien',
          description: 'Mitgliedschaft für Familien (2 Erwachsene + Kinder)',
          annual_fee: 720,
          age_category: 'Erwachsene',
          benefits: ['Kostenlose Platznutzung für alle Familienmitglieder', 'Familientraining']
        }
      ];
      
      setMembershipTypes(defaultTypes);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch membership types",
        variant: "destructive",
      });
    }
  };

  const validateAge = (birthDate: string, membershipType: string): boolean => {
    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate()) 
      ? age - 1 : age;

    const selectedType = membershipTypes.find(type => type.name === membershipType);
    if (!selectedType) return true; // Allow if type not found
    
    switch (selectedType.age_category) {
      case "Jugend": return actualAge < 18;
      case "Senioren": return actualAge >= 50;
      case "Erwachsene": return actualAge >= 18;
      default: return true;
    }
  };

  const validateIBAN = (iban: string): boolean => {
    const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;
    return ibanRegex.test(iban.replace(/\s/g, ""));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAge(formData.birth_date, formData.membership_type)) {
      toast({
        title: "Invalid Age",
        description: "Age doesn't match the selected membership type",
        variant: "destructive",
      });
      return;
    }

    if (!validateIBAN(formData.iban)) {
      toast({
        title: "Invalid IBAN",
        description: "Please enter a valid IBAN format",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      if (!formData.membership_type) {
        toast({
          title: "Missing Information",
          description: "Please select a membership type",
          variant: "destructive",
        });
        return;
      }

      const selectedMembershipType = membershipTypes.find(type => type.name === formData.membership_type);
      if (!selectedMembershipType) {
        toast({
          title: "Fehler",
          description: "Ungültiger Mitgliedschaftstyp ausgewählt",
          variant: "destructive",
        });
        return;
      }

      const memberData = {
        ...formData,
        membership_type: formData.membership_type as "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien",
        annual_fee: selectedMembershipType.annual_fee
      };

      if (editingMember) {
        const { error } = await supabase
          .from("members")
          .update(memberData)
          .eq("id", editingMember.id);
        
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "Member updated successfully",
        });
      } else {
        const { error } = await supabase
          .from("members")
          .insert([memberData]);
        
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "Member created successfully",
        });
      }

      setIsDialogOpen(false);
      setEditingMember(null);
      resetForm();
      fetchMembers();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save member",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (memberId: string) => {
    if (!confirm("Are you sure you want to delete this member?")) return;

    try {
      const { error } = await supabase
        .from("members")
        .delete()
        .eq("id", memberId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Member deleted successfully",
      });
      
      fetchMembers();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete member",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (member: Member) => {
    setEditingMember(member);
    setFormData({
      first_name: member.first_name,
      last_name: member.last_name,
      birth_date: member.birth_date,
      address: member.address,
      email: member.email,
      phone: member.phone,
      iban: member.iban,
      membership_type: member.membership_type,
      payment_status: member.payment_status
    });
    setIsDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      first_name: "",
      last_name: "",
      birth_date: "",
      address: "",
      email: "",
      phone: "",
      iban: "",
      membership_type: "" as "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien" | "",
      payment_status: "unpaid"
    });
  };

  const openCreateDialog = () => {
    setEditingMember(null);
    resetForm();
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Header with Add Member Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-primary" />
          <div>
            <h3 className="text-xl font-semibold">Mitgliederverwaltung</h3>
            <p className="text-sm text-muted-foreground">
              Alle Mitgliederkonten
            </p>
          </div>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Mitglied hinzufügen
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingMember ? "Mitglied bearbeiten" : "Neues Mitglied hinzufügen"}
              </DialogTitle>
              <DialogDescription>
                {editingMember 
                  ? "Aktualisieren Sie die Mitgliedsinformationen unten."
                  : "Füllen Sie die Details aus, um ein neues Mitglied zu erstellen."
                }
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="first_name">Vorname</Label>
                  <Input
                    id="first_name"
                    value={formData.first_name}
                    onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="last_name">Nachname</Label>
                  <Input
                    id="last_name"
                    value={formData.last_name}
                    onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="birth_date">Geburtsdatum</Label>
                <Input
                  id="birth_date"
                  type="date"
                  value={formData.birth_date}
                  onChange={(e) => setFormData({ ...formData, birth_date: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="email">E-Mail</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="phone">Telefon</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="address">Adresse</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  required
                />
              </div>

              <div>
                <Label htmlFor="iban">IBAN</Label>
                <Input
                  id="iban"
                  value={formData.iban}
                  onChange={(e) => setFormData({ ...formData, iban: e.target.value.toUpperCase() })}
                  placeholder="DE89 3704 0044 0532 0130 00"
                  required
                />
              </div>

              <div>
                <Label htmlFor="membership_type">Mitgliedschaftstyp</Label>
                <Select
                  value={formData.membership_type}
                  onValueChange={(value) => setFormData({ ...formData, membership_type: value as "Child" | "Youth" | "Student" | "Adult" | "Couple" | "Family" | "Kinder" | "Jugendliche" | "Studenten" | "Erwachsene" | "Senioren" | "Paare" | "Familien" })}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Mitgliedschaftstyp auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    {membershipTypes.map((type) => (
                      <SelectItem key={type.id} value={type.name}>
                        {type.name} (€{type.annual_fee}) - {type.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payment_status">Zahlungsstatus</Label>
                <Select
                  value={formData.payment_status}
                  onValueChange={(value) => setFormData({ ...formData, payment_status: value })}
                  required
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Bezahlt</SelectItem>
                    <SelectItem value="unpaid">Unbezahlt</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Abbrechen
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? "Speichern..." : editingMember ? "Aktualisieren" : "Erstellen"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <CardTitle>Mitglieder</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Typ</TableHead>
                <TableHead>Gebühr</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>E-Mail</TableHead>
                <TableHead className="text-right">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {members.map((member) => (
                <TableRow key={member.id}>
                  <TableCell className="font-medium">
                    {member.first_name} {member.last_name}
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{member.membership_type}</Badge>
                  </TableCell>
                  <TableCell>€{member.annual_fee}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={member.payment_status === "paid" ? "default" : "destructive"}
                    >
                      {member.payment_status === "paid" ? "Bezahlt" : "Unbezahlt"}
                    </Badge>
                  </TableCell>
                  <TableCell>{member.email}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(member)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(member.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberManagement;