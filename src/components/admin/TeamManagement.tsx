import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Crown, Trophy, Users, Plus, Edit, UserPlus, Settings, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

// Locally untyped client to access newly added tables until types are regenerated
const sbAny = supabase as any;

interface Player {
  id: string;
  first_name: string;
  last_name: string;
  birth_date: string;
  email: string;
  phone?: string;
  membership_type: string;
}

interface Team {
  id: string;
  name: string;
  description: string;
  league: string;
  captain_id: string;
  captain_name: string;
  players: Player[];
}

const TeamManagement = () => {
  // Load teams from DB instead of hardcoded demo data
  const [teams, setTeams] = useState<Team[]>([]);
  
  const [availablePlayers, setAvailablePlayers] = useState<Player[]>([]);
  const [filteredPlayers, setFilteredPlayers] = useState<Player[]>([]);
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [ageFrom, setAgeFrom] = useState("");
  const [ageTo, setAgeTo] = useState("");
  const [genderFilter, setGenderFilter] = useState("all");
  const [nameFilter, setNameFilter] = useState("");
  const [loading, setLoading] = useState(false);
  const [isNewTeamDialogOpen, setIsNewTeamDialogOpen] = useState(false);
  const [newTeamData, setNewTeamData] = useState({
    name: "",
    description: "",
    league: "",
    captain_id: "",
    captain_name: ""
  });
  const [memberEmails, setMemberEmails] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  useEffect(() => {
    fetchPlayers();
    fetchTeams();
    fetchMemberEmails();
  }, []);

  useEffect(() => {
    filterPlayers();
  }, [availablePlayers, ageFrom, ageTo, genderFilter, nameFilter, selectedTeam]);

  const fetchPlayers = async () => {
    setLoading(true);
    console.log('Fetching players from database...');
    
    try {
      const { data, error } = await supabase
        .from('accounts')
        .select('id, first_name, last_name, birth_date, email, membership_type, phone')
        .eq('account_type', 'Member')
        .order('first_name');

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }
      
      console.log('Successfully fetched players:', data?.length || 0);
      setAvailablePlayers(data || []);
      
      toast({
        title: "Spieler geladen",
        description: `${data?.length || 0} Spieler erfolgreich geladen.`
      });
    } catch (error) {
      console.error('Error fetching players:', error);
      toast({
        title: "Fehler",
        description: "Spieler konnten nicht geladen werden.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // NEU: Mitglieder-Emails aus der "members"-Tabelle laden und als Set speichern
  const fetchMemberEmails = async () => {
    console.log('Fetching member emails from "members" table for captain selection...');
    const { data, error } = await supabase
      .from('members')
      .select('email');

    if (error) {
      console.error('Error fetching member emails:', error);
      // Kein harter Abbruch – wir fallen auf die vollständige Accountliste zurück
      return;
    }

    const emails = new Set(
      (data || [])
        .map((row: any) => (row.email || '').toLowerCase())
        .filter(Boolean)
    );
    console.log('Loaded member emails count:', emails.size);
    setMemberEmails(emails);
  };

  // NEW: Fetch teams and roster from Supabase
  const fetchTeams = async () => {
    console.log("Fetching teams...");
    const { data: teamsData, error: teamsErr } = await sbAny
      .from('teams')
      .select('id, name, description, league, captain_account_id')
      .order('name', { ascending: true });

    if (teamsErr) {
      console.error("Error loading teams:", teamsErr);
      toast({ title: "Fehler", description: "Mannschaften konnten nicht geladen werden.", variant: "destructive" });
      return;
    }

    // Load all team_members with joined account info and group by team_id
    const { data: membersData, error: membersErr } = await sbAny
      .from('team_members')
      .select('team_id, account_id, accounts!inner(id, first_name, last_name, birth_date, email, phone, membership_type)');

    if (membersErr) {
      console.error("Error loading team members:", membersErr);
      toast({ title: "Fehler", description: "Spieler-Zuordnungen konnten nicht geladen werden.", variant: "destructive" });
      return;
    }

    const grouped: Record<string, Player[]> = {};
    (membersData || []).forEach((row: any) => {
      const acc = row.accounts;
      if (!acc) return;
      const p: Player = {
        id: acc.id,
        first_name: acc.first_name,
        last_name: acc.last_name,
        birth_date: acc.birth_date,
        email: acc.email,
        phone: acc.phone,
        membership_type: acc.membership_type,
      };
      if (!grouped[row.team_id]) grouped[row.team_id] = [];
      grouped[row.team_id].push(p);
    });

    const computedTeams: Team[] = (teamsData || []).map((t: any) => {
      const captain = (availablePlayers || []).find(p => p.id === t.captain_account_id);
      return {
        id: t.id,
        name: t.name,
        description: t.description || "",
        league: t.league || "",
        captain_id: t.captain_account_id || "",
        captain_name: captain ? `${captain.first_name} ${captain.last_name}` : "Noch nicht zugewiesen",
        players: grouped[t.id] || []
      };
    });

    console.log("Teams loaded:", computedTeams.length);
    setTeams(computedTeams);
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  const getGenderFromName = (firstName: string, lastName: string, teamName: string) => {
    // Erweiterte deutsche Vornamen für bessere Geschlechtsbestimmung
    const maleNames = [
      'max', 'paul', 'tom', 'peter', 'alexander', 'michael', 'thomas', 'christian',
      'jan', 'stefan', 'martin', 'andreas', 'daniel', 'frank', 'markus', 'tobias',
      'sebastian', 'matthias', 'jörg', 'dirk', 'marcel', 'patrick', 'dennis',
      'tim', 'felix', 'lukas', 'lars', 'björn', 'florian', 'oliver', 'ralf',
      'klaus', 'wolfgang', 'günter', 'hans', 'karl', 'werner', 'bernd'
    ];
    
    const femaleNames = [
      'anna', 'lisa', 'maria', 'sabine', 'petra', 'andrea', 'sandra', 'julia',
      'stefanie', 'nicole', 'melanie', 'katharina', 'christina', 'tanja', 'anja',
      'marion', 'claudia', 'susanne', 'birgit', 'kerstin', 'martina', 'silke',
      'marie', 'sophie', 'emma', 'clara', 'lena', 'sarah', 'laura', 'nina',
      'jana', 'eva', 'heike', 'barbara', 'brigitte', 'ursula', 'ingrid'
    ];
    
    const firstNameLower = firstName.toLowerCase();
    
    // Erst Team-Namen prüfen (am zuverlässigsten)
    if (teamName.toLowerCase().includes('damen')) return 'female';
    if (teamName.toLowerCase().includes('herren')) return 'male';
    
    // Dann Vornamen prüfen
    if (maleNames.includes(firstNameLower)) return 'male';
    if (femaleNames.includes(firstNameLower)) return 'female';
    
    return 'unknown';
  };

  const filterPlayers = () => {
    let filtered = [...availablePlayers]; // Kopie erstellen für bessere Performance

    console.log('Filtering players. Available players:', filtered.length);
    console.log('Filter criteria - Age:', ageFrom, 'to', ageTo, 'Gender:', genderFilter, 'Name:', nameFilter);

    // Altersfilter anwenden
    if (ageFrom || ageTo) {
      filtered = filtered.filter(player => {
        if (!player.birth_date) return false; // Spieler ohne Geburtsdatum ausschließen
        
        const age = calculateAge(player.birth_date);
        const fromAge = ageFrom ? parseInt(ageFrom) : 0;
        const toAge = ageTo ? parseInt(ageTo) : 150;
        
        const matchesAge = age >= fromAge && age <= toAge;
        if (!matchesAge) {
          console.log(`Age filter: ${player.first_name} ${player.last_name} (${age}) excluded`);
        }
        return matchesAge;
      });
      console.log('After age filter:', filtered.length, 'players');
    }

    // Geschlechtsfilter anwenden
    if (genderFilter !== "all") {
      filtered = filtered.filter(player => {
        const gender = getGenderFromName(player.first_name, player.last_name, selectedTeam?.name || '');
        const matchesGender = gender === genderFilter;
        if (!matchesGender && gender !== 'unknown') {
          console.log(`Gender filter: ${player.first_name} ${player.last_name} (${gender}) excluded`);
        }
        return matchesGender;
      });
      console.log('After gender filter:', filtered.length, 'players');
    }

    // Namensfilter anwenden
    if (nameFilter.trim()) {
      const searchTerm = nameFilter.toLowerCase().trim();
      filtered = filtered.filter(player => {
        const fullName = `${player.first_name} ${player.last_name}`.toLowerCase();
        const matchesName = fullName.includes(searchTerm) || 
                           player.first_name.toLowerCase().includes(searchTerm) ||
                           player.last_name.toLowerCase().includes(searchTerm);
        if (!matchesName) {
          console.log(`Name filter: ${player.first_name} ${player.last_name} excluded`);
        }
        return matchesName;
      });
      console.log('After name filter:', filtered.length, 'players');
    }

    // Bereits zugewiesene Spieler ausschließen
    if (selectedTeam) {
      const assignedPlayerIds = selectedTeam.players.map(p => p.id);
      const beforeExclusion = filtered.length;
      filtered = filtered.filter(player => !assignedPlayerIds.includes(player.id));
      console.log(`Excluded ${beforeExclusion - filtered.length} already assigned players`);
    }

    console.log('Final filtered players:', filtered.length);
    setFilteredPlayers(filtered);
  };

  // NEU: Nur Accounts, die in der "members"-Datenbank existieren (per Email-Abgleich), als Captain anbieten
  const getEligibleCaptains = () => {
    if (!memberEmails || memberEmails.size === 0) {
      // Falls members noch nicht geladen / leer: gesamte Accountliste anzeigen
      return availablePlayers;
    }
    return availablePlayers.filter((p) => memberEmails.has((p.email || '').toLowerCase()));
  };

  // Persistently add a player to a team
  const addPlayerToTeam = async (player: Player) => {
    if (!selectedTeam) return;

    console.log("Adding player to team in DB:", selectedTeam.id, player.id);
    const { error } = await sbAny
      .from('team_members')
      .insert([{ team_id: selectedTeam.id, account_id: player.id }]);

    if (error) {
      console.error("Error adding player:", error);
      toast({ title: "Fehler", description: "Spieler konnte nicht hinzugefügt werden.", variant: "destructive" });
      return;
    }

    // Update local state
    const updatedTeams = teams.map(team => {
      if (team.id === selectedTeam.id) {
        return { ...team, players: [...team.players, player] };
      }
      return team;
    });

    setTeams(updatedTeams);
    setSelectedTeam({
      ...selectedTeam,
      players: [...selectedTeam.players, player]
    });

    filterPlayers();

    toast({
      title: "Spieler hinzugefügt",
      description: `${player.first_name} ${player.last_name} wurde zu ${selectedTeam.name} hinzugefügt.`
    });
  };

  // Persistently remove a player from a team
  const removePlayerFromTeam = async (playerId: string) => {
    if (!selectedTeam) return;

    console.log("Removing player from team in DB:", selectedTeam.id, playerId);
    const { error } = await sbAny
      .from('team_members')
      .delete()
      .eq('team_id', selectedTeam.id)
      .eq('account_id', playerId);

    if (error) {
      console.error("Error removing player:", error);
      toast({ title: "Fehler", description: "Spieler konnte nicht entfernt werden.", variant: "destructive" });
      return;
    }

    const updatedTeams = teams.map(team => {
      if (team.id === selectedTeam.id) {
        return {
          ...team,
          players: team.players.filter(p => p.id !== playerId)
        };
      }
      return team;
    });

    setTeams(updatedTeams);
    setSelectedTeam({
      ...selectedTeam,
      players: selectedTeam.players.filter(p => p.id !== playerId)
    });

    filterPlayers();

    toast({
      title: "Spieler entfernt",
      description: "Spieler wurde aus der Mannschaft entfernt."
    });
  };

  const resetFilters = () => {
    setAgeFrom("");
    setAgeTo("");
    setGenderFilter("all");
    setNameFilter("");
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Mannschaftsverwaltung</h3>
        <Dialog open={isNewTeamDialogOpen} onOpenChange={setIsNewTeamDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Neue Mannschaft
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle>Neue Mannschaft erstellen</DialogTitle>
              <DialogDescription>
                Erstellen Sie eine neue Mannschaft mit allen erforderlichen Details
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="newTeamName">Name *</Label>
                  <Input 
                    id="newTeamName" 
                    placeholder="z.B. Herren I, Damen II"
                    value={newTeamData.name}
                    onChange={(e) => setNewTeamData({...newTeamData, name: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="newTeamLeague">Liga *</Label>
                  <Input 
                    id="newTeamLeague" 
                    placeholder="z.B. Verbandsliga, Kreisliga A"
                    value={newTeamData.league}
                    onChange={(e) => setNewTeamData({...newTeamData, league: e.target.value})}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="newTeamDescription">Beschreibung</Label>
                <Input 
                  id="newTeamDescription" 
                  placeholder="Kurze Beschreibung der Mannschaft"
                  value={newTeamData.description}
                  onChange={(e) => setNewTeamData({...newTeamData, description: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="newTeamCaptain">Spielführer</Label>
                <Select
                  value={newTeamData.captain_id}
                  onValueChange={(value) => {
                    const selectedPlayer = availablePlayers.find(p => p.id === value);
                    if (selectedPlayer) {
                      setNewTeamData({
                        ...newTeamData,
                        captain_id: value,
                        captain_name: `${selectedPlayer.first_name} ${selectedPlayer.last_name}`
                      });
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Spielführer auswählen (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {getEligibleCaptains().map((player) => (
                      <SelectItem key={player.id} value={player.id}>
                        {player.first_name} {player.last_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                {/* Captain Contact Information for new team */}
                {newTeamData.captain_id && (() => {
                  const captain = availablePlayers.find(p => p.id === newTeamData.captain_id);
                  return captain ? (
                    <div className="mt-3 p-3 bg-muted/30 rounded-lg border">
                      <h5 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <Crown className="h-4 w-4 text-amber-600" />
                        Kontaktdaten Spielführer
                      </h5>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">Email:</span>
                          <span className="font-medium">{captain.email}</span>
                        </div>
                        {captain.phone && (
                          <div className="flex items-center justify-between">
                            <span className="text-muted-foreground">Telefon:</span>
                            <span className="font-medium">{captain.phone}</span>
                          </div>
                        )}
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">Alter:</span>
                          <span className="font-medium">{calculateAge(captain.birth_date)} Jahre</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-muted-foreground">Mitgliedschaft:</span>
                          <span className="font-medium">{captain.membership_type}</span>
                        </div>
                      </div>
                    </div>
                  ) : null;
                })()}
              </div>
              
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsNewTeamDialogOpen(false);
                    setNewTeamData({
                      name: "",
                      description: "",
                      league: "",
                      captain_id: "",
                      captain_name: ""
                    });
                  }}
                >
                  Abbrechen
                </Button>
                <Button 
                  onClick={async () => {
                    if (!newTeamData.name.trim() || !newTeamData.league.trim()) {
                      toast({
                        title: "Fehler",
                        description: "Name und Liga sind Pflichtfelder.",
                        variant: "destructive"
                      });
                      return;
                    }
                    
                    // INSERT team into DB
                    const { data, error } = await sbAny
                      .from('teams')
                      .insert([{
                        name: newTeamData.name.trim(),
                        description: newTeamData.description.trim(),
                        league: newTeamData.league.trim(),
                        captain_account_id: newTeamData.captain_id || null
                      }])
                      .select('id, name, description, league, captain_account_id')
                      .single();

                    if (error) {
                      console.error("Error creating team:", error);
                      toast({ title: "Fehler", description: "Mannschaft konnte nicht erstellt werden.", variant: "destructive" });
                      return;
                    }

                    const captain = newTeamData.captain_id
                      ? availablePlayers.find(p => p.id === newTeamData.captain_id)
                      : undefined;

                    const newTeam: Team = {
                      id: data.id,
                      name: data.name,
                      description: data.description || "",
                      league: data.league || "",
                      captain_id: data.captain_account_id || "",
                      captain_name: captain ? `${captain.first_name} ${captain.last_name}` : "Noch nicht zugewiesen",
                      players: []
                    };
                    
                    setTeams([...teams, newTeam]);
                    setIsNewTeamDialogOpen(false);
                    setNewTeamData({
                      name: "",
                      description: "",
                      league: "",
                      captain_id: "",
                      captain_name: ""
                    });
                    
                    toast({
                      title: "Mannschaft erstellt",
                      description: `${newTeam.name} wurde erfolgreich erstellt.`
                    });
                  }}
                  disabled={!newTeamData.name.trim() || !newTeamData.league.trim()}
                >
                  Mannschaft erstellen
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        {teams.map((team) => (
          <Card key={team.id}>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Trophy className="h-4 w-4" />
                {team.name}
              </CardTitle>
              <CardDescription>{team.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Spielführer:</span>
                  <div className="flex items-center gap-1">
                    <Crown className="h-3 w-3" />
                    <span className="text-sm">{team.captain_name}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Liga:</span>
                  <Badge variant="outline">{team.league}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Spieler:</span>
                  <Badge variant="secondary">{team.players.length}</Badge>
                </div>
                <div className="flex gap-2 mt-3">
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" onClick={() => setSelectedTeam(team)}>
                        <Edit className="h-3 w-3 mr-1" />
                        Bearbeiten
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl h-[85vh] max-h-[85vh] overflow-hidden flex flex-col">
                      <DialogHeader>
                        <DialogTitle>Mannschaft bearbeiten - {team.name}</DialogTitle>
                        <DialogDescription>
                          Bearbeiten Sie die Details und Spieler für {team.name}
                        </DialogDescription>
                      </DialogHeader>
                      
                      <Tabs defaultValue="details" className="w-full flex-1 flex flex-col overflow-hidden">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="details" className="flex items-center gap-2">
                            <Settings className="h-4 w-4" />
                            Team Details
                          </TabsTrigger>
                          <TabsTrigger value="current-players" className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            Aktuelle Spieler ({team.players.length})
                          </TabsTrigger>
                          <TabsTrigger value="add-players" className="flex items-center gap-2">
                            <UserPlus className="h-4 w-4" />
                            Spieler hinzufügen
                          </TabsTrigger>
                        </TabsList>

                        {/* Team Details Tab */}
                        <TabsContent value="details" className="mt-6 flex-1 overflow-y-auto">
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="teamName">Name</Label>
                                <Input
                                  id="teamName"
                                  defaultValue={team.name}
                                  onBlur={async (e) => {
                                    const value = e.target.value.trim();
                                    if (value && value !== team.name) {
                                      const { error } = await sbAny.from('teams').update({ name: value }).eq('id', team.id);
                                      if (!error) {
                                        setTeams(teams.map(t => t.id === team.id ? { ...t, name: value } : t));
                                        toast({ title: "Gespeichert", description: "Name aktualisiert." });
                                      }
                                    }
                                  }}
                                />
                              </div>
                              <div>
                                <Label htmlFor="teamLeague">Liga</Label>
                                <Input
                                  id="teamLeague"
                                  defaultValue={team.league}
                                  onBlur={async (e) => {
                                    const value = e.target.value.trim();
                                    if (value !== team.league) {
                                      const { error } = await sbAny.from('teams').update({ league: value }).eq('id', team.id);
                                      if (!error) {
                                        setTeams(teams.map(t => t.id === team.id ? { ...t, league: value } : t));
                                        toast({ title: "Gespeichert", description: "Liga aktualisiert." });
                                      }
                                    }
                                  }}
                                />
                              </div>
                            </div>
                            <div>
                              <Label htmlFor="teamDescription">Beschreibung</Label>
                              <Input
                                id="teamDescription"
                                defaultValue={team.description}
                                onBlur={async (e) => {
                                  const value = e.target.value;
                                  if (value !== team.description) {
                                    const { error } = await sbAny.from('teams').update({ description: value }).eq('id', team.id);
                                    if (!error) {
                                      setTeams(teams.map(t => t.id === team.id ? { ...t, description: value } : t));
                                      toast({ title: "Gespeichert", description: "Beschreibung aktualisiert." });
                                    }
                                  }
                                }}
                              />
                            </div>
                            <div>
                              <Label htmlFor="teamCaptain">Spielführer</Label>
                              <Select
                                value={team.captain_id}
                                onValueChange={async (value) => {
                                  const selectedPlayer = availablePlayers.find(p => p.id === value);
                                  const { error } = await sbAny.from('teams').update({ captain_account_id: value || null }).eq('id', team.id);
                                  if (!error) {
                                    const updatedTeams = teams.map(t => 
                                      t.id === team.id 
                                        ? { ...t, captain_id: value, captain_name: selectedPlayer ? `${selectedPlayer.first_name} ${selectedPlayer.last_name}` : "Noch nicht zugewiesen" }
                                        : t
                                    );
                                    setTeams(updatedTeams);
                                    if (selectedTeam?.id === team.id) {
                                      setSelectedTeam({
                                        ...selectedTeam!,
                                        captain_id: value,
                                        captain_name: selectedPlayer ? `${selectedPlayer.first_name} ${selectedPlayer.last_name}` : "Noch nicht zugewiesen"
                                      });
                                    }
                                    toast({ title: "Gespeichert", description: "Spielführer aktualisiert." });
                                  }
                                }}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Spielführer auswählen" />
                                </SelectTrigger>
                                <SelectContent>
                                  {getEligibleCaptains().map((player) => (
                                    <SelectItem key={player.id} value={player.id}>
                                      {player.first_name} {player.last_name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              
                              {/* Captain Contact Information */}
                              {team.captain_id && (() => {
                                const captain = availablePlayers.find(p => p.id === team.captain_id);
                                return captain ? (
                                  <div className="mt-3 p-3 bg-muted/30 rounded-lg border">
                                    <h5 className="text-sm font-medium mb-2 flex items-center gap-2">
                                      <Crown className="h-4 w-4 text-amber-600" />
                                      Kontaktdaten Spielführer
                                    </h5>
                                    <div className="space-y-2 text-sm">
                                      <div className="flex items-center justify-between">
                                        <span className="text-muted-foreground">Email:</span>
                                        <span className="font-medium">{captain.email}</span>
                                      </div>
                                      {captain.phone && (
                                        <div className="flex items-center justify-between">
                                          <span className="text-muted-foreground">Telefon:</span>
                                          <span className="font-medium">{captain.phone}</span>
                                        </div>
                                      )}
                                      <div className="flex items-center justify-between">
                                        <span className="text-muted-foreground">Alter:</span>
                                        <span className="font-medium">{calculateAge(captain.birth_date)} Jahre</span>
                                      </div>
                                      <div className="flex items-center justify-between">
                                        <span className="text-muted-foreground">Mitgliedschaft:</span>
                                        <span className="font-medium">{captain.membership_type}</span>
                                      </div>
                                    </div>
                                  </div>
                                ) : null;
                              })()}
                            </div>
                          </div>
                        </TabsContent>

                        {/* Current Players Tab */}
                        <TabsContent value="current-players" className="mt-6 flex-1 overflow-y-auto">
                          <div className="space-y-4">
                            {team.players.length === 0 ? (
                              <div className="text-center py-8">
                                <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                <p className="text-muted-foreground">Keine Spieler zugewiesen</p>
                                <p className="text-sm text-muted-foreground">Fügen Sie Spieler über den "Spieler hinzufügen" Tab hinzu</p>
                              </div>
                            ) : (
                              <div className="grid gap-3 h-96 overflow-y-auto">
                                {team.players.map((player) => (
                                  <div key={player.id} className="flex items-center justify-between p-3 border rounded-lg bg-muted/20">
                                    <div className="flex items-center gap-3">
                                      <UserCheck className="h-5 w-5 text-green-600" />
                                      <div>
                                        <div className="font-medium">{player.first_name} {player.last_name}</div>
                                        <div className="text-sm text-muted-foreground">
                                          Alter: {calculateAge(player.birth_date)} Jahre • {player.membership_type}
                                        </div>
                                      </div>
                                    </div>
                                    <Button 
                                      variant="outline" 
                                      size="sm"
                                      onClick={() => removePlayerFromTeam(player.id)}
                                    >
                                      Entfernen
                                    </Button>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </TabsContent>

                        {/* Add Players Tab */}
                        <TabsContent value="add-players" className="mt-6 overflow-y-auto">
                          <div className="space-y-4">
                            {/* Filter Section */}
                            <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                              <h4 className="font-medium flex items-center gap-2">
                                <UserPlus className="h-4 w-4" />
                                Spieler Filter
                              </h4>
                              <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                                <div>
                                  <Label htmlFor="ageFrom" className="text-xs">Alter von:</Label>
                                  <Input
                                    id="ageFrom"
                                    type="number"
                                    placeholder="Min"
                                    value={ageFrom}
                                    onChange={(e) => setAgeFrom(e.target.value)}
                                    className="h-9"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="ageTo" className="text-xs">Alter bis:</Label>
                                  <Input
                                    id="ageTo"
                                    type="number"
                                    placeholder="Max"
                                    value={ageTo}
                                    onChange={(e) => setAgeTo(e.target.value)}
                                    className="h-9"
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="gender" className="text-xs">Geschlecht:</Label>
                                  <Select value={genderFilter} onValueChange={setGenderFilter}>
                                    <SelectTrigger className="h-9">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="all">Alle</SelectItem>
                                      <SelectItem value="male">Männlich</SelectItem>
                                      <SelectItem value="female">Weiblich</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label htmlFor="nameFilter" className="text-xs">Name:</Label>
                                  <Input
                                    id="nameFilter"
                                    placeholder="Spieler suchen..."
                                    value={nameFilter}
                                    onChange={(e) => setNameFilter(e.target.value)}
                                    className="h-9"
                                  />
                                </div>
                              </div>
                              <Button variant="outline" size="sm" onClick={resetFilters} className="w-full">
                                Filter zurücksetzen
                              </Button>
                            </div>

                            {/* Available Players List */}
                            <div>
                              <h4 className="font-medium mb-3">
                                Verfügbare Spieler ({filteredPlayers.length})
                              </h4>
                              <div className="space-y-2 h-64 overflow-y-auto">
                                {loading ? (
                                  <div className="text-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                                    <p className="text-sm text-muted-foreground mt-2">Lade Spieler...</p>
                                  </div>
                                ) : filteredPlayers.length === 0 ? (
                                  <div className="text-center py-8">
                                    <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                                    <p className="text-muted-foreground">Keine Spieler gefunden</p>
                                    <p className="text-sm text-muted-foreground">Versuchen Sie andere Filter-Einstellungen</p>
                                  </div>
                                ) : (
                                  filteredPlayers.map((player) => (
                                    <div key={player.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                                      <div className="flex items-center gap-3">
                                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                          <span className="text-sm font-medium">{player.first_name[0]}{player.last_name[0]}</span>
                                        </div>
                                        <div>
                                          <div className="font-medium">{player.first_name} {player.last_name}</div>
                                          <div className="text-sm text-muted-foreground">
                                            Alter: {calculateAge(player.birth_date)} Jahre • {player.membership_type}
                                          </div>
                                        </div>
                                      </div>
                                      <Button 
                                        size="sm"
                                        onClick={() => addPlayerToTeam(player)}
                                      >
                                        Hinzufügen
                                      </Button>
                                    </div>
                                  ))
                                )}
                              </div>
                            </div>
                          </div>
                        </TabsContent>
                      </Tabs>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default TeamManagement;
