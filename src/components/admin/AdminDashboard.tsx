import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, Calendar, Activity, Settings, TrendingUp, Trophy, ChevronRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface DashboardStats {
  totalMembers: number;
  upcomingBookings: Array<{
    id: string;
    court_number: number;
    player_name: string;
    start_time: string;
    booking_date: string;
  }>;
  recentActivity: Array<{
    id: string;
    action: string;
    timestamp: string;
    user: string;
  }>;
  activeModules: Array<{
    name: string;
    status: 'active' | 'inactive';
    description: string;
  }>;
}

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalMembers: 0,
    upcomingBookings: [],
    recentActivity: [],
    activeModules: []
  });
  const [loading, setLoading] = useState(true);

  // Check if we're in preview mode
  const isPreview = window.location.hostname.includes('lovable') || 
                   window.location.hostname.includes('localhost') ||
                   window.location.hostname.includes('preview');

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // In preview mode, use mock data only
        if (isPreview) {
          const mockActivity = [
            { id: '1', action: 'Neues Mitglied registriert', timestamp: new Date().toISOString(), user: 'System' },
            { id: '2', action: 'Platz 3 gewartet', timestamp: new Date().toISOString(), user: 'Admin' },
            { id: '3', action: 'Buchung storniert', timestamp: new Date().toISOString(), user: 'Mitglied' },
            { id: '4', action: 'System-Backup erstellt', timestamp: new Date().toISOString(), user: 'System' },
            { id: '5', action: 'Mitgliedsbeitrag aktualisiert', timestamp: new Date().toISOString(), user: 'Admin' }
          ];

          const mockModules = [
            { name: 'Buchungssystem', status: 'active' as const, description: 'Online-Platzbuchung' },
            { name: 'Mitgliederverwaltung', status: 'active' as const, description: 'Verwaltung aller Mitglieder' },
            { name: 'Arbeitsdienst', status: 'active' as const, description: 'Arbeitszeit-Erfassung' },
            { name: 'Zahlungssystem', status: 'inactive' as const, description: 'Automatische Beitragsverwaltung' }
          ];

          const mockBookings = [
            { id: '1', court_number: 1, player_name: 'Max Mustermann', start_time: '10:00', booking_date: '2025-08-08' },
            { id: '2', court_number: 2, player_name: 'Anna Schmidt', start_time: '14:30', booking_date: '2025-08-08' },
            { id: '3', court_number: 3, player_name: 'Peter Weber', start_time: '16:00', booking_date: '2025-08-09' }
          ];

          setStats({
            totalMembers: 42,
            upcomingBookings: mockBookings,
            recentActivity: mockActivity,
            activeModules: mockModules
          });
          setLoading(false);
          return;
        }

        // Fetch total members count
        const { count: memberCount } = await supabase
          .from('members')
          .select('*', { count: 'exact', head: true });

        // Fetch upcoming bookings with court information
        const { data: bookings } = await supabase
          .from('bookings')
          .select(`
            id,
            player_name,
            start_time,
            booking_date,
            courts!inner(number)
          `)
          .gte('booking_date', new Date().toISOString().split('T')[0])
          .order('booking_date', { ascending: true })
          .order('start_time', { ascending: true })
          .limit(5);

        const formattedBookings = bookings?.map(booking => ({
          id: booking.id,
          court_number: (booking.courts as any).number,
          player_name: booking.player_name,
          start_time: booking.start_time,
          booking_date: booking.booking_date
        })) || [];

        // Mock data for system activity (would come from audit logs)
        const mockActivity = [
          { id: '1', action: 'Neues Mitglied registriert', timestamp: new Date().toISOString(), user: 'System' },
          { id: '2', action: 'Platz 3 gewartet', timestamp: new Date().toISOString(), user: 'Admin' },
          { id: '3', action: 'Buchung storniert', timestamp: new Date().toISOString(), user: 'Mitglied' },
          { id: '4', action: 'System-Backup erstellt', timestamp: new Date().toISOString(), user: 'System' },
          { id: '5', action: 'Mitgliedsbeitrag aktualisiert', timestamp: new Date().toISOString(), user: 'Admin' }
        ];

        // Mock data for active modules
        const mockModules = [
          { name: 'Buchungssystem', status: 'active' as const, description: 'Online-Platzbuchung' },
          { name: 'Mitgliederverwaltung', status: 'active' as const, description: 'Verwaltung aller Mitglieder' },
          { name: 'Arbeitsdienst', status: 'active' as const, description: 'Arbeitszeit-Erfassung' },
          { name: 'Zahlungssystem', status: 'inactive' as const, description: 'Automatische Beitragsverwaltung' }
        ];

        setStats({
          totalMembers: memberCount || 0,
          upcomingBookings: formattedBookings,
          recentActivity: mockActivity,
          activeModules: mockModules
        });

      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [isPreview]);

  const formatTime = (timeString: string) => {
    return timeString.slice(0, 5); // Format HH:MM
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('de-DE', {
      day: '2-digit',
      month: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-foreground">Admin Dashboard</h1>
        <p className="text-muted-foreground">Übersicht über alle wichtigen Vereinsdaten</p>
      </div>

      {/* Upcoming Home Games - Compact Section */}
      <Card className="shadow-card-soft border-border">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-tennis-green" />
              <CardTitle className="text-lg">Anstehende Heimspiele</CardTitle>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => navigate('/admin/management?tab=schedule')}
              className="text-tennis-green hover:text-tennis-green/80"
            >
              Alle anzeigen
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
          <CardDescription>Nächste Mannschaftsspiele zu Hause</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50 border-green-200">
              <div>
                <div className="font-medium text-sm">Herren I vs. TC Musterstadt</div>
                <div className="text-xs text-muted-foreground">So, 15. Sep • 10:00</div>
              </div>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="text-xs">Platz 1-2</Badge>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg bg-green-50 border-green-200">
              <div>
                <div className="font-medium text-sm">Damen I vs. SV Beispielort</div>
                <div className="text-xs text-muted-foreground">Sa, 21. Sep • 14:00</div>
              </div>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="text-xs">Platz 3-4</Badge>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg bg-blue-50 border-blue-200">
              <div>
                <div className="font-medium text-sm">Damen Ü40 vs. TC Stadtpark</div>
                <div className="text-xs text-muted-foreground">So, 29. Sep • 11:00</div>
              </div>
              <div className="flex items-center gap-1">
                <Badge variant="outline" className="text-xs">Platz 5-6</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="shadow-card-soft border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtmitglieder</CardTitle>
            <Users className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMembers}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              Aktive Mitglieder
            </p>
          </CardContent>
        </Card>

        <Card className="shadow-card-soft border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Laufende Buchungen</CardTitle>
            <Calendar className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingBookings.length}</div>
            <p className="text-xs text-muted-foreground">Nächste 5 Termine</p>
          </CardContent>
        </Card>

        <Card className="shadow-card-soft border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System-Aktivität</CardTitle>
            <Activity className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentActivity.length}</div>
            <p className="text-xs text-muted-foreground">Letzte Aktionen</p>
          </CardContent>
        </Card>

        <Card className="shadow-card-soft border-border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Module</CardTitle>
            <Settings className="h-4 w-4 text-tennis-green" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.activeModules.filter(m => m.status === 'active').length}/{stats.activeModules.length}
            </div>
            <p className="text-xs text-muted-foreground">Module aktiviert</p>
          </CardContent>
        </Card>
      </div>

      {/* Detail Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Bookings */}
        <Card className="shadow-card-soft">
          <CardHeader>
            <CardTitle>Nächste Buchungen</CardTitle>
            <CardDescription>Die nächsten 5 Platzbuchungen</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.upcomingBookings.length > 0 ? (
                stats.upcomingBookings.map((booking) => (
                  <div key={booking.id} className="flex items-center justify-between p-3 bg-court-white rounded-lg">
                    <div>
                      <div className="font-medium">Platz {booking.court_number}</div>
                      <div className="text-sm text-muted-foreground">{booking.player_name}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{formatTime(booking.start_time)}</div>
                      <div className="text-xs text-muted-foreground">{formatDate(booking.booking_date)}</div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4">Keine anstehenden Buchungen</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* System Activity */}
        <Card className="shadow-card-soft">
          <CardHeader>
            <CardTitle>System-Log</CardTitle>
            <CardDescription>Letzte 5 Systemaktivitäten</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-court-white rounded-lg">
                  <div className="w-2 h-2 rounded-full bg-tennis-green mt-2 flex-shrink-0"></div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{activity.action}</div>
                    <div className="text-xs text-muted-foreground">
                      {activity.user} • {new Date(activity.timestamp).toLocaleString('de-DE')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Modules */}
      <Card className="shadow-card-soft">
        <CardHeader>
          <CardTitle>Aktive Module</CardTitle>
          <CardDescription>Übersicht über aktivierte Vereinsfunktionen</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {stats.activeModules.map((module, index) => (
              <div 
                key={index} 
                className={`p-4 rounded-lg border ${
                  module.status === 'active' 
                    ? 'bg-tennis-green/5 border-tennis-green/20' 
                    : 'bg-muted/50 border-border'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-sm">{module.name}</div>
                  <div className={`w-2 h-2 rounded-full ${
                    module.status === 'active' ? 'bg-tennis-green' : 'bg-muted-foreground'
                  }`}></div>
                </div>
                <p className="text-xs text-muted-foreground">{module.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDashboard;