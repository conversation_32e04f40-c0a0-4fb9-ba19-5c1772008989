import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Clock, BarChart3 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import WorkServiceDashboard from "./WorkServiceDashboard";
import ActivityRegistrationManagement from "./ActivityRegistrationManagement";
import ActivityRequestManagement from "./ActivityRequestManagement";

interface Activity {
  id: string;
  name: string;
  description?: string;
  hourly_rate?: number;
}

interface WorkLog {
  id: string;
  activity_id: string;
  date: string;
  duration_hours: number;
  activities: {
    name: string;
  };
}

interface Member {
  id: string;
  first_name: string;
  last_name: string;
}

const WorkServiceManagement = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [workLogs, setWorkLogs] = useState<WorkLog[]>([]);
  const [members, setMembers] = useState<Member[]>([]);
  const [isActivityDialogOpen, setIsActivityDialogOpen] = useState(false);
  const [isWorkLogDialogOpen, setIsWorkLogDialogOpen] = useState(false);
  const [editingActivity, setEditingActivity] = useState<Activity | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const [activityForm, setActivityForm] = useState({
    name: "",
    description: "",
    hourly_rate: ""
  });

  const [workLogForm, setWorkLogForm] = useState({
    activity_id: "",
    date: "",
    duration_hours: "",
    assigned_members: [] as string[]
  });

  useEffect(() => {
    fetchActivities();
    fetchWorkLogs();
    fetchMembers();
  }, []);

  const fetchActivities = async () => {
    try {
      const { data, error } = await supabase
        .from("activities")
        .select("*")
        .order("name", { ascending: true });

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch activities",
        variant: "destructive",
      });
    }
  };

  const fetchWorkLogs = async () => {
    try {
      const { data, error } = await supabase
        .from("work_logs")
        .select(`
          *,
          activities (name)
        `)
        .order("date", { ascending: false });

      if (error) throw error;
      setWorkLogs(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch work logs",
        variant: "destructive",
      });
    }
  };

  const fetchMembers = async () => {
    try {
      const { data, error } = await supabase
        .from("members")
        .select("id, first_name, last_name")
        .order("last_name", { ascending: true });

      if (error) throw error;
      setMembers(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch members",
        variant: "destructive",
      });
    }
  };

  const handleActivitySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const activityData = {
        name: activityForm.name,
        description: activityForm.description || null,
        hourly_rate: activityForm.hourly_rate ? parseFloat(activityForm.hourly_rate) : null
      };

      if (editingActivity) {
        const { error } = await supabase
          .from("activities")
          .update(activityData)
          .eq("id", editingActivity.id);
        
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "Activity updated successfully",
        });
      } else {
        const { error } = await supabase
          .from("activities")
          .insert([activityData]);
        
        if (error) throw error;
        
        toast({
          title: "Success",
          description: "Activity created successfully",
        });
      }

      setIsActivityDialogOpen(false);
      setEditingActivity(null);
      resetActivityForm();
      fetchActivities();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save activity",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleWorkLogSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const { data: workLog, error: workLogError } = await supabase
        .from("work_logs")
        .insert([{
          activity_id: workLogForm.activity_id,
          date: workLogForm.date,
          duration_hours: parseFloat(workLogForm.duration_hours)
        }])
        .select()
        .single();

      if (workLogError) throw workLogError;

      // Insert member assignments
      if (workLogForm.assigned_members.length > 0) {
        const assignments = workLogForm.assigned_members.map(memberId => ({
          work_log_id: workLog.id,
          member_id: memberId
        }));

        const { error: assignmentError } = await supabase
          .from("work_log_assignments")
          .insert(assignments);

        if (assignmentError) throw assignmentError;
      }

      toast({
        title: "Success",
        description: "Work log created successfully",
      });

      setIsWorkLogDialogOpen(false);
      resetWorkLogForm();
      fetchWorkLogs();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save work log",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteActivity = async (activityId: string) => {
    if (!confirm("Are you sure you want to delete this activity?")) return;

    try {
      const { error } = await supabase
        .from("activities")
        .delete()
        .eq("id", activityId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Activity deleted successfully",
      });
      
      fetchActivities();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete activity",
        variant: "destructive",
      });
    }
  };

  const handleEditActivity = (activity: Activity) => {
    setEditingActivity(activity);
    setActivityForm({
      name: activity.name,
      description: activity.description || "",
      hourly_rate: activity.hourly_rate?.toString() || ""
    });
    setIsActivityDialogOpen(true);
  };

  const resetActivityForm = () => {
    setActivityForm({
      name: "",
      description: "",
      hourly_rate: ""
    });
  };

  const resetWorkLogForm = () => {
    setWorkLogForm({
      activity_id: "",
      date: "",
      duration_hours: "",
      assigned_members: []
    });
  };

  const openCreateActivityDialog = () => {
    setEditingActivity(null);
    resetActivityForm();
    setIsActivityDialogOpen(true);
  };

  const openCreateWorkLogDialog = () => {
    resetWorkLogForm();
    setIsWorkLogDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dashboard">Übersicht</TabsTrigger>
          <TabsTrigger value="activities">Tätigkeiten</TabsTrigger>
          <TabsTrigger value="registrations">Mitgliederverzeichnis</TabsTrigger>
          <TabsTrigger value="pending-requests">Anfragen</TabsTrigger>
          <TabsTrigger value="work-logs">Arbeitsstunden</TabsTrigger>
          <TabsTrigger value="reports">Berichte</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <WorkServiceDashboard />
        </TabsContent>

        <TabsContent value="registrations" className="space-y-4">
          <ActivityRegistrationManagement />
        </TabsContent>

        <TabsContent value="pending-requests" className="space-y-4">
          <ActivityRequestManagement />
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Tätigkeitsdefinitionen</h3>
            <div className="flex gap-2">
              <Dialog open={isActivityDialogOpen} onOpenChange={setIsActivityDialogOpen}>
                <DialogTrigger asChild>
                  <Button onClick={openCreateActivityDialog} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Tätigkeit hinzufügen
                  </Button>
                </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingActivity ? "Edit Activity" : "Add New Activity"}
                  </DialogTitle>
                  <DialogDescription>
                    Define work activities for club maintenance and operations.
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleActivitySubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="activity_name">Activity Name</Label>
                    <Input
                      id="activity_name"
                      value={activityForm.name}
                      onChange={(e) => setActivityForm({ ...activityForm, name: e.target.value })}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="activity_description">Description</Label>
                    <Textarea
                      id="activity_description"
                      value={activityForm.description}
                      onChange={(e) => setActivityForm({ ...activityForm, description: e.target.value })}
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="hourly_rate">Hourly Rate (€)</Label>
                    <Input
                      id="hourly_rate"
                      type="number"
                      step="0.01"
                      value={activityForm.hourly_rate}
                      onChange={(e) => setActivityForm({ ...activityForm, hourly_rate: e.target.value })}
                      placeholder="Optional"
                    />
                  </div>

                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsActivityDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? "Saving..." : editingActivity ? "Update" : "Create"}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Beschreibung</TableHead>
                    <TableHead>Stundensatz</TableHead>
                    <TableHead className="text-right">Aktionen</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell className="font-medium">{activity.name}</TableCell>
                      <TableCell>{activity.description || "—"}</TableCell>
                      <TableCell>
                        {activity.hourly_rate ? `€${activity.hourly_rate}` : "—"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditActivity(activity)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleDeleteActivity(activity.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="work-logs" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Arbeitsstunden erfassen</h3>
            <Dialog open={isWorkLogDialogOpen} onOpenChange={setIsWorkLogDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={openCreateWorkLogDialog} className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Arbeit erfassen
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Log Work Hours</DialogTitle>
                  <DialogDescription>
                    Record work hours for club activities and assign to members.
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleWorkLogSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="work_activity">Activity</Label>
                    <Select
                      value={workLogForm.activity_id}
                      onValueChange={(value) => setWorkLogForm({ ...workLogForm, activity_id: value })}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select activity" />
                      </SelectTrigger>
                      <SelectContent>
                        {activities.map((activity) => (
                          <SelectItem key={activity.id} value={activity.id}>
                            {activity.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="work_date">Date</Label>
                    <Input
                      id="work_date"
                      type="date"
                      value={workLogForm.date}
                      onChange={(e) => setWorkLogForm({ ...workLogForm, date: e.target.value })}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="duration">Duration (Hours)</Label>
                    <Input
                      id="duration"
                      type="number"
                      step="0.5"
                      value={workLogForm.duration_hours}
                      onChange={(e) => setWorkLogForm({ ...workLogForm, duration_hours: e.target.value })}
                      required
                    />
                  </div>

                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setIsWorkLogDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading}>
                      {loading ? "Saving..." : "Log Work"}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Datum</TableHead>
                    <TableHead>Tätigkeit</TableHead>
                    <TableHead>Dauer</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {workLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>{new Date(log.date).toLocaleDateString()}</TableCell>
                      <TableCell>{log.activities.name}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{log.duration_hours}h</Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Arbeitsstunden Zusammenfassung
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detaillierte Berichte und Analysen zu Arbeitsstunden sind hier verfügbar.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WorkServiceManagement;