import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Trash2, Plus, Clock, CalendarIcon, Ban, Settings, Edit, Save, X } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface PlayTime {
  id?: string;
  start_time: string;
  end_time: string;
  days_of_week: number[];
}

interface CourtBlock {
  id: string;
  court_id: string;
  start_date: string;
  end_date: string;
  start_time: string | null;
  end_time: string | null;
  reason: string;
  created_at: string;
  updated_at: string;
}

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
  court_group?: string;
  surface_type?: string;
  availability: PlayTime[];
  blocks?: CourtBlock[];
}

const DAYS = [
  { value: 0, label: "So" },
  { value: 1, label: "Mo" },
  { value: 2, label: "Di" },
  { value: 3, label: "Mi" },
  { value: 4, label: "Do" },
  { value: 5, label: "Fr" },
  { value: 6, label: "Sa" },
];

const CourtConfigurationImproved = () => {
  const [courts, setCourts] = useState<Court[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>("court-1");
  const [defaultStandardTimes, setDefaultStandardTimes] = useState({
    start_time: "08:00",
    end_time: "22:00",
    days_of_week: [1, 2, 3, 4, 5, 6, 0]
  });
  const { toast } = useToast();

  useEffect(() => {
    loadCourtsData();
  }, []);

  const loadCourtsData = async () => {
    try {
      console.log('CourtConfigurationImproved: Starting to load courts data...');
      const { data: defaultTimesSettings } = await supabase
        .from("system_settings")
        .select("*")
        .eq("key", "default_standard_times")
        .maybeSingle();

      console.log('CourtConfigurationImproved: defaultTimesSettings result:', defaultTimesSettings);
      if (defaultTimesSettings) {
        setDefaultStandardTimes(JSON.parse(defaultTimesSettings.value as string));
      }

      const { data: courtsData } = await supabase
        .from("courts")
        .select(`
          *,
          court_availability (*),
          court_blocks (*)
        `)
        .order("number");

      console.log('CourtConfigurationImproved: courtsData result:', courtsData);
      if (courtsData) {
        const courtsWithData = courtsData.map(court => ({
          ...court,
          availability: court.court_availability || [],
          blocks: court.court_blocks || []
        }));
        setCourts(courtsWithData);
        
        if (courtsWithData.length > 0) {
          setActiveTab(`court-${courtsWithData[0].number}`);
        }
      }
      console.log('CourtConfigurationImproved: Data loaded successfully, courts:', courtsData?.length);
    } catch (error) {
      console.error('CourtConfigurationImproved: Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load court configuration",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('CourtConfigurationImproved: Loading finished');
    }
  };

  // Quick Actions Component
  const QuickActions = ({ court }: { court: Court }) => {
    const [editing, setEditing] = useState(false);
    const [courtGroup, setCourtGroup] = useState(court.court_group || "");
    const [surfaceType, setSurfaceType] = useState(court.surface_type || "");

    const saveDetails = async () => {
      try {
        await supabase
          .from("courts")
          .update({ court_group: courtGroup, surface_type: surfaceType })
          .eq("id", court.id);

        setCourts(courts.map(c => 
          c.id === court.id 
            ? { ...c, court_group: courtGroup, surface_type: surfaceType }
            : c
        ));
        
        setEditing(false);
        toast({
          title: "Erfolgreich",
          description: "Platz-Details wurden aktualisiert"
        });
      } catch (error) {
        toast({
          title: "Fehler", 
          description: "Fehler beim Speichern",
          variant: "destructive"
        });
      }
    };

    const toggleLock = async (locked: boolean) => {
      let reason = null;
      if (locked) {
        reason = prompt("Sperrgrund eingeben:");
        if (!reason) return;
      }

      try {
        await supabase
          .from("courts")
          .update({ locked, lock_reason: reason })
          .eq("id", court.id);

        setCourts(courts.map(c => 
          c.id === court.id 
            ? { ...c, locked, lock_reason: reason }
            : c
        ));

        toast({
          title: "Erfolgreich",
          description: `Platz ${locked ? "gesperrt" : "entsperrt"}`
        });
      } catch (error) {
        toast({
          title: "Fehler",
          description: "Fehler beim Ändern des Sperrstatus",
          variant: "destructive"
        });
      }
    };

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <CardTitle className="text-lg">Platz {court.number}</CardTitle>
              <div className="flex items-center gap-2">
                <Switch
                  checked={court.locked}
                  onCheckedChange={toggleLock}
                  className={cn(
                    "data-[state=checked]:bg-destructive data-[state=unchecked]:bg-green-500"
                  )}
                />
                <Label className="text-sm">
                  {court.locked ? "Gesperrt" : "Aktiv"}
                </Label>
              </div>
              {court.locked && (
                <Badge variant="destructive" className="text-xs">
                  {court.lock_reason}
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {editing ? (
                <>
                  <Button size="sm" onClick={saveDetails}>
                    <Save className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setEditing(false)}>
                    <X className="h-4 w-4" />
                  </Button>
                </>
              ) : (
                <Button size="sm" variant="outline" onClick={() => setEditing(true)}>
                  <Edit className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        {editing && (
          <CardContent className="pt-0">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm">Platzgruppe</Label>
                <Select value={courtGroup} onValueChange={setCourtGroup}>
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Auswählen..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Keine</SelectItem>
                    <SelectItem value="Indoor">Indoor</SelectItem>
                    <SelectItem value="Outdoor">Outdoor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-sm">Bodenbelag</Label>
                <Select value={surfaceType} onValueChange={setSurfaceType}>
                  <SelectTrigger className="h-8">
                    <SelectValue placeholder="Auswählen..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Keine Angabe</SelectItem>
                    <SelectItem value="Rasen">Rasen</SelectItem>
                    <SelectItem value="Sandplatz">Sandplatz</SelectItem>
                    <SelectItem value="Keramiksand">Keramiksand</SelectItem>
                    <SelectItem value="Hartplatz">Hartplatz</SelectItem>
                    <SelectItem value="Teppich">Teppich</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    );
  };

  // Play Times Section
  const PlayTimesSection = ({ court }: { court: Court }) => {
    const [showForm, setShowForm] = useState(false);
    const [startTime, setStartTime] = useState("09:00");
    const [endTime, setEndTime] = useState("17:00");
    const [selectedDays, setSelectedDays] = useState<number[]>([1, 2, 3, 4, 5]);

    const addPlayTime = async () => {
      if (selectedDays.length === 0) {
        toast({
          title: "Fehler",
          description: "Bitte mindestens einen Tag auswählen",
          variant: "destructive"
        });
        return;
      }

      try {
        const { data } = await supabase
          .from("court_availability")
          .insert({
            court_id: court.id,
            start_time: startTime,
            end_time: endTime,
            days_of_week: selectedDays
          })
          .select()
          .single();

        if (data) {
          setCourts(courts.map(c => 
            c.id === court.id 
              ? { ...c, availability: [...c.availability, data] }
              : c
          ));
          
          setShowForm(false);
          setStartTime("09:00");
          setEndTime("17:00");
          setSelectedDays([1, 2, 3, 4, 5]);
          
          toast({
            title: "Erfolgreich",
            description: "Spielzeit hinzugefügt"
          });
        }
      } catch (error) {
        toast({
          title: "Fehler",
          description: "Fehler beim Hinzufügen der Spielzeit",
          variant: "destructive"
        });
      }
    };

    const removePlayTime = async (blockId: string) => {
      try {
        await supabase
          .from("court_availability")
          .delete()
          .eq("id", blockId);

        setCourts(courts.map(c => 
          c.id === court.id 
            ? { ...c, availability: c.availability.filter(b => b.id !== blockId) }
            : c
        ));
        
        toast({
          title: "Erfolgreich",
          description: "Spielzeit entfernt"
        });
      } catch (error) {
        toast({
          title: "Fehler",
          description: "Fehler beim Entfernen der Spielzeit",
          variant: "destructive"
        });
      }
    };

    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Spielzeiten
            </CardTitle>
            <Button size="sm" variant="outline" onClick={() => setShowForm(!showForm)}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {court.availability.length === 0 ? (
            <p className="text-sm text-muted-foreground">Keine Spielzeiten definiert</p>
          ) : (
            court.availability.map((block) => (
              <div key={block.id} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-3">
                  <span className="font-medium text-sm">
                    {block.start_time} - {block.end_time}
                  </span>
                  <div className="flex gap-1">
                    {block.days_of_week.map(day => (
                      <Badge key={day} variant="outline" className="text-xs px-1">
                        {DAYS.find(d => d.value === day)?.label}
                      </Badge>
                    ))}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removePlayTime(block.id!)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))
          )}
          
          {showForm && (
            <div className="space-y-3 p-3 border rounded bg-muted/30">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm">Von</Label>
                  <Input
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-sm">Bis</Label>
                  <Input
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-sm">Wochentage</Label>
                <div className="flex gap-1 mt-1">
                  {DAYS.map(day => (
                    <Badge
                      key={day.value}
                      variant={selectedDays.includes(day.value) ? "default" : "outline"}
                      className="cursor-pointer text-xs px-2"
                      onClick={() => {
                        if (selectedDays.includes(day.value)) {
                          setSelectedDays(selectedDays.filter(d => d !== day.value));
                        } else {
                          setSelectedDays([...selectedDays, day.value]);
                        }
                      }}
                    >
                      {day.label}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button size="sm" onClick={addPlayTime}>
                  Hinzufügen
                </Button>
                <Button size="sm" variant="outline" onClick={() => setShowForm(false)}>
                  Abbrechen
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Improved Blocks Section with Start/End Date
  const BlocksSection = ({ court }: { court: Court }) => {
    const [showForm, setShowForm] = useState(false);
    const [startDate, setStartDate] = useState<Date | undefined>(undefined);
    const [endDate, setEndDate] = useState<Date | undefined>(undefined);
    const [startTime, setStartTime] = useState("");
    const [endTime, setEndTime] = useState("");
    const [reason, setReason] = useState("");

    const addBlock = async () => {
      if (!startDate || !endDate || !reason) {
        toast({
          title: "Fehler",
          description: "Bitte alle Pflichtfelder ausfüllen",
          variant: "destructive"
        });
        return;
      }

      try {
        const blockData = {
          court_id: court.id,
          start_date: format(startDate, "yyyy-MM-dd"),
          end_date: format(endDate, "yyyy-MM-dd"),
          start_time: startTime || null,
          end_time: endTime || null,
          reason
        };

        const { data } = await supabase
          .from("court_blocks")
          .insert(blockData)
          .select()
          .single();

        if (data) {
          setCourts(courts.map(c => 
            c.id === court.id 
              ? { ...c, blocks: [...(c.blocks || []), data] }
              : c
          ));
          
          setShowForm(false);
          setStartDate(undefined);
          setEndDate(undefined);
          setStartTime("");
          setEndTime("");
          setReason("");
          
          toast({
            title: "Erfolgreich",
            description: "Blockierung hinzugefügt"
          });
        }
      } catch (error) {
        toast({
          title: "Fehler",
          description: "Fehler beim Hinzufügen der Blockierung",
          variant: "destructive"
        });
      }
    };

    const removeBlock = async (blockId: string) => {
      try {
        await supabase
          .from("court_blocks")
          .delete()
          .eq("id", blockId);

        setCourts(courts.map(c => 
          c.id === court.id 
            ? { ...c, blocks: (c.blocks || []).filter(b => b.id !== blockId) }
            : c
        ));
        
        toast({
          title: "Erfolgreich",
          description: "Blockierung entfernt"
        });
      } catch (error) {
        toast({
          title: "Fehler",
          description: "Fehler beim Entfernen der Blockierung",
          variant: "destructive"
        });
      }
    };

    return (
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <Ban className="h-4 w-4" />
              Blockierungen
            </CardTitle>
            <Button size="sm" variant="outline" onClick={() => setShowForm(!showForm)}>
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {!court.blocks || court.blocks.length === 0 ? (
            <p className="text-sm text-muted-foreground">Keine Blockierungen</p>
          ) : (
            court.blocks.map((block) => (
              <div key={block.id} className="flex items-center justify-between p-2 border rounded bg-destructive/5">
                <div className="flex items-center gap-3">
                  <div>
                    <span className="font-medium text-sm">
                      {format(new Date(block.start_date), "dd.MM.yyyy")}
                      {block.start_date !== block.end_date && 
                        ` - ${format(new Date(block.end_date), "dd.MM.yyyy")}`
                      }
                    </span>
                    {block.start_time && block.end_time && (
                      <span className="text-xs text-muted-foreground ml-2">
                        ({block.start_time} - {block.end_time})
                      </span>
                    )}
                  </div>
                  <Badge variant="destructive" className="text-xs">
                    {block.reason}
                  </Badge>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeBlock(block.id!)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            ))
          )}
          
          {showForm && (
            <div className="space-y-3 p-3 border rounded bg-destructive/5">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm">Von Datum *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "h-8 justify-start text-left font-normal",
                          !startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-3 w-3" />
                        {startDate ? format(startDate, "dd.MM.yyyy") : "Datum wählen"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={setStartDate}
                        initialFocus
                        className="pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div>
                  <Label className="text-sm">Bis Datum *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "h-8 justify-start text-left font-normal",
                          !endDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-3 w-3" />
                        {endDate ? format(endDate, "dd.MM.yyyy") : "Datum wählen"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                        className="pointer-events-auto"
                        disabled={(date) => startDate ? date < startDate : false}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm">Von Zeit (optional)</Label>
                  <Input
                    type="time"
                    value={startTime}
                    onChange={(e) => setStartTime(e.target.value)}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-sm">Bis Zeit (optional)</Label>
                  <Input
                    type="time"
                    value={endTime}
                    onChange={(e) => setEndTime(e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>
              
              <div>
                <Label className="text-sm">Grund *</Label>
                <Textarea
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="z.B. Wartungsarbeiten, Turnier..."
                  className="h-16"
                />
              </div>
              
              <div className="flex gap-2">
                <Button size="sm" onClick={addBlock} variant="destructive">
                  Blockieren
                </Button>
                <Button size="sm" variant="outline" onClick={() => setShowForm(false)}>
                  Abbrechen
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return <div>Laden...</div>;
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Platz-Konfiguration
          </CardTitle>
          <CardDescription>
            Kompakte Verwaltung von Spielzeiten, Blockierungen und Details
          </CardDescription>
        </CardHeader>
        <CardContent>
          {courts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Keine Plätze vorhanden</p>
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                {courts.map((court) => (
                  <TabsTrigger 
                    key={court.id} 
                    value={`court-${court.number}`}
                    className="flex items-center gap-2"
                  >
                    Platz {court.number}
                    {court.locked && (
                      <Badge variant="destructive" className="h-3 text-xs px-1">
                        !
                      </Badge>
                    )}
                  </TabsTrigger>
                ))}
              </TabsList>

              {courts.map((court) => (
                <TabsContent key={court.id} value={`court-${court.number}`} className="space-y-4">
                  <QuickActions court={court} />
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <PlayTimesSection court={court} />
                    <BlocksSection court={court} />
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CourtConfigurationImproved;