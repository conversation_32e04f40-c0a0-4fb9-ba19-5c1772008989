import { NavLink, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import {
  BarChart3,
  Users,
  MapPin,
  Calendar,
  Settings,
  ChevronLeft,
  ChevronRight,
  Trophy,
  MessageSquare,
  FolderOpen,
  DollarSign,
  Hammer
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";

interface SidebarProps {
  className?: string;
}

const sidebarItems = [
  {
    title: "Dashboard",
    href: "/admin",
    icon: BarChart3,
    description: "Übersicht und Statistiken",
    hasContent: true
  },
  {
    title: "Verein",
    href: "/admin/management",
    icon: FolderOpen,
    description: "Vereinsverwaltung",
    hasContent: true
  },
  {
    title: "Benutzer",
    href: "/admin/members",
    icon: Users,
    description: "Benutzerverwaltung",
    hasContent: true
  },
  {
    title: "<PERSON>l<PERSON><PERSON>",
    href: "/admin/courts",
    icon: MapPin,
    description: "Platzverwaltung",
    hasContent: true
  },
  {
    title: "Buchungssystem",
    href: "/admin/bookings",
    icon: Calendar,
    description: "Buchungen verwalten",
    hasContent: true
  },
  {
    title: "Turniere & Events",
    href: "/admin/tournaments",
    icon: Trophy,
    description: "Turniere und Events verwalten",
    hasContent: false
  },
  {
    title: "Kommunikation",
    href: "/admin/communication",
    icon: MessageSquare,
    description: "Mitgliederkommunikation",
    hasContent: false
  },
  {
    title: "Finanzen",
    href: "/admin/finances",
    icon: DollarSign,
    description: "Finanzielle Verwaltung",
    hasContent: false
  },
  {
    title: "Arbeitsdienste",
    href: "/admin/work-services",
    icon: Hammer,
    description: "Arbeitsstunden und Tätigkeiten verwalten",
    hasContent: true
  },
  {
    title: "Einstellungen",
    href: "/admin/settings",
    icon: Settings,
    description: "Systemeinstellungen",
    hasContent: true
  }
];

const AdminSidebar = ({ className }: SidebarProps) => {
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(false);

  const isActive = (path: string) => {
    if (path === "/admin") {
      return location.pathname === "/admin";
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div
      className={cn(
        "relative flex flex-col bg-sidebar border-r border-sidebar-border",
        collapsed ? "w-16" : "w-64",
        "transition-all duration-300 ease-in-out",
        className
      )}
    >
      {/* Collapse Toggle */}
      <div className="flex justify-end p-2">
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => setCollapsed(!collapsed)}
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-2 pb-4">
        <div className="space-y-1">
          {sidebarItems.map((item) => {
            const active = isActive(item.href);
            return item.hasContent ? (
              <NavLink
                key={item.href}
                to={item.href}
                className={cn(
                  "group flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                  active
                    ? "bg-sidebar-accent text-sidebar-accent-foreground"
                    : "text-sidebar-foreground",
                  collapsed && "justify-center px-2"
                )}
                title={collapsed ? item.title : undefined}
              >
                <item.icon
                  className={cn(
                    "flex-shrink-0",
                    collapsed ? "h-5 w-5" : "mr-3 h-4 w-4",
                    active ? "text-tennis-green" : ""
                  )}
                />
                {!collapsed && (
                  <div className="flex flex-col flex-1 min-w-0">
                    <span className="truncate">{item.title}</span>
                    <span className="text-xs text-sidebar-foreground/60 truncate">
                      {item.description}
                    </span>
                  </div>
                )}
              </NavLink>
            ) : (
              <div
                key={item.href}
                className={cn(
                  "group flex items-center rounded-md px-3 py-2 text-sm font-medium",
                  "opacity-60 cursor-not-allowed text-sidebar-foreground",
                  collapsed && "justify-center px-2"
                )}
                title={collapsed ? `${item.title} (Bald verfügbar)` : undefined}
              >
                <item.icon
                  className={cn(
                    "flex-shrink-0 opacity-50",
                    collapsed ? "h-5 w-5" : "mr-3 h-4 w-4"
                  )}
                />
                {!collapsed && (
                  <div className="flex flex-col flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="truncate">{item.title}</span>
                      <span className="text-xs bg-amber-100 text-amber-700 px-1 rounded text-[10px] font-normal">
                        Bald verfügbar
                      </span>
                    </div>
                    <span className="text-xs text-sidebar-foreground/60 truncate">
                      {item.description}
                    </span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </nav>

      {/* Footer */}
      {!collapsed && (
        <div className="p-4 border-t border-sidebar-border">
          <div className="text-xs text-sidebar-foreground/60 text-center">
            TennisVerein Admin
            <br />
            Version 1.0
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSidebar;