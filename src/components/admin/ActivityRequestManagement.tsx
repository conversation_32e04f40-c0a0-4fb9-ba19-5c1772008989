import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface ActivityRequest {
  id: string;
  name: string;
  description?: string;
  hourly_rate?: number;
  status: string;
  created_at: string;
  requested_by: string;
}

const ActivityRequestManagement = () => {
  const [requests, setRequests] = useState<ActivityRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<ActivityRequest | null>(null);
  const [isApprovalDialogOpen, setIsApprovalDialogOpen] = useState(false);
  const [approvalAction, setApprovalAction] = useState<"approve" | "reject">("approve");
  const [approvalNotes, setApprovalNotes] = useState("");
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchPendingRequests();
  }, []);

  const fetchPendingRequests = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from("activities")
        .select(`
          id,
          name,
          description,
          hourly_rate,
          status,
          created_at,
          requested_by
        `)
        .eq("status", "pending")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setRequests(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch pending requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprovalAction = (request: ActivityRequest, action: "approve" | "reject") => {
    setSelectedRequest(request);
    setApprovalAction(action);
    setApprovalNotes("");
    setIsApprovalDialogOpen(true);
  };

  const submitApprovalAction = async () => {
    if (!selectedRequest) return;

    setLoading(true);
    try {
      const updateData: any = {
        status: approvalAction === "approve" ? "approved" : "rejected",
        approved_at: new Date().toISOString(),
        // approved_by: should be set to current admin user
      };

      const { error } = await supabase
        .from("activities")
        .update(updateData)
        .eq("id", selectedRequest.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Activity ${approvalAction === "approve" ? "approved" : "rejected"} successfully`,
      });

      setIsApprovalDialogOpen(false);
      setSelectedRequest(null);
      fetchPendingRequests();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${approvalAction} activity`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          Wartend
        </Badge>;
      case "approved":
        return <Badge variant="default" className="flex items-center gap-1">
          <CheckCircle className="h-3 w-3" />
          Genehmigt
        </Badge>;
      case "rejected":
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          Abgelehnt
        </Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Offene Tätigkeitsanfragen</CardTitle>
          <AlertCircle className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{requests.length}</div>
          <p className="text-xs text-muted-foreground">
            Warten auf Genehmigung
          </p>
        </CardContent>
      </Card>

      {/* Requests Table */}
      <Card>
        <CardHeader>
          <CardTitle>Tätigkeitsanfragen verwalten</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tätigkeit</TableHead>
                <TableHead>Erstellt von</TableHead>
                <TableHead>Stundensatz</TableHead>
                <TableHead>Eingereicht am</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Aktionen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Loading...
                  </TableCell>
                </TableRow>
              ) : requests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    Keine offenen Anfragen
                  </TableCell>
                </TableRow>
              ) : (
                requests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{request.name}</div>
                        {request.description && (
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {request.description}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {request.requested_by || "—"}
                    </TableCell>
                    <TableCell>
                      {request.hourly_rate ? `€${request.hourly_rate}` : "—"}
                    </TableCell>
                    <TableCell>
                      {new Date(request.created_at).toLocaleDateString("de-DE")}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(request.status)}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApprovalAction(request, "approve")}
                          className="flex items-center gap-1"
                        >
                          <CheckCircle className="h-3 w-3" />
                          Genehmigen
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleApprovalAction(request, "reject")}
                          className="flex items-center gap-1"
                        >
                          <XCircle className="h-3 w-3" />
                          Ablehnen
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <Dialog open={isApprovalDialogOpen} onOpenChange={setIsApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalAction === "approve" ? "Tätigkeit genehmigen" : "Tätigkeit ablehnen"}
            </DialogTitle>
            <DialogDescription>
              {approvalAction === "approve" 
                ? "Diese Tätigkeit wird für Mitglieder verfügbar gemacht."
                : "Diese Tätigkeit wird abgelehnt und nicht verfügbar gemacht."
              }
            </DialogDescription>
          </DialogHeader>
          
          {selectedRequest && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Tätigkeit</Label>
                <p className="text-sm">{selectedRequest.name}</p>
              </div>
              
              {selectedRequest.description && (
                <div>
                  <Label className="text-sm font-medium">Beschreibung</Label>
                  <p className="text-sm">{selectedRequest.description}</p>
                </div>
              )}
              
              <div>
                <Label htmlFor="approval_notes">
                  {approvalAction === "approve" ? "Anmerkungen (optional)" : "Grund für Ablehnung"}
                </Label>
                <Textarea
                  id="approval_notes"
                  value={approvalNotes}
                  onChange={(e) => setApprovalNotes(e.target.value)}
                  placeholder={
                    approvalAction === "approve" 
                      ? "Zusätzliche Hinweise zur Genehmigung..." 
                      : "Bitte geben Sie einen Grund für die Ablehnung an..."
                  }
                  rows={3}
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsApprovalDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button 
              onClick={submitApprovalAction}
              disabled={loading || (approvalAction === "reject" && !approvalNotes.trim())}
              variant={approvalAction === "approve" ? "default" : "destructive"}
            >
              {loading ? "..." : approvalAction === "approve" ? "Genehmigen" : "Ablehnen"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ActivityRequestManagement;