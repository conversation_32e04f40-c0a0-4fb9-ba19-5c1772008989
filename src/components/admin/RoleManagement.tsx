import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>ting<PERSON>, Shield, Check, X, Plus, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";
import type { Database } from "@/integrations/supabase/types";

type AppRole = Database['public']['Enums']['app_role'];
type AppPermission = Database['public']['Enums']['app_permission'];

interface RolePermission {
  id: string;
  role: AppRole;
  permission: AppPermission;
}

interface RoleStats {
  role: AppRole;
  userCount: number;
  permissions: AppPermission[];
}

const RoleManagement = () => {
  const [rolePermissions, setRolePermissions] = useState<RolePermission[]>([]);
  const [roleStats, setRoleStats] = useState<RoleStats[]>([]);
  const [loading, setLoading] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newRoleName, setNewRoleName] = useState('');
  const [newRoleDisplayName, setNewRoleDisplayName] = useState('');
  const [creatingRole, setCreatingRole] = useState(false);

  // Available roles and permissions from the database enums
  const availableRoles: AppRole[] = ['admin', 'mitglied', 'trainer', 'guest', 'super_admin'];
  const permissionCategories = {
    members: {
      name: 'Mitglieder',
      permissions: ['members_view', 'members_create', 'members_edit', 'members_delete'] as AppPermission[]
    },
    courts: {
      name: 'Plätze',
      permissions: ['courts_view', 'courts_book', 'courts_manage', 'courts_config'] as AppPermission[]
    },
    payments: {
      name: 'Zahlungen',
      permissions: ['payments_view', 'payments_process', 'payments_manage'] as AppPermission[]
    },
    tournaments: {
      name: 'Turniere',
      permissions: ['tournaments_view', 'tournaments_create', 'tournaments_manage'] as AppPermission[]
    },
    courses: {
      name: 'Kurse',
      permissions: ['courses_view', 'courses_create', 'courses_manage', 'courses_participate'] as AppPermission[]
    },
    community: {
      name: 'Kommunikation',
      permissions: ['community_view', 'community_post', 'community_moderate'] as AppPermission[]
    },
    reports: {
      name: 'Berichte',
      permissions: ['reports_view', 'reports_generate', 'reports_export'] as AppPermission[]
    },
    profile: {
      name: 'Profile',
      permissions: ['profile_edit_own', 'profile_view_others'] as AppPermission[]
    },
    system: {
      name: 'System',
      permissions: ['system_config', 'user_roles_manage'] as AppPermission[]
    }
  };

  const availablePermissions: AppPermission[] = Object.values(permissionCategories).flatMap(cat => cat.permissions);

  const getRoleDisplayName = (role: AppRole) => {
    const roleNames: Record<AppRole, string> = {
      'admin': 'Administrator',
      'mitglied': 'Mitglied',
      'trainer': 'Trainer',
      'guest': 'Gast',
      'super_admin': 'Super Administrator'
    };
    return roleNames[role] || role;
  };

  const getPermissionDisplayName = (permission: AppPermission) => {
    const permissionNames: Record<string, string> = {
      'members_view': 'Mitglieder anzeigen',
      'members_create': 'Mitglieder erstellen',
      'members_edit': 'Mitglieder bearbeiten',
      'members_delete': 'Mitglieder löschen',
      'courts_view': 'Plätze anzeigen',
      'courts_book': 'Plätze buchen',
      'courts_manage': 'Plätze verwalten',
      'courts_config': 'Platz-Konfiguration',
      'payments_view': 'Zahlungen anzeigen',
      'payments_process': 'Zahlungen verarbeiten',
      'payments_manage': 'Zahlungen verwalten',
      'tournaments_view': 'Turniere anzeigen',
      'tournaments_create': 'Turniere erstellen',
      'tournaments_manage': 'Turniere verwalten',
      'courses_view': 'Kurse anzeigen',
      'courses_create': 'Kurse erstellen',
      'courses_manage': 'Kurse verwalten',
      'courses_participate': 'An Kursen teilnehmen',
      'community_view': 'Community anzeigen',
      'community_post': 'Community-Posts erstellen',
      'community_moderate': 'Community moderieren',
      'reports_view': 'Berichte anzeigen',
      'reports_generate': 'Berichte erstellen',
      'reports_export': 'Berichte exportieren',
      'system_config': 'Systemkonfiguration',
      'user_roles_manage': 'Benutzerrollen verwalten',
      'profile_edit_own': 'Eigenes Profil bearbeiten',
      'profile_view_others': 'Andere Profile anzeigen'
    };
    return permissionNames[permission] || permission;
  };

  useEffect(() => {
    if (isDialogOpen) {
      fetchRoleData();
    }
  }, [isDialogOpen]);

  // Refresh data when component is visible to ensure synchronization
  useEffect(() => {
    if (isDialogOpen) {
      const interval = setInterval(fetchRoleData, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [isDialogOpen]);

  const fetchRoleData = async () => {
    try {
      setLoading(true);
      console.log('RoleManagement: Starting to fetch role data...');
      
      // Fetch role permissions
      const { data: permissions, error: permError } = await supabase
        .from('role_permissions')
        .select('*');

      console.log('RoleManagement: permissions result:', { permissions, permError });
      if (permError) throw permError;

      // Fetch user role statistics
      const { data: userRoles, error: userError } = await supabase
        .from('user_roles')
        .select('role');

      if (userError) throw userError;

      setRolePermissions(permissions || []);

      // Calculate role statistics
      const stats = availableRoles.map(role => {
        const userCount = userRoles?.filter(ur => ur.role === role).length || 0;
        const rolePerms = permissions?.filter(p => p.role === role).map(p => p.permission) || [];
        
        return {
          role,
          userCount,
          permissions: rolePerms
        };
      });

      setRoleStats(stats);
      console.log('RoleManagement: Data loaded successfully, stats:', stats);
    } catch (error) {
      console.error('RoleManagement: Error fetching role data:', error);
      toast({
        title: "Fehler",
        description: "Rollendaten konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      console.log('RoleManagement: Loading finished');
    }
  };

  const togglePermission = async (role: AppRole, permission: AppPermission, hasPermission: boolean) => {
    try {
      if (hasPermission) {
        // Remove permission
        const { error } = await supabase
          .from('role_permissions')
          .delete()
          .eq('role', role)
          .eq('permission', permission);

        if (error) throw error;
      } else {
        // Add permission
        const { error } = await supabase
          .from('role_permissions')
          .insert({ role, permission });

        if (error) throw error;
      }

      // Refresh data
      await fetchRoleData();
      
      toast({
        title: "Erfolgreich",
        description: `Berechtigung für ${getRoleDisplayName(role)} ${hasPermission ? 'entfernt' : 'hinzugefügt'}.`,
      });
    } catch (error) {
      console.error('Error toggling permission:', error);
      toast({
        title: "Fehler",
        description: "Berechtigung konnte nicht geändert werden.",
        variant: "destructive",
      });
    }
  };

  const hasPermission = (role: AppRole, permission: AppPermission) => {
    return rolePermissions.some(rp => rp.role === role && rp.permission === permission);
  };

  const hasCategoryPermissions = (role: AppRole, categoryPermissions: AppPermission[]) => {
    return categoryPermissions.every(permission => hasPermission(role, permission));
  };

  const toggleCategoryPermissions = async (role: AppRole, categoryPermissions: AppPermission[], hasAllPermissions: boolean) => {
    try {
      if (hasAllPermissions) {
        // Remove all category permissions
        for (const permission of categoryPermissions) {
          const { error } = await supabase
            .from('role_permissions')
            .delete()
            .eq('role', role)
            .eq('permission', permission);
          
          if (error) throw error;
        }
      } else {
        // Add all category permissions that are missing
        const permissionsToAdd = categoryPermissions.filter(permission => !hasPermission(role, permission));
        
        if (permissionsToAdd.length > 0) {
          const { error } = await supabase
            .from('role_permissions')
            .insert(permissionsToAdd.map(permission => ({ role, permission })));
          
          if (error) throw error;
        }
      }

      // Refresh data
      await fetchRoleData();
      
      toast({
        title: "Erfolgreich",
        description: `Kategorie-Berechtigungen für ${getRoleDisplayName(role)} ${hasAllPermissions ? 'entfernt' : 'hinzugefügt'}.`,
      });
    } catch (error) {
      console.error('Error toggling category permissions:', error);
      toast({
        title: "Fehler",
        description: "Kategorie-Berechtigungen konnten nicht geändert werden.",
        variant: "destructive",
      });
    }
  };

  const createNewRole = async () => {
    if (!newRoleName.trim()) {
      toast({
        title: "Fehler",
        description: "Bitte geben Sie einen Rollennamen ein.",
        variant: "destructive",
      });
      return;
    }

    try {
      setCreatingRole(true);
      
      // Check if role already exists
      if (availableRoles.includes(newRoleName.toLowerCase() as AppRole)) {
        toast({
          title: "Fehler",
          description: "Diese Rolle existiert bereits.",
          variant: "destructive",
        });
        return;
      }

      // Note: Creating new enum values requires a database migration
      // In a production system, this would trigger a migration
      toast({
        title: "Information",
        description: "Das Erstellen neuer Rollen erfordert eine Datenbank-Migration. Bitte kontaktieren Sie den Administrator.",
        variant: "default",
      });

      // Reset form
      setNewRoleName('');
      setNewRoleDisplayName('');
      
    } catch (error) {
      console.error('Error creating role:', error);
      toast({
        title: "Fehler",
        description: "Rolle konnte nicht erstellt werden.",
        variant: "destructive",
      });
    } finally {
      setCreatingRole(false);
    }
  };

  const confirmDeleteRole = (role: AppRole) => {
    toast({
      title: "Warnung",
      description: "Das Löschen von Rollen ist in PostgreSQL nicht möglich, wenn sie bereits verwendet werden. Entfernen Sie zuerst alle Benutzer mit dieser Rolle.",
      variant: "destructive",
    });
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Shield className="h-4 w-4" />
          Rollen verwalten
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Rollen- und Berechtigungsverwaltung
          </DialogTitle>
          <DialogDescription>
            Verwalte die Rollen und ihre Berechtigungen im System
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="manage" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="manage">Rollen verwalten</TabsTrigger>
            <TabsTrigger value="permissions">Berechtigungen</TabsTrigger>
            <TabsTrigger value="overview">Übersicht</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Rollen-Übersicht</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchRoleData}
                disabled={loading}
                className="gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                Aktualisieren
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {roleStats.map((stat) => (
                <Card key={stat.role}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Users className="h-5 w-5" />
                      {getRoleDisplayName(stat.role)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm text-muted-foreground">Anzahl Benutzer</p>
                        <p className="text-2xl font-bold">{stat.userCount}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground mb-2">Berechtigungen</p>
                        <div className="flex flex-wrap gap-1">
                          {stat.permissions.length > 0 ? (
                            stat.permissions.map((perm) => (
                              <Badge key={perm} variant="secondary" className="text-xs">
                                {getPermissionDisplayName(perm)}
                              </Badge>
                            ))
                          ) : (
                            <p className="text-xs text-muted-foreground">Keine Berechtigungen</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="permissions" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Berechtigungsmatrix</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Verwalte welche Berechtigungen jede Rolle hat
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {Object.entries(permissionCategories).map(([categoryKey, category]) => (
                    <div key={categoryKey} className="border rounded-lg">
                      <div className="bg-muted/30 px-4 py-3 border-b">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-lg">{category.name}</h3>
                          <div className="flex items-center gap-4">
                            {availableRoles.map((role) => {
                              const hasAllPermissions = hasCategoryPermissions(role, category.permissions);
                              return (
                                <div key={role} className="flex flex-col items-center gap-1">
                                  <span className="text-xs font-medium text-muted-foreground">
                                    {getRoleDisplayName(role)}
                                  </span>
                                  <Switch
                                    checked={hasAllPermissions}
                                    onCheckedChange={() => toggleCategoryPermissions(role, category.permissions, hasAllPermissions)}
                                    disabled={loading}
                                  />
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                      <div className="p-4">
                        <div className="overflow-x-auto">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Berechtigung</TableHead>
                                {availableRoles.map((role) => (
                                  <TableHead key={role} className="text-center">
                                    {getRoleDisplayName(role)}
                                  </TableHead>
                                ))}
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {category.permissions.map((permission) => (
                                <TableRow key={permission}>
                                  <TableCell className="font-medium">
                                    {getPermissionDisplayName(permission)}
                                  </TableCell>
                                  {availableRoles.map((role) => {
                                    const hasPermissionValue = hasPermission(role, permission);
                                    return (
                                      <TableCell key={role} className="text-center">
                                        <Switch
                                          checked={hasPermissionValue}
                                          onCheckedChange={() => togglePermission(role, permission, hasPermissionValue)}
                                          disabled={loading}
                                        />
                                      </TableCell>
                                    );
                                  })}
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Create New Role Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Plus className="h-5 w-5" />
                    Neue Rolle erstellen
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Erstelle eine neue Rolle für das System
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="roleName">Rollenname (Systemname)</Label>
                      <Input
                        id="roleName"
                        value={newRoleName}
                        onChange={(e) => setNewRoleName(e.target.value)}
                        placeholder="z.B. manager, secretary"
                        className="mt-1"
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Kleinbuchstaben, ohne Leerzeichen
                      </p>
                    </div>
                    <div>
                      <Label htmlFor="roleDisplayName">Anzeigename</Label>
                      <Input
                        id="roleDisplayName"
                        value={newRoleDisplayName}
                        onChange={(e) => setNewRoleDisplayName(e.target.value)}
                        placeholder="z.B. Manager, Sekretär"
                        className="mt-1"
                      />
                    </div>
                    <Button 
                      onClick={createNewRole} 
                      disabled={creatingRole || !newRoleName.trim()}
                      className="w-full"
                    >
                      {creatingRole ? "Erstelle Rolle..." : "Rolle erstellen"}
                    </Button>
                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5" />
                        <div className="text-sm text-amber-800">
                          <p className="font-medium">Hinweis:</p>
                          <p>Das Erstellen neuer Rollen erfordert eine Datenbank-Migration und Neustart des Systems.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Existing Roles Management */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Bestehende Rollen
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Verwalte bestehende Systemrollen
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {availableRoles.map((role) => {
                      const stat = roleStats.find(s => s.role === role);
                      const userCount = stat?.userCount || 0;
                      const isSystemRole = ['admin', 'mitglied', 'trainer', 'guest'].includes(role);
                      
                      return (
                        <div key={role} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="flex flex-col">
                              <span className="font-medium">{getRoleDisplayName(role)}</span>
                              <span className="text-xs text-muted-foreground">
                                {userCount} Benutzer • {stat?.permissions.length || 0} Berechtigungen
                              </span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            {isSystemRole && (
                              <Badge variant="secondary" className="text-xs">
                                System
                              </Badge>
                            )}
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  disabled={userCount > 0}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Rolle löschen?</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {userCount > 0 ? (
                                      <>
                                        Diese Rolle kann nicht gelöscht werden, da {userCount} Benutzer diese Rolle haben. 
                                        Entfernen Sie zuerst alle Benutzer von dieser Rolle.
                                      </>
                                    ) : (
                                      <>
                                        Das Löschen von Rollen aus PostgreSQL Enums ist komplex und kann zu Datenverlust führen. 
                                        Sind Sie sicher, dass Sie fortfahren möchten?
                                      </>
                                    )}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => confirmDeleteRole(role)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Löschen bestätigen
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-4">
                    <div className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5" />
                      <div className="text-sm text-red-800">
                        <p className="font-medium">Warnung:</p>
                        <p>Das Löschen von Rollen in PostgreSQL ist nur möglich, wenn keine Daten sie referenzieren. 
                        Systemrollen (Admin, Mitglied, Trainer, Gast) sollten nicht gelöscht werden.</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default RoleManagement;