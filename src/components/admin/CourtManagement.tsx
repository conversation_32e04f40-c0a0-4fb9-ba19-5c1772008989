import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Plus, Edit, Trash2, MapPin, AlertCircle } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
  court_group: string | null;
  surface_type: string | null;
  created_at: string;
  updated_at: string;
}

const CourtManagement = () => {
  const [courts, setCourts] = useState<Court[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCourt, setEditingCourt] = useState<Court | null>(null);
  const [formData, setFormData] = useState({
    number: "",
    court_group: "",
    surface_type: "",
    locked: false,
    lock_reason: ""
  });
  const { toast } = useToast();

  useEffect(() => {
    console.log('CourtManagement: Component mounted, starting to load data...');
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('CourtManagement: Starting to fetch courts data...');
      const { data: courtsData, error: courtsError } = await supabase
        .from("courts")
        .select("*")
        .order("number");

      console.log('CourtManagement: courts result:', { courtsData, courtsError });
      if (courtsError) throw courtsError;
      setCourts(courtsData || []);
      console.log('CourtManagement: Data loaded successfully:', courtsData?.length, 'courts');
    } catch (error) {
      console.error('CourtManagement: Error loading data:', error);
      toast({
        title: "Fehler",
        description: "Fehler beim Laden der Daten",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      console.log('CourtManagement: Loading finished');
    }
  };

  const resetForm = () => {
    setFormData({
      number: "",
      court_group: "",
      surface_type: "",
      locked: false,
      lock_reason: ""
    });
    setEditingCourt(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.number) {
      toast({
        title: "Fehler",
        description: "Bitte geben Sie eine Platznummer ein",
        variant: "destructive"
      });
      return;
    }

    try {
      if (editingCourt) {
        const { error } = await supabase
          .from("courts")
          .update({
            number: parseInt(formData.number),
            court_group: formData.court_group || null,
            surface_type: formData.surface_type || null,
            locked: formData.locked,
            lock_reason: formData.lock_reason || null
          })
          .eq("id", editingCourt.id);

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Platz wurde aktualisiert"
        });
      } else {
        const { error } = await supabase
          .from("courts")
          .insert({
            number: parseInt(formData.number),
            court_group: formData.court_group || null,
            surface_type: formData.surface_type || null,
            locked: formData.locked,
            lock_reason: formData.lock_reason || null
          });

        if (error) throw error;

        toast({
          title: "Erfolg",
          description: "Platz wurde erstellt"
        });
      }

      setDialogOpen(false);
      resetForm();
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: `Fehler beim ${editingCourt ? 'Aktualisieren' : 'Erstellen'} des Platzes`,
        variant: "destructive"
      });
    }
  };

  const handleEdit = (court: Court) => {
    setEditingCourt(court);
    setFormData({
      number: court.number.toString(),
      court_group: court.court_group || "",
      surface_type: court.surface_type || "",
      locked: court.locked,
      lock_reason: court.lock_reason || ""
    });
    setDialogOpen(true);
  };

  const handleDelete = async (court: Court) => {
    if (!confirm(`Sind Sie sicher, dass Sie Platz ${court.number} löschen möchten?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from("courts")
        .delete()
        .eq("id", court.id);

      if (error) throw error;

      toast({
        title: "Erfolg",
        description: "Platz wurde gelöscht"
      });
      loadData();
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Löschen des Platzes",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return <div>Laden...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Plätze verwalten
            </CardTitle>
            <CardDescription>
              Verwalten Sie die vorhandenen Tennisplätze (aktuelles Schema)
            </CardDescription>
          </div>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Neuer Platz
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCourt ? "Platz bearbeiten" : "Neuer Platz"}
                </DialogTitle>
                <DialogDescription>
                  {editingCourt ? "Bearbeiten Sie die Platz-Details" : "Erstellen Sie einen neuen Tennisplatz"}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="number">Platznummer *</Label>
                  <Input
                    id="number"
                    type="number"
                    value={formData.number}
                    onChange={(e) => setFormData({ ...formData, number: e.target.value })}
                    placeholder="z.B. 1"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="court_group">Platzgruppe</Label>
                  <Input
                    id="court_group"
                    value={formData.court_group}
                    onChange={(e) => setFormData({ ...formData, court_group: e.target.value })}
                    placeholder="z.B. Hauptplätze"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="surface_type">Oberflächentyp</Label>
                  <Select
                    value={formData.surface_type}
                    onValueChange={(value) => setFormData({ ...formData, surface_type: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Oberflächentyp auswählen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sand">Sand</SelectItem>
                      <SelectItem value="hard">Hartplatz</SelectItem>
                      <SelectItem value="grass">Rasen</SelectItem>
                      <SelectItem value="indoor">Indoor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="locked"
                    checked={formData.locked}
                    onCheckedChange={(checked) => setFormData({ ...formData, locked: checked })}
                  />
                  <Label htmlFor="locked">Platz gesperrt</Label>
                </div>

                {formData.locked && (
                  <div className="space-y-2">
                    <Label htmlFor="lock_reason">Sperrgrund</Label>
                    <Textarea
                      id="lock_reason"
                      value={formData.lock_reason}
                      onChange={(e) => setFormData({ ...formData, lock_reason: e.target.value })}
                      placeholder="Grund für die Sperrung..."
                      rows={2}
                    />
                  </div>
                )}

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                    Abbrechen
                  </Button>
                  <Button type="submit">
                    {editingCourt ? "Aktualisieren" : "Erstellen"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Hinweis: Dies verwendet das aktuelle Datenbankschema. Für erweiterte Felder (Name, Typ, Club, Beschreibung) muss die Datenbank-Migration ausgeführt werden.
          </AlertDescription>
        </Alert>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Nummer</TableHead>
              <TableHead>Gruppe</TableHead>
              <TableHead>Oberfläche</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Sperrgrund</TableHead>
              <TableHead className="text-right">Aktionen</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {courts.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center text-muted-foreground">
                  Keine Plätze gefunden
                </TableCell>
              </TableRow>
            ) : (
              courts.map((court) => (
                <TableRow key={court.id}>
                  <TableCell className="font-medium">{court.number}</TableCell>
                  <TableCell>{court.court_group || "-"}</TableCell>
                  <TableCell>{court.surface_type || "-"}</TableCell>
                  <TableCell>
                    <Badge variant={court.locked ? "destructive" : "default"}>
                      {court.locked ? "Gesperrt" : "Verfügbar"}
                    </Badge>
                  </TableCell>
                  <TableCell>{court.lock_reason || "-"}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(court)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(court)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default CourtManagement;