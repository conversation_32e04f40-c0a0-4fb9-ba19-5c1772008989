import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Plus, Minus, Clock, Settings2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

const DAYS = [
  { value: 0, label: "So" },
  { value: 1, label: "Mo" },
  { value: 2, label: "Di" },
  { value: 3, label: "Mi" },
  { value: 4, label: "Do" },
  { value: 5, label: "Fr" },
  { value: 6, label: "Sa" },
];

const CourtSetup = () => {
  const [courts, setCourts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [defaultStandardTimes, setDefaultStandardTimes] = useState({
    start_time: "08:00",
    end_time: "22:00",
    days_of_week: [1, 2, 3, 4, 5, 6, 0] // Mon-Sun
  });
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // Load default standard times settings
      const { data: defaultTimesSettings } = await supabase
        .from("system_settings")
        .select("*")
        .eq("key", "default_standard_times")
        .maybeSingle();

      if (defaultTimesSettings) {
        setDefaultStandardTimes(JSON.parse(defaultTimesSettings.value as string));
      }

      // Load courts
      const { data: courtsData } = await supabase
        .from("courts")
        .select("*")
        .order("number");

      if (courtsData) {
        setCourts(courtsData);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load court setup data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const addNewCourt = async () => {
    try {
      const newCourtNumber = courts.length + 1;
      const { data } = await supabase
        .from("courts")
        .insert({ number: newCourtNumber })
        .select()
        .single();
      
      if (data) {
        setCourts([...courts, data]);

        toast({
          title: "Success",
          description: `Platz ${newCourtNumber} wurde hinzugefügt`
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add new court",
        variant: "destructive"
      });
    }
  };

  const removeCourt = async () => {
    if (courts.length <= 1) {
      toast({
        title: "Warnung",
        description: "Mindestens ein Platz muss vorhanden sein",
        variant: "destructive"
      });
      return;
    }

    try {
      const lastCourt = courts[courts.length - 1];
      await supabase.from("courts").delete().eq("id", lastCourt.id);
      
      const updatedCourts = courts.slice(0, -1);
      setCourts(updatedCourts);

      toast({
        title: "Success",
        description: `Platz ${lastCourt.number} wurde entfernt`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove court",
        variant: "destructive"
      });
    }
  };

  const saveDefaultStandardTimes = async () => {
    try {
      await supabase
        .from("system_settings")
        .upsert({ 
          key: "default_standard_times", 
          value: JSON.stringify(defaultStandardTimes) 
        });

      toast({
        title: "Success",
        description: "Standard-Spielzeiten-Konfiguration wurde gespeichert"
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save default standard times",
        variant: "destructive"
      });
    }
  };

  const applyStandardTimesToAllCourts = async () => {
    if (courts.length === 0) {
      toast({
        title: "Warnung",
        description: "Keine Plätze vorhanden",
        variant: "destructive"
      });
      return;
    }

    try {
      // Remove all existing availability for all courts
      await supabase
        .from("court_availability")
        .delete()
        .in("court_id", courts.map(c => c.id));

      // Apply the default standard times to all courts
      const insertData = courts.map(court => ({
        court_id: court.id,
        start_time: defaultStandardTimes.start_time,
        end_time: defaultStandardTimes.end_time,
        days_of_week: defaultStandardTimes.days_of_week
      }));

      await supabase
        .from("court_availability")
        .insert(insertData);

      // Trigger a custom event to notify other components
      window.dispatchEvent(new CustomEvent('courtAvailabilityUpdated'));

      toast({
        title: "Erfolgreich",
        description: `Standard-Spielzeiten (${defaultStandardTimes.start_time} - ${defaultStandardTimes.end_time}) wurden auf alle ${courts.length} Plätze angewendet.`
      });
    } catch (error) {
      toast({
        title: "Fehler",
        description: "Fehler beim Anwenden der Standard-Spielzeiten",
        variant: "destructive"
      });
    }
  };

  const toggleDay = (day: number) => {
    const newDays = defaultStandardTimes.days_of_week.includes(day)
      ? defaultStandardTimes.days_of_week.filter(d => d !== day)
      : [...defaultStandardTimes.days_of_week, day];
    
    setDefaultStandardTimes({
      ...defaultStandardTimes,
      days_of_week: newDays
    });
  };

  if (loading) {
    return <div>Laden...</div>;
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Plätze verwalten */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings2 className="h-5 w-5" />
            Anzahl Plätze
          </CardTitle>
          <CardDescription>
            Anzahl der verfügbaren Tennisplätze festlegen
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex flex-col">
              <Label className="text-sm font-medium mb-1">Anzahl Plätze</Label>
              <p className="text-5xl font-bold text-primary leading-none">{courts.length}</p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={removeCourt}
                disabled={courts.length <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={addNewCourt}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2">
            {courts.map((court, index) => (
              <Badge key={court.id} variant="outline">
                Platz {court.number}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Standard-Spielzeiten */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Standard-Spielzeiten
          </CardTitle>
          <CardDescription>
            Standard-Zeiten für das Buchungssystem definieren
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_time">Von</Label>
              <Input
                id="start_time"
                type="time"
                value={defaultStandardTimes.start_time}
                onChange={(e) => setDefaultStandardTimes({
                  ...defaultStandardTimes,
                  start_time: e.target.value
                })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="end_time">Bis</Label>
              <Input
                id="end_time"
                type="time"
                value={defaultStandardTimes.end_time}
                onChange={(e) => setDefaultStandardTimes({
                  ...defaultStandardTimes,
                  end_time: e.target.value
                })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Wochentage</Label>
            <div className="flex flex-wrap gap-2">
              {DAYS.map((day) => (
                <Button
                  key={day.value}
                  variant={defaultStandardTimes.days_of_week.includes(day.value) ? "default" : "outline"}
                  size="sm"
                  onClick={() => toggleDay(day.value)}
                  className="w-12"
                >
                  {day.label}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          <div className="flex gap-2">
            <Button onClick={saveDefaultStandardTimes} className="flex-1">
              Speichern
            </Button>
            <Button 
              variant="outline" 
              onClick={applyStandardTimesToAllCourts}
              className="flex-1"
              disabled={courts.length === 0}
            >
              Auf alle anwenden
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CourtSetup;