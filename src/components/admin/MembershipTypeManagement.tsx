import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, CommandSeparator } from "@/components/ui/command";
import { Badge } from "@/components/ui/badge";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Separator } from "@/components/ui/separator";
import { Plus, Edit, Trash2, Users, Euro, Link, Unlink, RefreshCw, Check, ChevronDown, X } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";

interface MembershipType {
  id: string;
  name: string;
  description: string;
  annual_fee: number;
  age_category: 'Jugend' | 'Erwachsene' | 'Senioren';
  is_group_type: boolean;
  member_count?: number;
}

interface FeeType {
  id: string;
  name: string;
  description: string | null;
  amount: number;
  currency: string;
  billing_cycle: string;
  category: string;
  is_active: boolean;
  membership_type_id: string | null;
}

interface MembershipTypeManagementProps {
  onRefresh?: () => void;
}

const MembershipTypeManagement = ({ onRefresh }: MembershipTypeManagementProps) => {
  const [membershipTypes, setMembershipTypes] = useState<MembershipType[]>([]);
  const [feeTypes, setFeeTypes] = useState<FeeType[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingType, setEditingType] = useState<MembershipType | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedMembershipForFees, setSelectedMembershipForFees] = useState<string | null>(null);
  const [selectedFeesInDialog, setSelectedFeesInDialog] = useState<string[]>([]);
  const { toast } = useToast();
  const { user, isLoading: authLoading, isAdmin } = useAuth();
  const [openFeesCreate, setOpenFeesCreate] = useState(false);
  const [openFeesEdit, setOpenFeesEdit] = useState(false);
  const [feeMappings, setFeeMappings] = useState<{ id: string; fee_type_id: string; membership_type_id: string }[]>([]);
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    annual_fee: '',
    age_category: '',
    is_group_type: false,
  });

  useEffect(() => {
    if (!authLoading && user) {
      console.log('MembershipTypeManagement: User authenticated, fetching data...');
      fetchMembershipTypes();
      fetchFeeTypes();
      fetchFeeMappings();
    }
  }, [authLoading, user]);
  // Show loading while auth is being established
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Authentifizierung wird geladen...</span>
        </div>
      </div>
    );
  }

  // Show error state if not authenticated
  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-32 space-y-3">
        <div className="text-center">
          <p className="text-muted-foreground">
            Nicht authentifiziert. Bitte melden Sie sich an.
          </p>
        </div>
      </div>
    );
  }

  const fetchMembershipTypes = async () => {
    try {
      setLoading(true);
      console.log('MembershipTypeManagement: Starting to fetch membership types...');
      
      // Check current user and their roles
      const { data: user, error: userError } = await supabase.auth.getUser();
      console.log('MembershipTypeManagement: Current user:', { user: user?.user?.id, email: user?.user?.email, userError });
      
      if (userError) {
        console.error('MembershipTypeManagement: User authentication error:', userError);
        throw userError;
      }

      // Check user roles
      if (user?.user?.id) {
        const { data: roles, error: rolesError } = await supabase
          .rpc('get_user_roles', { _user_id: user.user.id });
        console.log('MembershipTypeManagement: User roles:', { roles, rolesError });
      }
      
      // Fetch membership types from the membership_types table
      const { data: membershipTypesData, error: membershipTypesError } = await supabase
        .from('membership_types')
        .select('*')
        .order('display_name');

      console.log('MembershipTypeManagement: membership_types result:', { membershipTypesData, membershipTypesError });
      
      if (membershipTypesError) {
        console.error('MembershipTypeManagement: Error fetching membership types:', membershipTypesError);
        throw membershipTypesError;
      }

      // Get member counts by membership type
      const { data: memberData, error: memberError } = await supabase
        .from('members')
        .select('membership_type')
        .not('membership_type', 'is', null);

      console.log('MembershipTypeManagement: memberData result:', { memberData, memberError });
      if (memberError) {
        console.error('MembershipTypeManagement: Error fetching members:', memberError);
        // Continue without member counts rather than failing completely
      }

      const memberCounts = memberData?.reduce((acc: any, member) => {
        acc[member.membership_type] = (acc[member.membership_type] || 0) + 1;
        return acc;
      }, {}) || {};

      // Transform the data to match our interface
      const transformedTypes: MembershipType[] = membershipTypesData?.map(type => ({
        id: type.id,
        name: type.display_name,
        description: type.description || '',
        annual_fee: 0, // We don't store annual fee in membership_types anymore, it's in fee_types
        age_category: getAgeCategoryForType(type.value),
        is_group_type: !!type.is_group_type,
        member_count: memberCounts[type.value] || 0
      })) || [];

      setMembershipTypes(transformedTypes);
      console.log('MembershipTypeManagement: Final membership types set:', transformedTypes);
      
    } catch (error) {
      console.error('MembershipTypeManagement: Error in fetchMembershipTypes:', error);
      toast({
        title: "Fehler",
        description: "Mitgliedschaftstypen konnten nicht geladen werden.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchFeeTypes = async () => {
    try {
      const { data, error } = await supabase
        .from("fee_types")
        .select("*")
        .eq("is_active", true)
        .order("category", { ascending: true })
        .order("name", { ascending: true });

      if (error) throw error;
      setFeeTypes(data || []);
    } catch (error) {
      console.error("Error fetching fee types:", error);
      toast({
        title: "Fehler",
        description: "Gebührenarten konnten nicht geladen werden.",
        variant: "destructive",
      });
    }
  };

  const fetchFeeMappings = async () => {
    try {
      const { data, error } = await supabase
        .from("fee_type_membership_types")
        .select("*");
      if (error) throw error;
      setFeeMappings(data || []);
    } catch (error) {
      console.error("Error fetching fee mappings:", error);
    }
  };

  const getGermanName = (type: string): string => {
    const translations: { [key: string]: string } = {
      'Adult': 'Erwachsene',
      'Youth': 'Jugendliche', 
      'Child': 'Kinder',
      'Senior': 'Senioren',
      'Student': 'Studenten',
      'Family': 'Familien',
      'Couple': 'Paare'
    };
    // Filter out guest types
    if (type.toLowerCase().includes('guest') || type.toLowerCase().includes('gast')) {
      return '';
    }
    return translations[type] || type;
  };

  const getDescriptionForType = (type: string): string => {
    const descriptions: { [key: string]: string } = {
      'Adult': 'Mitgliedschaft für Erwachsene',
      'Youth': 'Mitgliedschaft für Jugendliche',
      'Child': 'Mitgliedschaft für Kinder',
      'Senior': 'Mitgliedschaft für Senioren',
      'Student': 'Mitgliedschaft für Studenten',
      'Family': 'Mitgliedschaft für Familien mit Kindern',
      'Couple': 'Mitgliedschaft für Paare'
    };
    return descriptions[type] || 'Standardmitgliedschaft';
  };

  const getAgeCategoryForType = (type: string): 'Jugend' | 'Erwachsene' | 'Senioren' => {
    if (type.toLowerCase().includes('youth') || type.toLowerCase().includes('jugend') || type.toLowerCase().includes('child') || type.toLowerCase().includes('kind')) return 'Jugend';
    if (type.toLowerCase().includes('senior')) return 'Senioren';
    return 'Erwachsene';
  };


  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      annual_fee: '',
      age_category: '',
      is_group_type: false,
    });
  };

  const handleCreate = () => {
    resetForm();
    setSelectedFeesInDialog([]);
    setIsCreateDialogOpen(true);
  };

  const handleEdit = (membershipType: MembershipType) => {
    setEditingType(membershipType);
    setFormData({
      name: membershipType.name,
      description: membershipType.description,
      annual_fee: membershipType.annual_fee.toString(),
      age_category: membershipType.age_category,
      is_group_type: membershipType.is_group_type,
    });
    // Vorbelegung der Gebührenauswahl mit bereits zugeordneten Gebühren
    const initialSelected = getAssignedFees(membershipType.name).map((fee) => fee.id);
    setSelectedFeesInDialog(initialSelected);
    setIsEditDialogOpen(true);
  };

  const handleSave = async () => {
    try {
      if (!formData.name) {
        toast({
          title: "Fehler",
          description: "Bitte geben Sie einen Namen ein.",
          variant: "destructive",
        });
        return;
      }

      const newType: MembershipType = {
        id: editingType?.id || `${formData.name.toLowerCase().replace(/\s+/g, '_')}_${Date.now()}`,
        name: formData.name,
        description: formData.description,
        annual_fee: parseFloat(formData.annual_fee || '0'),
        age_category: formData.age_category as 'Jugend' | 'Erwachsene' | 'Senioren',
        is_group_type: formData.is_group_type,
        member_count: editingType?.member_count || 0
      };

      // Gebühren-Verknüpfungen anwenden (optional)
      const membershipNameForAssignment = editingType ? editingType.name : formData.name;
      const prevAssignedIds = editingType ? getAssignedFees(editingType.name).map((f) => f.id) : [];
      const toAssign = selectedFeesInDialog.filter((id) => !prevAssignedIds.includes(id));
      const toUnassign = prevAssignedIds.filter((id) => !selectedFeesInDialog.includes(id));
      const membershipValue = getMembershipTypeValue(membershipNameForAssignment);
      if (toAssign.length || toUnassign.length) {
        await Promise.all([
          ...toAssign.map((id) => handleFeeAssignment(id, membershipValue, true)),
          ...toUnassign.map((id) => handleFeeAssignment(id, membershipValue, false)),
        ]);
      }

      if (editingType) {
        // Update existing
        setMembershipTypes(prev => 
          prev.map(type => type.id === editingType.id ? newType : type)
        );
        setIsEditDialogOpen(false);
        setEditingType(null);
        toast({
          title: "Erfolg",
          description: "Mitgliedschaftstyp wurde aktualisiert.",
        });
      } else {
        // Create new
        setMembershipTypes(prev => [...prev, newType]);
        setIsCreateDialogOpen(false);
        toast({
          title: "Erfolg",
          description: "Neuer Mitgliedschaftstyp wurde erstellt.",
        });
      }

      resetForm();
      onRefresh?.();
    } catch (error) {
      console.error('Error saving membership type:', error);
      toast({
        title: "Fehler",
        description: "Mitgliedschaftstyp konnte nicht gespeichert werden.",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (membershipType: MembershipType) => {
    try {
      if (membershipType.member_count && membershipType.member_count > 0) {
        toast({
          title: "Fehler",
          description: "Mitgliedschaftstyp kann nicht gelöscht werden, da noch Mitglieder zugeordnet sind.",
          variant: "destructive",
        });
        return;
      }

      setMembershipTypes(prev => prev.filter(type => type.id !== membershipType.id));
      toast({
        title: "Erfolg",
        description: "Mitgliedschaftstyp wurde gelöscht.",
      });
      onRefresh?.();
    } catch (error) {
      console.error('Error deleting membership type:', error);
      toast({
        title: "Fehler",
        description: "Mitgliedschaftstyp konnte nicht gelöscht werden.",
        variant: "destructive",
      });
    }
  };

  const handleFeeAssignment = async (feeTypeId: string, membershipId: string, assign: boolean) => {
    try {
      if (assign) {
        const { error } = await supabase
          .from("fee_type_membership_types")
          .upsert(
            [{ fee_type_id: feeTypeId, membership_type_id: membershipId }],
            { onConflict: "fee_type_id,membership_type_id" }
          );
        if (error) throw error;
        setFeeMappings((prev) =>
          prev.some((m) => m.fee_type_id === feeTypeId && m.membership_type_id === membershipId)
            ? prev
            : [...prev, { id: crypto.randomUUID?.() || `${feeTypeId}-${membershipId}`, fee_type_id: feeTypeId, membership_type_id: membershipId }]
        );
      } else {
        const { error } = await supabase
          .from("fee_type_membership_types")
          .delete()
          .match({ fee_type_id: feeTypeId, membership_type_id: membershipId });
        if (error) throw error;
        setFeeMappings((prev) => prev.filter((m) => !(m.fee_type_id === feeTypeId && m.membership_type_id === membershipId)));
      }

      toast({
        title: "Erfolg",
        description: assign
          ? "Gebühr wurde der Mitgliedschaft zugeordnet."
          : "Gebühr wurde von der Mitgliedschaft entfernt.",
      });
    } catch (error) {
      console.error("Error updating fee assignment:", error);
      toast({
        title: "Fehler",
        description: "Zuordnung konnte nicht geändert werden.",
        variant: "destructive",
      });
    }
  };
  const getMembershipTypeValue = (membershipTypeName: string): string => {
    // Map display names to database values
    const nameMapping: { [key: string]: string } = {
      'Kinder': 'Jugendlicher',
      'Jugendliche': 'Jugendlicher', 
      'Studenten': 'Student',
      'Erwachsene': 'Erwachsener',
      'Senioren': 'Senior',
      'Paare': 'Familie',
      'Familien': 'Familie'
    };
    return nameMapping[membershipTypeName] || membershipTypeName;
  };

  const getAssignedFees = (membershipTypeName: string): FeeType[] => {
    const membership = membershipTypes.find((mt) => mt.name === membershipTypeName);
    if (!membership) return [];
    const assignedSet = new Set(
      feeMappings.filter((m) => m.membership_type_id === membership.id).map((m) => m.fee_type_id)
    );
    return feeTypes.filter((fee) => assignedSet.has(fee.id));
  };

  const getUnassignedFees = (membershipTypeName: string): FeeType[] => {
    const membership = membershipTypes.find((mt) => mt.name === membershipTypeName);
    if (!membership) return [];
    const assignedSet = new Set(
      feeMappings.filter((m) => m.membership_type_id === membership.id).map((m) => m.fee_type_id)
    );
    return feeTypes.filter((fee) => !assignedSet.has(fee.id));
  };
  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Lade Mitgliedschaftstypen...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Mitgliedschaftsformen verwalten</h3>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreate}>
              <Plus className="h-4 w-4 mr-2" />
              Neue Mitgliedschaft
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Neue Mitgliedschaft erstellen</DialogTitle>
              <DialogDescription>
                Erstellen Sie einen neuen Mitgliedschaftstyp mit spezifischen Konditionen.
              </DialogDescription>
            </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="is_group_type">Kontotyp</Label>
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-muted-foreground">Einzel</span>
                    <Switch
                      id="is_group_type"
                      checked={formData.is_group_type}
                      onCheckedChange={(checked) => setFormData({ ...formData, is_group_type: checked })}
                      aria-label="Kontotyp umschalten"
                    />
                    <span className="text-sm text-muted-foreground">Gruppe</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Einzel: Mitgliedschaft für eine Person. Gruppe: Für Familien/Paare; ermöglicht mehrere zugeordnete Mitglieder und einen Rechnungsempfänger.
                  </p>
                </div>
              <div>
                <Label htmlFor="name">Name der Mitgliedschaft *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="z.B. Erwachsene, Familien, Paare, Studenten"
                />
              </div>
              <div>
                <Label htmlFor="description">Beschreibung</Label>
                <Input
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="z.B. Mitgliedschaft für Familien mit Kindern oder Paartraining inklusive"
                />
              </div>
              <div>
                <Label>Beiträge & Gebühren (optional)</Label>
                <p className="text-xs text-muted-foreground mb-3">Wählen Sie Gebühren, die automatisch zugeordnet werden. Sie können dies später auf der Karte ändern.</p>
                <Popover open={openFeesCreate} onOpenChange={setOpenFeesCreate}>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-between">
                      <div className="flex flex-wrap gap-1">
                        {selectedFeesInDialog.length > 0 ? (
                          selectedFeesInDialog.map((id) => {
                            const fee = feeTypes.find((f) => f.id === id);
                            if (!fee) return null;
                            return (
                              <Badge key={id} variant="secondary" className="flex items-center gap-1">
                                {fee.name}
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setSelectedFeesInDialog((prev) => prev.filter((x) => x !== id));
                                  }}
                                  aria-label={`Auswahl ${fee.name} entfernen`}
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </Badge>
                            );
                          })
                        ) : (
                          <span className="text-sm text-muted-foreground">Gebühren auswählen</span>
                        )}
                      </div>
                      <ChevronDown className="h-4 w-4 opacity-60" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[420px] p-0 z-50" align="start">
                    <Command>
                      <CommandInput placeholder="Gebühren suchen..." />
                      <CommandList>
                        <CommandEmpty>Keine Treffer</CommandEmpty>
                        <CommandGroup heading="Mitgliedschaft">
                          {feeTypes.filter((f) => f.category === 'membership').map((fee) => (
                            <CommandItem
                              key={fee.id}
                              value={fee.id}
                              onSelect={() =>
                                setSelectedFeesInDialog((prev) =>
                                  prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                                )
                              }
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-2">
                                  {selectedFeesInDialog.includes(fee.id) ? (
                                    <Check className="h-4 w-4" />
                                  ) : (
                                    <span className="inline-block h-4 w-4" />
                                  )}
                                  <span>{fee.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-muted-foreground">€{fee.amount}</span>
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                        <CommandSeparator />
                        <CommandGroup heading="Kurse">
                          {feeTypes.filter((f) => f.category === 'course').map((fee) => (
                            <CommandItem
                              key={fee.id}
                              value={fee.id}
                              onSelect={() =>
                                setSelectedFeesInDialog((prev) =>
                                  prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                                )
                              }
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-2">
                                  {selectedFeesInDialog.includes(fee.id) ? (
                                    <Check className="h-4 w-4" />
                                  ) : (
                                    <span className="inline-block h-4 w-4" />
                                  )}
                                  <span>{fee.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-muted-foreground">€{fee.amount}</span>
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                        <CommandSeparator />
                        <CommandGroup heading="Sonstige">
                          {feeTypes.filter((f) => f.category !== 'membership' && f.category !== 'course').map((fee) => (
                            <CommandItem
                              key={fee.id}
                              value={fee.id}
                              onSelect={() =>
                                setSelectedFeesInDialog((prev) =>
                                  prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                                )
                              }
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-2">
                                  {selectedFeesInDialog.includes(fee.id) ? (
                                    <Check className="h-4 w-4" />
                                  ) : (
                                    <span className="inline-block h-4 w-4" />
                                  )}
                                  <span>{fee.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-muted-foreground">€{fee.amount}</span>
                                </div>
                              </div>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Abbrechen
              </Button>
              <Button onClick={handleSave}>Erstellen</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        {membershipTypes.map((membershipType) => (
          <Card key={membershipType.id}>
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    {membershipType.name}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {membershipType.description}
                  </CardDescription>
                </div>
                <div className="flex gap-1">
                  <Button variant="ghost" size="icon" onClick={() => handleEdit(membershipType)}>
                    <Edit className="h-4 w-4" />
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Mitgliedschaft löschen</AlertDialogTitle>
                        <AlertDialogDescription>
                          Sind Sie sicher, dass Sie "{membershipType.name}" löschen möchten? 
                          {membershipType.member_count && membershipType.member_count > 0 && (
                            <span className="text-red-600 font-medium">
                              {" "}Dieser Typ hat noch {membershipType.member_count} aktive Mitglieder und kann nicht gelöscht werden.
                            </span>
                          )}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Abbrechen</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={() => handleDelete(membershipType)}
                          disabled={membershipType.member_count && membershipType.member_count > 0}
                        >
                          Löschen
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Kontotyp:</span>
                  <Badge variant="outline">{membershipType.is_group_type ? 'Gruppe' : 'Einzel'}</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Aktive Mitglieder:</span>
                  <Badge variant="secondary">{membershipType.member_count || 0}</Badge>
                </div>
                
                <Separator />
                
                {/* Fee Assignment Section */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Zugeordnete Gebühren:</span>
                    <Button 
                      variant="ghost" 
                      size="sm"
                      onClick={() => setSelectedMembershipForFees(
                        selectedMembershipForFees === membershipType.name ? null : membershipType.name
                      )}
                    >
                      {selectedMembershipForFees === membershipType.name ? (
                        <>
                          <Unlink className="h-3 w-3 mr-1" />
                          Schließen
                        </>
                      ) : (
                        <>
                          <Link className="h-3 w-3 mr-1" />
                          Verwalten
                        </>
                      )}
                    </Button>
                  </div>
                  
                  {/* Show assigned fees */}
                  <div className="space-y-1">
                    {getAssignedFees(membershipType.name).length > 0 ? (
                      getAssignedFees(membershipType.name).map(fee => (
                        <div key={fee.id} className="flex items-center justify-between text-xs p-2 bg-muted rounded">
                          <span>{fee.name}</span>
                          <span className="text-muted-foreground">€{fee.amount}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-xs text-muted-foreground p-2 bg-muted rounded text-center">
                        Keine Gebühren zugeordnet
                      </div>
                    )}
                  </div>
                  
                  {/* Fee assignment interface */}
                  {selectedMembershipForFees === membershipType.name && (
                    <div className="mt-3 p-3 border rounded-lg bg-background">
                      <div className="space-y-3">
                        <div>
                          <h4 className="text-sm font-medium mb-2">Zugeordnete Gebühren entfernen:</h4>
                          <div className="space-y-1">
                            {getAssignedFees(membershipType.name).map(fee => (
                              <div key={fee.id} className="flex items-center justify-between text-xs p-2 border rounded">
                                <div>
                                  <span className="font-medium">{fee.name}</span>
                                  <span className="text-muted-foreground ml-2">€{fee.amount}</span>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleFeeAssignment(fee.id, membershipType.id, false)}
                                >
                                  <Unlink className="h-3 w-3" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <Separator />
                        
                        <div>
                          <h4 className="text-sm font-medium mb-2">Verfügbare Gebühren zuordnen:</h4>
                          <div className="space-y-1">
                            {getUnassignedFees(membershipType.name).length > 0 ? (
                              getUnassignedFees(membershipType.name).map(fee => (
                                <div key={fee.id} className="flex items-center justify-between text-xs p-2 border rounded">
                                  <div>
                                    <span className="font-medium">{fee.name}</span>
                                    <span className="text-muted-foreground ml-2">€{fee.amount}</span>
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleFeeAssignment(fee.id, getMembershipTypeValue(membershipType.name), true)}
                                  >
                                    <Link className="h-3 w-3" />
                                  </Button>
                                </div>
                              ))
                            ) : (
                              <div className="text-xs text-muted-foreground p-2 border rounded text-center">
                                Alle Gebühren sind bereits zugeordnet
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Mitgliedschaft bearbeiten</DialogTitle>
            <DialogDescription>
              Bearbeiten Sie die Details der Mitgliedschaft.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-is_group_type">Kontotyp</Label>
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground">Einzel</span>
                <Switch
                  id="edit-is_group_type"
                  checked={formData.is_group_type}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_group_type: checked })}
                  aria-label="Kontotyp umschalten"
                />
                <span className="text-sm text-muted-foreground">Gruppe</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Einzel: Mitgliedschaft für eine Person. Gruppe: Für Familien/Paare; ermöglicht mehrere zugeordnete Mitglieder und einen Rechnungsempfänger.
              </p>
            </div>
            <div>
              <Label htmlFor="edit-name">Name der Mitgliedschaft *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="z.B. Vollmitgliedschaft Erwachsene"
              />
            </div>
            <div>
              <Label htmlFor="edit-description">Beschreibung</Label>
              <Input
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Kurze Beschreibung der Mitgliedschaft"
              />
            </div>
            <div>
              <Label>Beiträge & Gebühren (optional)</Label>
              <p className="text-xs text-muted-foreground mb-3">Wählen Sie Gebühren, die automatisch zugeordnet werden. Sie können dies später auf der Karte ändern.</p>
              <Popover open={openFeesEdit} onOpenChange={setOpenFeesEdit}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-between">
                    <div className="flex flex-wrap gap-1">
                      {selectedFeesInDialog.length > 0 ? (
                        selectedFeesInDialog.map((id) => {
                          const fee = feeTypes.find((f) => f.id === id);
                          if (!fee) return null;
                          return (
                            <Badge key={id} variant="secondary" className="flex items-center gap-1">
                              {fee.name}
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedFeesInDialog((prev) => prev.filter((x) => x !== id));
                                }}
                                aria-label={`Auswahl ${fee.name} entfernen`}
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          );
                        })
                      ) : (
                        <span className="text-sm text-muted-foreground">Gebühren auswählen</span>
                      )}
                    </div>
                    <ChevronDown className="h-4 w-4 opacity-60" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[420px] p-0 z-50" align="start">
                  <Command>
                    <CommandInput placeholder="Gebühren suchen..." />
                    <CommandList>
                      <CommandEmpty>Keine Treffer</CommandEmpty>
                      <CommandGroup heading="Mitgliedschaft">
                        {feeTypes.filter((f) => f.category === 'membership').map((fee) => (
                          <CommandItem
                            key={fee.id}
                            value={fee.id}
                            onSelect={() =>
                              setSelectedFeesInDialog((prev) =>
                                prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                              )
                            }
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                {selectedFeesInDialog.includes(fee.id) ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <span className="inline-block h-4 w-4" />
                                )}
                                <span>{fee.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">€{fee.amount}</span>
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                      <CommandSeparator />
                      <CommandGroup heading="Kurse">
                        {feeTypes.filter((f) => f.category === 'course').map((fee) => (
                          <CommandItem
                            key={fee.id}
                            value={fee.id}
                            onSelect={() =>
                              setSelectedFeesInDialog((prev) =>
                                prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                              )
                            }
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                {selectedFeesInDialog.includes(fee.id) ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <span className="inline-block h-4 w-4" />
                                )}
                                <span>{fee.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">€{fee.amount}</span>
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                      <CommandSeparator />
                      <CommandGroup heading="Sonstige">
                        {feeTypes.filter((f) => f.category !== 'membership' && f.category !== 'course').map((fee) => (
                          <CommandItem
                            key={fee.id}
                            value={fee.id}
                            onSelect={() =>
                              setSelectedFeesInDialog((prev) =>
                                prev.includes(fee.id) ? prev.filter((x) => x !== fee.id) : [...prev, fee.id]
                              )
                            }
                          >
                            <div className="flex items-center justify-between w-full">
                              <div className="flex items-center gap-2">
                                {selectedFeesInDialog.includes(fee.id) ? (
                                  <Check className="h-4 w-4" />
                                ) : (
                                  <span className="inline-block h-4 w-4" />
                                )}
                                <span>{fee.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">€{fee.amount}</span>
                              </div>
                            </div>
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Abbrechen
            </Button>
            <Button onClick={handleSave}>Speichern</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MembershipTypeManagement;