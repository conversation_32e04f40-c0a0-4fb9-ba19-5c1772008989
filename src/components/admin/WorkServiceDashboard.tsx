import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Clock, Users, TrendingUp, Calendar } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { de } from "date-fns/locale";

interface WorkStatsData {
  totalHours: number;
  activeMembers: number;
  completedTasks: number;
  monthlyHours: number;
}

interface MemberWorkSummary {
  member_id: string;
  member_name: string;
  total_hours: number;
  recent_activities: string[];
}

interface RecentActivity {
  id: string;
  activity_name: string;
  member_name: string;
  date: string;
  duration_hours: number;
}

const WorkServiceDashboard = () => {
  const [stats, setStats] = useState<WorkStatsData>({
    totalHours: 0,
    activeMembers: 0,
    completedTasks: 0,
    monthlyHours: 0
  });
  const [memberSummaries, setMemberSummaries] = useState<MemberWorkSummary[]>([]);
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch work statistics
      const { data: workLogs, error: workError } = await supabase
        .from("work_logs")
        .select(`
          *,
          activities (name),
          work_log_assignments (
            member_id,
            members (first_name, last_name)
          )
        `);

      if (workError) throw workError;

      // Calculate stats
      const currentMonth = new Date().getMonth();
      const currentYear = new Date().getFullYear();
      
      const totalHours = workLogs?.reduce((sum, log) => sum + (log.duration_hours || 0), 0) || 0;
      const monthlyHours = workLogs?.filter(log => {
        const logDate = new Date(log.date);
        return logDate.getMonth() === currentMonth && logDate.getFullYear() === currentYear;
      }).reduce((sum, log) => sum + (log.duration_hours || 0), 0) || 0;

      const uniqueMembers = new Set();
      workLogs?.forEach(log => {
        log.work_log_assignments?.forEach((assignment: any) => {
          if (assignment.member_id) uniqueMembers.add(assignment.member_id);
        });
      });

      setStats({
        totalHours,
        activeMembers: uniqueMembers.size,
        completedTasks: workLogs?.length || 0,
        monthlyHours
      });

      // Process member summaries
      const memberMap = new Map<string, MemberWorkSummary>();
      
      workLogs?.forEach(log => {
        log.work_log_assignments?.forEach((assignment: any) => {
          if (assignment.member_id && assignment.members) {
            const memberId = assignment.member_id;
            const memberName = `${assignment.members.first_name} ${assignment.members.last_name}`;
            
            if (!memberMap.has(memberId)) {
              memberMap.set(memberId, {
                member_id: memberId,
                member_name: memberName,
                total_hours: 0,
                recent_activities: []
              });
            }
            
            const memberSummary = memberMap.get(memberId)!;
            memberSummary.total_hours += log.duration_hours || 0;
            
            if (log.activities && memberSummary.recent_activities.length < 3) {
              memberSummary.recent_activities.push(log.activities.name);
            }
          }
        });
      });

      setMemberSummaries(Array.from(memberMap.values()).slice(0, 10));

      // Get recent activities
      const recentActivitiesData: RecentActivity[] = [];
      workLogs?.slice(0, 5).forEach(log => {
        log.work_log_assignments?.forEach((assignment: any) => {
          if (assignment.members && log.activities) {
            recentActivitiesData.push({
              id: log.id,
              activity_name: log.activities.name,
              member_name: `${assignment.members.first_name} ${assignment.members.last_name}`,
              date: log.date,
              duration_hours: log.duration_hours || 0
            });
          }
        });
      });

      setRecentActivities(recentActivitiesData.slice(0, 5));

    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      toast({
        title: "Fehler",
        description: "Konnte Dashboard-Daten nicht laden",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
                <div className="h-4 w-4 bg-muted animate-pulse rounded" />
              </CardHeader>
              <CardContent>
                <div className="h-8 w-16 bg-muted animate-pulse rounded mb-1" />
                <div className="h-3 w-32 bg-muted animate-pulse rounded" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gesamtstunden</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalHours}</div>
            <p className="text-xs text-muted-foreground">
              +{stats.monthlyHours} diesen Monat
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktive Mitglieder</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeMembers}</div>
            <p className="text-xs text-muted-foreground">
              Haben Arbeitsstunden geleistet
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Erledigte Aufgaben</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedTasks}</div>
            <p className="text-xs text-muted-foreground">
              Arbeitseinträge insgesamt
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monatliches Ziel</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round((stats.monthlyHours / 50) * 100)}%</div>
            <Progress value={(stats.monthlyHours / 50) * 100} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {stats.monthlyHours} von 50 Stunden
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Top Contributors */}
        <Card>
          <CardHeader>
            <CardTitle>Top Mitglieder</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {memberSummaries.map((member, index) => (
                <div key={member.member_id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="w-8 h-8 rounded-full p-0 flex items-center justify-center">
                      {index + 1}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{member.member_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {member.recent_activities.slice(0, 2).join(", ")}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{member.total_hours}h</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Neueste Aktivitäten</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{activity.activity_name}</p>
                    <p className="text-xs text-muted-foreground">
                      {activity.member_name} • {format(new Date(activity.date), "dd.MM.yyyy", { locale: de })}
                    </p>
                  </div>
                  <Badge variant="secondary">
                    {activity.duration_hours}h
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WorkServiceDashboard;