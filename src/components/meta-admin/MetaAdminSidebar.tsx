import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import {
  Building2,
  Users,
  Settings,
  BarChart3,
  Shield,
  Globe,
  ChevronDown,
  ChevronRight,
  CreditCard,
  FileText,
  Database,
  Monitor,
  HelpCircle,
  Key,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
  section?: string; // For permission checking
}

const navigation: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/meta-admin',
    icon: BarChart3,
    section: 'dashboard',
  },
  {
    title: 'Vereine (Tenants)',
    icon: Building2,
    section: 'clubs',
    children: [
      { title: 'Alle Vereine', href: '/meta-admin/clubs', icon: Building2, section: 'clubs' },
      { title: 'Neuer Verein', href: '/meta-admin/clubs/new', icon: Building2, section: 'clubs' },
    ],
  },
  {
    title: 'Domains & SSL',
    icon: Globe,
    section: 'domains',
    children: [
      { title: 'Domain Verwaltung', href: '/meta-admin/domains', icon: Globe, section: 'domains' },
      { title: 'SSL Zertifikate', href: '/meta-admin/domains/ssl', icon: Shield, section: 'domains' },
    ],
  },
  {
    title: 'Nutzer & Zugriffsrechte',
    icon: Users,
    section: 'users',
    children: [
      { title: 'Alle Benutzer', href: '/meta-admin/users', icon: Users, section: 'users' },
      { title: 'Meta-Admin Rollen', href: '/meta-admin/users/meta-admins', icon: Shield, section: 'users' },
      { title: 'Impersonation', href: '/meta-admin/users/impersonation', icon: Key, section: 'users' },
    ],
  },
  {
    title: 'Pläne & Abrechnung',
    icon: CreditCard,
    section: 'billing',
    children: [
      { title: 'Plan Verwaltung', href: '/meta-admin/billing/plans', icon: CreditCard, section: 'billing' },
      { title: 'Abonnements', href: '/meta-admin/billing/subscriptions', icon: CreditCard, section: 'billing' },
      { title: 'Rechnungen', href: '/meta-admin/billing/invoices', icon: FileText, section: 'billing' },
    ],
  },
  {
    title: 'Inhalte & Templates',
    icon: FileText,
    section: 'content',
    children: [
      { title: 'E-Mail Templates', href: '/meta-admin/content/email-templates', icon: FileText, section: 'content' },
      { title: 'Branding Defaults', href: '/meta-admin/content/branding', icon: FileText, section: 'content' },
    ],
  },
  {
    title: 'Daten & Integrationen',
    icon: Database,
    section: 'integrations',
    children: [
      { title: 'API Keys & Webhooks', href: '/meta-admin/integrations/api', icon: Key, section: 'integrations' },
      { title: 'Datenexporte', href: '/meta-admin/integrations/exports', icon: Database, section: 'integrations' },
      { title: 'DSGVO Tools', href: '/meta-admin/integrations/gdpr', icon: Shield, section: 'integrations' },
    ],
  },
  {
    title: 'Monitoring & Logs',
    icon: Monitor,
    section: 'monitoring',
    children: [
      { title: 'System Logs', href: '/meta-admin/monitoring/logs', icon: Monitor, section: 'monitoring' },
      { title: 'Performance', href: '/meta-admin/monitoring/performance', icon: BarChart3, section: 'monitoring' },
      { title: 'Alerts', href: '/meta-admin/monitoring/alerts', icon: Monitor, section: 'monitoring' },
    ],
  },
  {
    title: 'Support-Tools',
    icon: HelpCircle,
    section: 'support',
    children: [
      { title: 'Diagnose Tools', href: '/meta-admin/support/diagnostics', icon: HelpCircle, section: 'support' },
      { title: 'Support Tickets', href: '/meta-admin/support/tickets', icon: HelpCircle, section: 'support' },
    ],
  },
  {
    title: 'Einstellungen (global)',
    href: '/meta-admin/settings',
    icon: Settings,
    section: 'settings',
  },
];

export function MetaAdminSidebar() {
  const location = useLocation();
  const { canAccessSection, isReadOnly, roles } = useMetaAdminRole();
  const [openItems, setOpenItems] = useState<string[]>(['Vereine (Tenants)', 'Nutzer & Zugriffsrechte']);

  const toggleItem = (title: string) => {
    setOpenItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const renderNavItem = (item: NavItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openItems.includes(item.title);
    
    // Check if user has permission to access this item
    if (item.section && !canAccessSection(item.section)) {
      return null;
    }

    if (hasChildren) {
      return (
        <Collapsible
          key={item.title}
          open={isOpen}
          onOpenChange={() => toggleItem(item.title)}
        >
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                'w-full justify-start gap-2 h-10',
                level > 0 && 'ml-4'
              )}
            >
              <item.icon className="h-4 w-4" />
              <span className="flex-1 text-left">{item.title}</span>
              {isOpen ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="space-y-1">
            {item.children?.filter(child => !child.section || canAccessSection(child.section)).map(child => renderNavItem(child, level + 1))}
          </CollapsibleContent>
        </Collapsible>
      );
    }

    return (
      <Button
        key={item.title}
        variant="ghost"
        className={cn(
          'w-full justify-start gap-2 h-10',
          level > 0 && 'ml-4',
          item.href && isActive(item.href) && 'bg-accent'
        )}
        asChild
      >
        <Link to={item.href!}>
          <item.icon className="h-4 w-4" />
          <span>{item.title}</span>
        </Link>
      </Button>
    );
  };

  return (
    <div className="w-64 bg-card border-r border-border">
      <div className="p-6">
        <h1 className="text-xl font-bold">Meta Admin</h1>
        <p className="text-sm text-muted-foreground">Vereinsverwaltung</p>
        {roles.length > 0 && (
          <div className="mt-2">
            <div className="text-xs text-muted-foreground">Rolle:</div>
            <div className="text-xs font-medium">
              {roles.join(', ')}
              {isReadOnly && <span className="text-yellow-600 ml-1">(Nur Lesen)</span>}
            </div>
          </div>
        )}
      </div>
      <nav className="px-4 space-y-1">
        {navigation.filter(item => !item.section || canAccessSection(item.section)).map(item => renderNavItem(item))}
      </nav>
    </div>
  );
}