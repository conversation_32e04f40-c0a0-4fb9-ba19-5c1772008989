import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useIsMetaAdmin } from '@/contexts/TenantContext';
import { useMetaAdminRole } from '@/hooks/useMetaAdminRole';
import { MetaAdminSidebar } from './MetaAdminSidebar';
import { MetaAdminHeader } from './MetaAdminHeader';

interface MetaAdminLayoutProps {
  children: ReactNode;
}

export function MetaAdminLayout({ children }: MetaAdminLayoutProps) {
  const { user, isLoading } = useAuth();
  const isMetaAdmin = useIsMetaAdmin();
  const { hasAnyRole, isLoading: rolesLoading } = useMetaAdminRole();

  // Show loading while checking authentication and roles
  if (isLoading || rolesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to auth if not logged in
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Check if user has meta admin role and is on meta admin domain
  if (!isMetaAdmin || !hasAnyRole()) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="flex h-screen bg-background">
      <MetaAdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <MetaAdminHeader />
        <main className="flex-1 overflow-auto p-6">
          {children}
        </main>
      </div>
    </div>
  );
}