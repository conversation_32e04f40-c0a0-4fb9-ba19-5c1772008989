import React, { useEffect, useMemo, useState } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { Dialog, DialogContent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarIcon, Search } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { supabase } from "@/integrations/supabase/client";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export type SpielTyp = "einzel" | "doppel";

export interface PlayerOption {
  id: string;
  label: string; // z.B. "Max Mustermann"
  source: "mitglied" | "gast" | "trainer" | "account";
}

export interface BookingDetails {
  date: Date;
  startTime: string; // HH:mm
  endTime: string;   // HH:mm
  spielTyp: SpielTyp;
  partners: PlayerOption[]; // 1 bei Einzel, 3 bei Doppel
  actingAccountId?: string;
  bookedForAccountId?: string;
}

interface BookingDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  courtNumber?: number;
  defaultDate: Date;
  defaultStartTime: string; // HH:mm
  defaultEndTime?: string;  // HH:mm (optional → +60 Min)
  onConfirm: (details: BookingDetails) => void;
}

// Spieler-Suche (Mitglieder + Gäste/Accounts)
const usePlayerSearch = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<PlayerOption[]>([]);

  const search = async (query: string) => {
    const q = query.trim();
    if (!q) {
      setResults([]);
      return;
    }
    setLoading(true);
    try {
      // Suche Mitglieder
      const { data: members } = await supabase
        .from("members")
        .select("id, first_name, last_name")
        .ilike("first_name", `%${q}%`)
        .limit(10);

      const { data: members2 } = await supabase
        .from("members")
        .select("id, first_name, last_name")
        .ilike("last_name", `%${q}%`)
        .limit(10);

      // Suche Accounts (z.B. Gäste / Trainer)
      const { data: accounts } = await supabase
        .from("accounts")
        .select("id, first_name, last_name, account_type")
        .or(`first_name.ilike.%${q}%,last_name.ilike.%${q}%`)
        .limit(15);

      const mapped: PlayerOption[] = [];

      (members || []).forEach((m) =>
        mapped.push({ id: m.id, label: `${m.first_name} ${m.last_name}`.trim(), source: "mitglied" })
      );
      (members2 || []).forEach((m) =>
        mapped.push({ id: m.id, label: `${m.first_name} ${m.last_name}`.trim(), source: "mitglied" })
      );
      (accounts || []).forEach((a) =>
        mapped.push({
          id: a.id,
          label: `${a.first_name} ${a.last_name}`.trim(),
          source: (String(a.account_type || "account").toLowerCase() as PlayerOption["source"]) || "account",
        })
      );

      // Deduplizieren nach id + label
      const uniq = new Map<string, PlayerOption>();
      for (const r of mapped) {
        const key = `${r.id}-${r.label}`;
        if (!uniq.has(key)) uniq.set(key, r);
      }
      setResults(Array.from(uniq.values()).slice(0, 15));
    } finally {
      setLoading(false);
    }
  };

  return { loading, results, search };
};

function PlayerCombobox({
  value,
  onChange,
  placeholder = "Mitspieler suchen…",
}: {
  value?: PlayerOption | null;
  onChange: (v: PlayerOption | null) => void;
  placeholder?: string;
}) {
  const { loading, results, search } = usePlayerSearch();
  const [open, setOpen] = useState(false);
  const [input, setInput] = useState("");

  useEffect(() => {
    const t = setTimeout(() => {
      if (open) search(input);
    }, 250);
    return () => clearTimeout(t);
  }, [input, open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" role="combobox" className="w-full justify-between">
          <span className={cn(!value && "text-muted-foreground")}>{value ? value.label : placeholder}</span>
          <Search className="h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[360px] p-0" align="start">
        <Command>
          <CommandInput
            value={input}
            onValueChange={setInput}
            placeholder="Name eingeben…"
          />
          <CommandList>
            {loading && <CommandEmpty>Suche…</CommandEmpty>}
            {!loading && results.length === 0 && <CommandEmpty>Keine Ergebnisse</CommandEmpty>}
            {!loading && results.length > 0 && (
              <CommandGroup heading="Ergebnisse">
                {results.map((r) => (
                  <CommandItem
                    key={`${r.id}-${r.label}`}
                    onSelect={() => {
                      onChange(r);
                      setOpen(false);
                    }}
                  >
                    {r.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export const BookingDetailsDialog: React.FC<BookingDetailsDialogProps> = ({
  open,
  onOpenChange,
  courtNumber,
  defaultDate,
  defaultStartTime,
  defaultEndTime,
  onConfirm,
}) => {
  const [date, setDate] = useState<Date>(defaultDate);
  const [startTime, setStartTime] = useState<string>(defaultStartTime);
  const [endTime, setEndTime] = useState<string>(
    defaultEndTime || (() => {
      const [h, m] = defaultStartTime.split(":").map((n) => parseInt(n, 10));
      const end = new Date();
      end.setHours(h);
      end.setMinutes(m + 60);
      return `${String(end.getHours()).padStart(2, "0")}:${String(end.getMinutes()).padStart(2, "0")}`;
    })()
  );
  const [spielTyp, setSpielTyp] = useState<SpielTyp>("einzel");
  const requiredPartners = useMemo(() => (spielTyp === "einzel" ? 1 : 3), [spielTyp]);
  const [partners, setPartners] = useState<(PlayerOption | null)[]>(Array(requiredPartners).fill(null));
  const [accountOptions, setAccountOptions] = useState<{ id: string; label: string }[]>([]);
  const [showAccountSelectors, setShowAccountSelectors] = useState<boolean>(false);
  const [actingAccountId, setActingAccountId] = useState<string | undefined>(undefined);
  const [bookedForAccountId, setBookedForAccountId] = useState<string | undefined>(undefined);
  // Wenn Dialog geöffnet oder Defaults geändert werden, Datum/Zeit aus dem Slot übernehmen
  useEffect(() => {
    if (!open) return;
    setDate(defaultDate);
    setStartTime(defaultStartTime);
    if (defaultEndTime) {
      setEndTime(defaultEndTime);
    } else {
      const [h, m] = defaultStartTime.split(":").map((n) => parseInt(n, 10));
      const end = new Date();
      end.setHours(h || 0);
      end.setMinutes((m || 0) + 60);
      setEndTime(`${String(end.getHours()).padStart(2, "0")}:${String(end.getMinutes()).padStart(2, "0")}`);
    }
  }, [open, defaultDate, defaultStartTime, defaultEndTime]);

  useEffect(() => {
    setPartners((prev) => {
      const next = Array(requiredPartners).fill(null) as (PlayerOption | null)[];
      for (let i = 0; i < Math.min(prev.length, next.length); i++) next[i] = prev[i];
      return next;
    });
  }, [requiredPartners]);

  useEffect(() => {
    const loadAccounts = async () => {
      const { data: auth } = await supabase.auth.getUser();
      const userId = auth?.user?.id;
      if (!userId) {
        setAccountOptions([]);
        setShowAccountSelectors(false);
        return;
      }

      // Ermittele alle Accounts, für die der Nutzer buchen darf
      const { data: rights } = await supabase.rpc("effective_rights", { u: userId });
      const accountIds: string[] = (rights || []).map((r: any) => r.account_id).filter(Boolean);

      if (accountIds.length === 0) {
        setAccountOptions([]);
        setShowAccountSelectors(false);
        return;
      }

      const { data: accounts } = await supabase
        .from("accounts")
        .select("id, first_name, last_name, email, user_id")
        .in("id", accountIds)
        .order("last_name", { ascending: true });

      const options = (accounts || []).map((a: any) => ({
        id: a.id,
        label: `${a.first_name ?? ""} ${a.last_name ?? ""}`.trim() || a.email,
      }));

      setAccountOptions(options);
      setShowAccountSelectors(options.length > 1);

      // Sinnvolle Defaults setzen (eigener Account bevorzugt)
      const own = (accounts || []).find((a: any) => a.user_id === userId);
      const defaultId = own?.id ?? (accounts?.[0]?.id as string | undefined);
      if (defaultId) {
        setActingAccountId(defaultId);
        setBookedForAccountId(defaultId);
      }
    };
    if (open) loadAccounts();
  }, [open]);

  const canConfirm = useMemo(() => partners.filter(Boolean).length === requiredPartners, [partners, requiredPartners]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader className="text-center">
          <DialogTitle>Buchungsdetails</DialogTitle>
          {courtNumber ? (
            <div className="text-sm text-muted-foreground mt-1">Platz {courtNumber}</div>
          ) : null}
        </DialogHeader>

        {/* Datum & Uhrzeit kompakt in einer Zeile */}
        <div className="grid gap-4 md:grid-cols-3 items-end">
          <div className="space-y-2">
            <Label>Datum</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="justify-start">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(date, "PPP", { locale: de })}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={date}
                  onSelect={(d) => d && setDate(d)}
                  initialFocus
                  className={cn("p-3 pointer-events-auto")}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="space-y-2">
            <Label>Zeit von</Label>
            <Input type="time" value={startTime} onChange={(e) => setStartTime(e.target.value)} />
          </div>

          <div className="space-y-2">
            <Label>Zeit bis</Label>
            <Input type="time" value={endTime} onChange={(e) => setEndTime(e.target.value)} />
          </div>
        </div>

        {/* Spieltyp als separate kompakte Zeile */}
        <div className="mt-4 space-y-2">
          <Label>Spieltyp</Label>
          <RadioGroup value={spielTyp} onValueChange={(v) => setSpielTyp(v as SpielTyp)} className="flex gap-6">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="einzel" id="einzel" />
              <Label htmlFor="einzel">Einzel</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="doppel" id="doppel" />
              <Label htmlFor="doppel">Doppel</Label>
            </div>
          </RadioGroup>
          <p className="text-xs text-muted-foreground">Einzel = 1 Mitspieler, Doppel = 3 Mitspieler</p>
        </div>

        <div className="mt-4 space-y-3">
          <Label>Mitspieler auswählen</Label>
          {Array.from({ length: requiredPartners }).map((_, idx) => (
            <PlayerCombobox
              key={idx}
              value={partners[idx]}
              onChange={(v) =>
                setPartners((prev) => {
                  const next = [...prev];
                  next[idx] = v;
                  return next;
                })
              }
              placeholder={requiredPartners === 1 ? "Mitspieler auswählen" : `Mitspieler ${idx + 1} auswählen`}
            />
          ))}
          <p className="text-xs text-muted-foreground">
            Regel: Einzel = 1 Mitspieler, Doppel = 3 Mitspieler
          </p>
        </div>

        {showAccountSelectors && (
          <div className="mt-4 grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Buchen als</Label>
              <Select value={actingAccountId} onValueChange={setActingAccountId}>
                <SelectTrigger>
                  <SelectValue placeholder="Account auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {accountOptions.map((opt) => (
                    <SelectItem key={opt.id} value={opt.id}>{opt.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Buchen für</Label>
              <Select value={bookedForAccountId} onValueChange={setBookedForAccountId}>
                <SelectTrigger>
                  <SelectValue placeholder="Account auswählen" />
                </SelectTrigger>
                <SelectContent>
                  {accountOptions.map((opt) => (
                    <SelectItem key={opt.id} value={opt.id}>{opt.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Abbrechen</Button>
          <Button
            variant="tennis"
            disabled={!canConfirm}
            onClick={() =>
              onConfirm({
                date,
                startTime,
                endTime,
                spielTyp,
                partners: partners.filter(Boolean) as PlayerOption[],
                actingAccountId,
                bookedForAccountId,
              })
            }
          >
            Buchung bestätigen
          </Button>
        </DialogFooter>

      </DialogContent>
    </Dialog>
  );
};
