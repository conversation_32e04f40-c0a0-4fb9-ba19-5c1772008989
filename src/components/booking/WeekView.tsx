import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Lock, Clock } from "lucide-react";
import { addDays, format } from "date-fns";
import { de } from "date-fns/locale";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface WeekViewProps {
  selectedDate: Date;
  courts: Court[];
  timeSlots: TimeSlot[];
  onSlotSelect: (courtId: string, time: string, date: Date) => void;
  getSlotStatus: (courtId: string, time: string, date?: Date) => string;
  getBookedPlayerNames: (courtId: string, time: string) => string[];
}

export const WeekView = ({ 
  selectedDate, 
  courts, 
  timeSlots, 
  onSlotSelect, 
  getSlotStatus, 
  getBookedPlayerNames 
}: WeekViewProps) => {
  // Generate week days starting from Monday
  const getWeekDays = (date: Date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);
    
    return Array.from({ length: 7 }, (_, i) => addDays(startOfWeek, i));
  };

  const weekDays = getWeekDays(selectedDate);

  return (
    <div className="overflow-x-auto">
      <div className="min-w-[1400px]">
        <Card>
          <CardContent className="p-6">
            {/* Fixed Header with days */}
            <div className="sticky top-0 z-10 bg-background">
              <div className="grid gap-2 mb-4" style={{ gridTemplateColumns: '100px repeat(7, 1fr)' }}>
                <div className="p-2 font-semibold text-center bg-background">Zeit</div>
                {weekDays.map((day) => (
                  <div key={day.toISOString()} className="text-center p-2 bg-muted rounded">
                    <div className="font-semibold text-sm">
                      {format(day, 'EEEE', { locale: de })}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {format(day, 'dd.MM', { locale: de })}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Scrollable content area */}
            <div className="max-h-[600px] overflow-y-auto">
              {/* Time slots with all courts for each day */}
              {timeSlots.map((slot) => (
                <div key={slot.time} className="mb-3">
                  {/* Time header */}
                  <div className="grid gap-2 mb-1" style={{ gridTemplateColumns: '100px repeat(7, 1fr)' }}>
                    <div className="flex items-center justify-center p-2 bg-primary/10 rounded font-medium sticky left-0 z-5">
                      <Clock className="h-3 w-3 mr-1" />
                      <span className="text-sm">{slot.time}</span>
                    </div>
                    {weekDays.map((day) => (
                      <div key={`header-${slot.time}-${day.toISOString()}`}></div>
                    ))}
                  </div>
                  
                  {/* Court rows */}
                  {courts.map((court) => (
                    <div key={`${slot.time}-${court.id}`} className="grid gap-2 mb-1" style={{ gridTemplateColumns: '100px repeat(7, 1fr)' }}>
                      {/* Court label */}
                      <div className="flex items-center justify-center p-1 bg-muted/50 rounded text-xs font-medium sticky left-0 z-5">
                        Platz {court.number}
                      </div>
                      
                      {/* Day columns */}
                       {weekDays.map((day) => {
                         const slotStatus = court.locked ? 'locked' : getSlotStatus(court.id, slot.time, day);
                         const isDisabled = court.locked || slotStatus === 'booked' || slotStatus === 'unavailable';
                         const bookedPlayers = slotStatus === 'booked' ? getBookedPlayerNames(court.id, slot.time) : [];
                         
                         let buttonClasses = "w-full h-8 flex flex-col items-center justify-center p-1 text-xs";
                         
                         if (court.locked) {
                           buttonClasses += " bg-muted/50 text-muted-foreground border-muted";
                         } else if (slotStatus === 'unavailable') {
                           buttonClasses += " bg-muted/30 text-muted-foreground border-muted";
                         } else if (slotStatus === 'booked') {
                           buttonClasses += " bg-green-500 text-white border-green-600 hover:bg-green-600";
                         } else {
                           buttonClasses += " bg-background text-foreground border-border hover:bg-muted/50";
                         }
                        
                        return (
                          <Button
                            key={`${court.id}-${slot.time}-${day.toISOString()}`}
                            variant="outline"
                            disabled={isDisabled}
                            size="sm"
                            className={buttonClasses}
                            onClick={() => {
                              if (!isDisabled) {
                                onSlotSelect(court.id, slot.time, day);
                              }
                            }}
                          >
                             <div className="flex items-center gap-1">
                               <span className="font-medium">P{court.number}</span>
                               {court.locked && <Lock className="h-2 w-2" />}
                             </div>
                             
                             {slotStatus === 'unavailable' && (
                               <div className="text-xs line-through opacity-60">—</div>
                             )}
                             {slotStatus === 'booked' && (
                               <div className="text-xs font-medium truncate w-full max-w-[60px] text-center">
                                 {bookedPlayers[0]?.split(' ')[0]}
                               </div>
                             )}
                             {court.locked && (
                               <span className="text-xs text-muted-foreground">Gesperrt</span>
                             )}
                          </Button>
                        );
                      })}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};