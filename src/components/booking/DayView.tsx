import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Lock } from "lucide-react";
import { useState } from "react";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface DayViewProps {
  courts: Court[];
  timeSlots: TimeSlot[];
  onSlotSelect: (courtId: string, time: string) => void;
  getSlotStatus: (courtId: string, time: string, date?: Date) => string;
  getBookedPlayerNames: (courtId: string, time: string) => string[];
}

export const DayView = ({ 
  courts, 
  timeSlots, 
  onSlotSelect, 
  getSlotStatus, 
  getBookedPlayerNames 
}: DayViewProps) => {
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [selectedSlotInfo, setSelectedSlotInfo] = useState<{
    courtNumber: number;
    time: string;
    players: string[];
  } | null>(null);

  const sortedCourts = [...courts].sort((a, b) => a.number - b.number);
  
  // Debug logging
  console.log('DayView - Courts data:', courts);
  console.log('Locked courts with reasons:', courts.filter(c => c.locked && c.lock_reason));

  return (
    <TooltipProvider>
      <div className="overflow-x-auto">
        <div className="min-w-[400px]">
          <Card>
            <CardContent className="p-6">
              {/* Fixed Header with courts */}
              <div className="sticky top-0 z-10 bg-background">
                <div className="grid gap-2 mb-4" style={{ gridTemplateColumns: '80px 1fr' }}>
                  <div className="p-2 font-semibold text-center bg-background">Zeit</div>
                  <div className="text-center p-2 bg-muted rounded">
                    <div className="font-semibold text-sm">Heute</div>
                    <div className="grid gap-1 mt-1" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                      {sortedCourts.map((court) => (
                        <div key={court.id} className="text-xs px-1 h-8 bg-background rounded text-foreground font-medium flex items-center justify-center">
                          Platz {court.number}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Scrollable content area */}
              <div className="max-h-[600px] overflow-y-auto">
                {timeSlots.map((slot) => (
                  <div key={slot.time} className="grid gap-2 mb-2" style={{ gridTemplateColumns: '80px 1fr' }}>
                    {/* Time column */}
                    <div className="flex items-center justify-center p-2 bg-primary/10 rounded font-medium sticky left-0 z-5">
                      <span className="text-sm">{slot.time}</span>
                    </div>
                    
                    {/* Day column */}
                    <div className="grid gap-1 p-2 bg-muted rounded" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                       {sortedCourts.map((court) => {
                         const slotStatus = court.locked ? 'locked' : getSlotStatus(court.id, slot.time);
                         const isDisabled = court.locked || slotStatus === 'booked' || slotStatus === 'unavailable';
                         const bookedPlayers = slotStatus === 'booked' ? getBookedPlayerNames(court.id, slot.time) : [];
                         
                         let buttonClasses = "h-8 p-1 text-xs min-w-0 border flex items-center justify-center";
                         
                         if (court.locked) {
                           buttonClasses += " bg-muted/50 text-muted-foreground border-muted";
                         } else if (slotStatus === 'unavailable') {
                           buttonClasses += " bg-muted/30 text-muted-foreground border-muted";
                         } else if (slotStatus === 'booked') {
                           buttonClasses += " bg-green-500 text-white border-green-600 hover:bg-green-600";
                         } else {
                           buttonClasses += " bg-background text-foreground border-border hover:bg-muted/50";
                         }
                        
                        const buttonContent = (
                           <Button
                             key={`${court.id}-${slot.time}`}
                             variant="outline"
                             disabled={isDisabled}
                             size="sm"
                             className={buttonClasses}
                             onClick={() => {
                               if (slotStatus === 'booked') {
                                 setSelectedSlotInfo({
                                   courtNumber: court.number,
                                   time: slot.time,
                                   players: bookedPlayers
                                 });
                                 setShowPlayerDialog(true);
                               } else if (!isDisabled) {
                                 onSlotSelect(court.id, slot.time);
                               }
                             }}
                           >
                             {court.locked && <Lock className="h-2 w-2" />}
                             {slotStatus === 'unavailable' && (
                               <div className="text-xs line-through opacity-60">—</div>
                             )}
                             {slotStatus === 'booked' && (
                               <div className="text-xs font-medium truncate w-full px-0.5">
                                 {bookedPlayers[0]?.split(' ')[0]}
                               </div>
                             )}
                           </Button>
                        );

                        return court.locked && court.lock_reason ? (
                          <Tooltip key={`${court.id}-${slot.time}`}>
                            <TooltipTrigger asChild>
                              {buttonContent}
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{court.lock_reason}</p>
                            </TooltipContent>
                          </Tooltip>
                        ) : buttonContent;
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Player Info Dialog */}
        <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                Buchungsdetails - Platz {selectedSlotInfo?.courtNumber}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                Heute um {selectedSlotInfo?.time}
              </div>
              <div>
                <h4 className="font-medium mb-2">Gebuchte Spieler:</h4>
                <div className="space-y-1">
                  {selectedSlotInfo?.players.map((player, index) => (
                    <div key={index} className="text-sm bg-muted p-2 rounded">
                      {player}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
};