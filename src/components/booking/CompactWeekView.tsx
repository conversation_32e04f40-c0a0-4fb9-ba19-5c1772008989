import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Lock } from "lucide-react";
import { addDays, format } from "date-fns";
import { de } from "date-fns/locale";
import { useState } from "react";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

interface CompactWeekViewProps {
  selectedDate: Date;
  courts: Court[];
  timeSlots: TimeSlot[];
  onSlotSelect: (courtId: string, time: string, date: Date) => void;
  getSlotStatus: (courtId: string, time: string, date?: Date) => string;
  getBookedPlayerNames: (courtId: string, time: string, date?: Date) => string[];
}

export const CompactWeekView = ({ 
  selectedDate, 
  courts, 
  timeSlots, 
  onSlotSelect, 
  getSlotStatus, 
  getBookedPlayerNames 
}: CompactWeekViewProps) => {
  const [showPlayerDialog, setShowPlayerDialog] = useState(false);
  const [selectedSlotInfo, setSelectedSlotInfo] = useState<{
    courtNumber: number;
    time: string;
    date: Date;
    players: string[];
  } | null>(null);

  // Generate week days starting from Monday
  const getWeekDays = (date: Date) => {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    
    return Array.from({ length: 7 }, (_, i) => addDays(startOfWeek, i));
  };

  const weekDays = getWeekDays(selectedDate);
  const sortedCourts = [...courts].sort((a, b) => a.number - b.number);
  
  // Debug logging
  console.log('CompactWeekView - Courts data:', courts);
  console.log('Locked courts with reasons:', courts.filter(c => c.locked && c.lock_reason));

  return (
    <TooltipProvider>
      <div className="overflow-x-auto">
        <div className="min-w-[1000px]">
          <Card>
            <CardContent className="p-6">
              {/* Fixed Header with days */}
              <div className="sticky top-0 z-10 bg-background">
                <div className="grid gap-2 mb-4" style={{ gridTemplateColumns: '80px repeat(7, 1fr)' }}>
                  <div className="p-2 font-semibold text-center bg-background">Zeit</div>
                  {weekDays.map((day) => (
                    <div key={day.toISOString()} className="text-center p-2 bg-muted rounded">
                      <div className="flex items-center justify-center gap-2">
                        <div className="font-semibold text-sm">
                          {format(day, 'EEEE', { locale: de })}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(day, 'dd.MM', { locale: de })}
                        </div>
                      </div>
                      <div className="grid gap-1 mt-1" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                        {sortedCourts.map((court) => (
                          <div key={court.id} className="text-xs px-1 h-6 bg-background rounded text-foreground font-medium flex items-center justify-center">
                            {court.number}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Scrollable content area */}
              <div className="max-h-[600px] overflow-y-auto">
                {timeSlots.map((slot) => (
                  <div key={slot.time} className="grid gap-2 mb-2" style={{ gridTemplateColumns: '80px repeat(7, 1fr)' }}>
                    {/* Time column */}
                    <div className="flex items-center justify-center p-2 bg-primary/10 rounded font-medium sticky left-0 z-5">
                      <span className="text-sm">{slot.time}</span>
                    </div>
                    
                    {/* Day columns */}
                     {weekDays.map((day) => (
                      <div key={`${slot.time}-${day.toISOString()}`} className="grid gap-1 p-2 bg-muted rounded" style={{ gridTemplateColumns: `repeat(${sortedCourts.length}, 1fr)` }}>
                         {sortedCourts.map((court) => {
                           const slotStatus = court.locked ? 'locked' : getSlotStatus(court.id, slot.time, day);
                           const isDisabled = court.locked || slotStatus === 'booked' || slotStatus === 'unavailable';
                           const bookedPlayers = slotStatus === 'booked' ? getBookedPlayerNames(court.id, slot.time, day) : [];
                           
                           let buttonClasses = "h-14 p-1 text-[11px] min-w-0 border flex flex-col items-stretch leading-none";
                           
                           if (court.locked) {
                             buttonClasses += " bg-muted/50 text-muted-foreground border-muted";
                           } else if (slotStatus === 'unavailable') {
                             buttonClasses += " bg-muted/30 text-muted-foreground border-muted";
                           } else if (slotStatus === 'booked') {
                             buttonClasses += " bg-green-500 text-white border-green-600 hover:bg-green-600";
                           } else {
                             buttonClasses += " bg-background text-foreground border-border hover:bg-muted/50";
                           }
                          
                          const buttonContent = (
                             <Button
                               key={`${court.id}-${slot.time}-${day.toISOString()}`}
                               variant="outline"
                               disabled={isDisabled}
                               size="sm"
                                className={buttonClasses}
                                title={slotStatus === 'booked' ? bookedPlayers.join(', ') : (court.locked && court.lock_reason ? court.lock_reason : undefined)}
                               onClick={() => {
                                 if (slotStatus === 'booked') {
                                   setSelectedSlotInfo({
                                     courtNumber: court.number,
                                     time: slot.time,
                                     date: day,
                                     players: bookedPlayers
                                   });
                                   setShowPlayerDialog(true);
                                 } else if (!isDisabled) {
                                   onSlotSelect(court.id, slot.time, day);
                                 }
                               }}
                             >
                               {court.locked && <Lock className="h-2 w-2" />}
                               {slotStatus === 'unavailable' && (
                                 <div className="text-xs line-through opacity-60">—</div>
                               )}
                                {slotStatus === 'booked' && (
                                  <div className="w-full h-full flex flex-col justify-between">
                                    <div className="px-0.5">
                                      <span className="block text-[11px] font-semibold overflow-hidden text-clip whitespace-nowrap leading-none text-left">
                                        {(bookedPlayers[0] || '').split(' ').filter(Boolean)[0] || ''}
                                      </span>
                                      {(() => { const parts = (bookedPlayers[0] || '').trim().split(' ').filter(Boolean); const ln = parts.length > 1 ? parts[parts.length - 1] : ''; return ln ? (
                                        <span className="block text-[11px] font-medium overflow-hidden text-clip whitespace-nowrap leading-none text-left">{ln}</span>
                                      ) : null; })()}
                                    </div>
                                    {bookedPlayers[1] && (
                                      <>
                                      <div className="h-px bg-border mx-0.5" />
                                      <span className="text-[11px] font-medium overflow-hidden text-clip whitespace-nowrap leading-none px-0.5 text-right">{bookedPlayers[1].split(' ')[0]}</span>
                                      </>
                                    )}
                                  </div>
                                )}
                             </Button>
                          );

                          return court.locked && court.lock_reason ? (
                            <Tooltip key={`${court.id}-${slot.time}-${day.toISOString()}`}>
                              <TooltipTrigger asChild>
                                {buttonContent}
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{court.lock_reason}</p>
                              </TooltipContent>
                            </Tooltip>
                          ) : buttonContent;
                        })}
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Player Info Dialog */}
        <Dialog open={showPlayerDialog} onOpenChange={setShowPlayerDialog}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                Buchungsdetails - Platz {selectedSlotInfo?.courtNumber}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-3">
              <div className="text-sm text-muted-foreground">
                {selectedSlotInfo && format(selectedSlotInfo.date, 'EEEE, dd.MM.yyyy', { locale: de })} um {selectedSlotInfo?.time}
              </div>
              <div>
                <h4 className="font-medium mb-2">Gebuchte Spieler:</h4>
                <div className="space-y-1">
                  {selectedSlotInfo?.players.map((player, index) => (
                    <div key={index} className="text-sm bg-muted p-2 rounded">
                      {player}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
};