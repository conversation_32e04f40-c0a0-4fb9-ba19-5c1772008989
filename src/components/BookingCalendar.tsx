import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ChevronLeft, ChevronRight, Calendar } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { ViewSelector } from "./booking/ViewSelector";
import { DayView } from "./booking/DayView";
import { WeekView } from "./booking/WeekView";
import { CompactWeekView } from "./booking/CompactWeekView";
import { BookingDetailsDialog, type BookingDetails } from "@/components/booking/BookingDetailsDialog";

interface Court {
  id: string;
  number: number;
  locked: boolean;
  lock_reason: string | null;
}

interface TimeSlot {
  time: string;
  available: boolean;
  price?: number;
}

const BookingCalendar = () => {
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedCourt, setSelectedCourt] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [showBookingDialog, setShowBookingDialog] = useState(false);
  const [courts, setCourts] = useState<Court[]>([]);
  const [courtAvailability, setCourtAvailability] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [view, setView] = useState<'day' | 'week' | 'compact'>('compact');
  const viewAutoSet = useRef(false);
  const { toast } = useToast();
  useEffect(() => {
    loadCourts();
    loadCourtAvailability();
  }, []);

  // Reload availability when component regains focus or when court availability is updated
  useEffect(() => {
    const handleFocus = () => {
      loadCourtAvailability();
    };
    
    const handleCourtAvailabilityUpdate = (event: CustomEvent) => {
      console.log('Court availability updated, reloading...', event.detail);
      setCourtAvailability(event.detail || []);
    };
    
    window.addEventListener('focus', handleFocus);
    window.addEventListener('courtAvailabilityUpdated', handleCourtAvailabilityUpdate as EventListener);
    
    return () => {
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('courtAvailabilityUpdated', handleCourtAvailabilityUpdate as EventListener);
    };
  }, []);

  const loadCourts = async () => {
    try {
      const { data, error } = await supabase
        .from("courts")
        .select("*")
        .order("number");

      if (error) throw error;
      setCourts(data || []);
      if (!viewAutoSet.current) {
        const count = data?.length ?? 0;
        if (count > 6) {
          setView('day');
        } else {
          setView('compact');
        }
        viewAutoSet.current = true;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load courts",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadCourtAvailability = async () => {
    try {
      const { data, error } = await supabase
        .from("court_availability")
        .select("*");

      if (error) throw error;
      setCourtAvailability(data || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load court availability",
        variant: "destructive"
      });
    }
  };

  // Generate dynamic time slots based on actual court availability
  const generateTimeSlots = (): TimeSlot[] => {
    if (courtAvailability.length === 0) {
      // Fallback to default times if no availability data
      return [
        { time: '07:00', available: true, price: 20 },
        { time: '08:00', available: true, price: 20 },
        { time: '09:00', available: true, price: 25 },
        { time: '10:00', available: true, price: 25 },
        { time: '11:00', available: false },
        { time: '12:00', available: true, price: 30 },
        { time: '13:00', available: true, price: 30 },
        { time: '14:00', available: true, price: 30 },
        { time: '15:00', available: false },
        { time: '16:00', available: true, price: 35 },
        { time: '17:00', available: true, price: 35 },
        { time: '18:00', available: true, price: 35 },
        { time: '19:00', available: true, price: 30 },
        { time: '20:00', available: true, price: 25 },
        { time: '21:00', available: true, price: 25 },
      ];
    }

    // Find the earliest start time and latest end time across all courts
    let earliestStart = '23:59';
    let latestEnd = '00:00';

    courtAvailability.forEach(availability => {
      if (availability.start_time < earliestStart) {
        earliestStart = availability.start_time;
      }
      if (availability.end_time > latestEnd) {
        latestEnd = availability.end_time;
      }
    });

    // Generate hourly slots from earliest to latest
    const slots: TimeSlot[] = [];
    const startHour = parseInt(earliestStart.split(':')[0]);
    const endHour = parseInt(latestEnd.split(':')[0]);

    for (let hour = startHour; hour <= endHour; hour++) {
      const timeString = `${hour.toString().padStart(2, '0')}:00`;
      slots.push({
        time: timeString,
        available: true,
        price: hour < 9 || hour > 19 ? 20 : hour < 12 || hour > 17 ? 25 : hour < 16 ? 30 : 35
      });
    }

    return slots;
  };

  const timeSlots = generateTimeSlots();

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-tennis-green text-white';
      case 'booked':
        return 'bg-destructive text-white';
      case 'maintenance':
        return 'bg-muted text-muted-foreground';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  const isTimeSlotAvailable = (courtId: string, time: string, date: Date) => {
    const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const availability = courtAvailability.find(a => a.court_id === courtId);
    
    if (!availability) return false;
    
    // Check if this day is included in the court's available days
    if (!availability.days_of_week.includes(dayOfWeek)) return false;
    
    // Check if time is within availability window
    const slotTime = time + ':00'; // Convert to HH:MM:SS format
    const timeInMinutes = parseInt(time.split(':')[0]) * 60 + parseInt(time.split(':')[1]);
    const startInMinutes = parseInt(availability.start_time.split(':')[0]) * 60 + parseInt(availability.start_time.split(':')[1]);
    const endInMinutes = parseInt(availability.end_time.split(':')[0]) * 60 + parseInt(availability.end_time.split(':')[1]);
    
    return timeInMinutes >= startInMinutes && timeInMinutes < endInMinutes;
  };

  const getSlotStatus = (courtId: string, time: string, date?: Date) => {
    const checkDate = date || selectedDate;
    
    // Check if time slot is within court availability
    if (!isTimeSlotAvailable(courtId, time, checkDate)) {
      return 'unavailable';
    }
    
    // In real implementation, check actual bookings from database
    return 'available';
  };

  const getBookedPlayerNames = (courtId: string, time: string) => {
    // No demo bookings - return empty array
    return [];
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(selectedDate);
    const daysToMove = view === 'week' ? 7 : 1;
    if (direction === 'prev') {
      newDate.setDate(newDate.getDate() - daysToMove);
    } else {
      newDate.setDate(newDate.getDate() + daysToMove);
    }
    setSelectedDate(newDate);
  };

  const handleSlotSelect = (courtId: string, time: string, date?: Date) => {
    setSelectedCourt(courtId);
    setSelectedTime(time);
    if (date) {
      setSelectedDate(date);
    }
    setShowBookingDialog(true);
  };

  const handleConfirmBooking = async (details: BookingDetails) => {
    try {
      const { data: auth } = await supabase.auth.getUser();
      const userId = auth?.user?.id;

      if (!userId) {
        toast({ title: "Anmeldung erforderlich", description: "Bitte melde dich an, um eine Buchung zu erstellen.", variant: "destructive" });
        return;
      }

      let playerName = "Unbekannter Spieler";
      if (userId) {
        const { data: profile } = await supabase
          .from("profiles")
          .select("first_name,last_name,email")
          .eq("id", userId)
          .maybeSingle();
        if (profile?.first_name || profile?.last_name) {
          playerName = `${profile.first_name ?? ""} ${profile.last_name ?? ""}`.trim();
        } else if (auth.user.email) {
          playerName = auth.user.email;
        }
      }

      if (!selectedCourt) {
        toast({ title: "Fehler", description: "Kein Platz ausgewählt", variant: "destructive" });
        return;
      }

      // Partnerliste als String
      const partnerStr = details.partners.map((p) => p.label).join(", ");

      // Eigenen Account ermitteln (Standard: Selbstbuchung)
      const { data: account } = await supabase
        .from("accounts")
        .select("id")
        .eq("user_id", userId)
        .limit(1)
        .maybeSingle();

      // Start/End als Timestamp (timestamptz) ableiten
      const makeDateTime = (date: Date, time: string) => {
        const [h, m] = time.split(":").map(Number);
        const dt = new Date(date);
        dt.setHours(h || 0, m || 0, 0, 0);
        return dt.toISOString();
      };
      const startAt = makeDateTime(details.date, details.startTime);
      const endAt = makeDateTime(details.date, details.endTime);

      const payload: any = {
        court_id: selectedCourt,
        booking_date: format(details.date, "yyyy-MM-dd"),
        start_time: `${details.startTime}:00`,
        end_time: `${details.endTime}:00`,
        player_name: playerName,
        partner_name: partnerStr || null,
        created_by: userId,
        start_at: startAt,
        end_at: endAt,
      };

      const actingId = (details as any)?.actingAccountId ?? account?.id;
      const forId = (details as any)?.bookedForAccountId ?? actingId;

      if (actingId) {
        payload.acting_account_id = actingId;
        payload.booked_for_account_id = forId || actingId;
      }

      const { error } = await supabase.from("bookings").insert([payload]);
      if (error) throw error;

      toast({ title: "Buchung erstellt", description: `Platz gebucht am ${format(details.date, "PP", { locale: de })} um ${details.startTime}` });
      setShowBookingDialog(false);
    } catch (e: any) {
      console.error(e);
      toast({ title: "Fehler bei der Buchung", description: e?.message || "Unbekannter Fehler", variant: "destructive" });
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">Buche Deinen Platz</h2>
        <p className="text-muted-foreground">Wähle ein Datum, einen Platz und eine Uhrzeit für deine Reservierung</p>
      </div>

      {/* View Selector */}
      <ViewSelector view={view} onViewChange={setView} />

      {/* Date Navigation */}
      <Card>
        <CardHeader className="py-2">
          <div className="flex items-center justify-between w-1/2 mx-auto">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigateDate('prev')}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="ghost" className="font-normal">
                  <Calendar className="mr-2 h-4 w-4" />
                  <span className="text-xl font-semibold">
                    {view === 'week' ? `Woche vom ${formatDate(selectedDate)}` : formatDate(selectedDate)}
                  </span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="center">
                <CalendarComponent
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  initialFocus
                  className="p-3 pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigateDate('next')}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Courts View */}
      {view === 'day' ? (
        <DayView
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      ) : view === 'week' ? (
        <WeekView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      ) : (
        <CompactWeekView
          selectedDate={selectedDate}
          courts={courts}
          timeSlots={timeSlots}
          onSlotSelect={handleSlotSelect}
          getSlotStatus={getSlotStatus}
          getBookedPlayerNames={getBookedPlayerNames}
        />
      )}

      {/* Booking Summary */}
      {(selectedCourt && selectedTime) && (
        <Card>
          <CardContent className="p-6">
            <div className="p-4 bg-gradient-court rounded-lg border">
              <h4 className="font-semibold text-tennis-green mb-2">Ausgewählte Buchung</h4>
              <p className="text-sm text-muted-foreground mb-4">
                Platz {courts.find(c => c.id === selectedCourt)?.number} • {formatDate(selectedDate)} um {selectedTime} • €30
              </p>
              <Button variant="tennis" className="w-full">
                Buchung Abschließen
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Buchungs-Dialog */}
      <BookingDetailsDialog
        open={showBookingDialog}
        onOpenChange={setShowBookingDialog}
        courtNumber={courts.find((c) => c.id === selectedCourt)?.number}
        defaultDate={selectedDate}
        defaultStartTime={selectedTime || "08:00"}
        defaultEndTime={selectedTime ? (() => { const [h,m] = selectedTime.split(":").map(Number); const end = new Date(); end.setHours(h); end.setMinutes((m||0)+60); return `${String(end.getHours()).padStart(2,'0')}:${String(end.getMinutes()).padStart(2,'0')}` })() : "09:00"}
        onConfirm={handleConfirmBooking}
      />
    </div>
  );
};

export default BookingCalendar;
