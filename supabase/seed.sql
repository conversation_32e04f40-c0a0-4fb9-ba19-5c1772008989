SET session_replication_role = replica;

--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: audit_log_entries; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."audit_log_entries" ("instance_id", "id", "payload", "created_at", "ip_address") VALUES
	('********-0000-0000-0000-********0000', '5102b40e-1a01-4805-b979-68e02028f0e4', '{"action":"user_signedup","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team","traits":{"provider":"email"}}', '2025-08-07 14:25:54.225606+00', ''),
	('********-0000-0000-0000-********0000', 'abe1e3a8-005a-4c05-8b2f-9f1b60a8d24c', '{"action":"login","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-07 14:25:54.24106+00', ''),
	('********-0000-0000-0000-********0000', 'f1f80dc1-00e7-4913-8e0e-3bbcdb0a1ce9', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 15:23:55.874746+00', ''),
	('********-0000-0000-0000-********0000', '9e50d2ce-12a6-487d-b3f4-ca852c939adf', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 15:23:55.908076+00', ''),
	('********-0000-0000-0000-********0000', 'fecfe318-50e6-4b2d-9b76-3d25d730e4fe', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 16:23:13.835236+00', ''),
	('********-0000-0000-0000-********0000', '86d8871d-7c28-4f94-9126-68d3161f0a4f', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 16:23:13.848094+00', ''),
	('********-0000-0000-0000-********0000', 'f1a0f282-520b-4dd1-8a68-47162a381deb', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 19:07:22.933336+00', ''),
	('********-0000-0000-0000-********0000', 'c4c92cc4-9f54-42bf-8a3c-414cae8694ed', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 19:07:22.941387+00', ''),
	('********-0000-0000-0000-********0000', '5a6d73a0-5d69-46f3-8336-f71a462773e3', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 20:07:46.472776+00', ''),
	('********-0000-0000-0000-********0000', 'bf956f70-cb51-4cd4-b0bc-a82333f25ca7', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 20:07:46.48191+00', ''),
	('********-0000-0000-0000-********0000', '1de4df1e-e671-439b-89a3-b8e16c3077fb', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 21:09:38.44676+00', ''),
	('********-0000-0000-0000-********0000', 'a460156c-457c-4ff9-b9ac-da251e9c2337', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-07 21:09:38.46188+00', ''),
	('********-0000-0000-0000-********0000', 'a471a23c-df9c-465a-9f42-297332be81e3', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 07:10:19.299593+00', ''),
	('********-0000-0000-0000-********0000', '47f24842-26da-44ae-855c-631c821cd037', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 07:10:19.309105+00', ''),
	('********-0000-0000-0000-********0000', '3724c992-6df7-4804-9110-7129aa42130b', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 08:08:44.89894+00', ''),
	('********-0000-0000-0000-********0000', '00fc3f1c-7fbb-4232-8321-84c2e605db05', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 08:08:44.911407+00', ''),
	('********-0000-0000-0000-********0000', '23296a8d-26ef-4b2f-9e4b-14fd910832a4', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 09:06:58.111559+00', ''),
	('********-0000-0000-0000-********0000', '328121a1-0c21-4e87-971b-55cb6951d891', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 09:06:58.116287+00', ''),
	('********-0000-0000-0000-********0000', 'cb4340f5-304a-4f59-8a15-325673660958', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 10:05:15.452727+00', ''),
	('********-0000-0000-0000-********0000', '26ebd8cf-3b58-42e0-9e6b-fc697965c62b', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 10:05:15.462102+00', ''),
	('********-0000-0000-0000-********0000', 'fac85db1-41ea-4f62-941a-920cf5db808f', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 11:03:53.933643+00', ''),
	('********-0000-0000-0000-********0000', '7781d7a8-e2ba-4437-ac6c-4e1e8a8a2d1b', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 11:03:53.944459+00', ''),
	('********-0000-0000-0000-********0000', '1c7f5c06-b7db-4055-af0f-31e6f144a4a6', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 12:02:16.625143+00', ''),
	('********-0000-0000-0000-********0000', 'da09aa56-ffd0-455c-a74b-d40860f6e7c9', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 12:02:16.637121+00', ''),
	('********-0000-0000-0000-********0000', '3449a39b-8b77-4564-9b85-2351d1a850c9', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 13:01:06.914159+00', ''),
	('********-0000-0000-0000-********0000', '11754e8b-b859-4f02-8721-ec650d6a773e', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 13:01:06.924259+00', ''),
	('********-0000-0000-0000-********0000', '1cf6698c-187a-4ff0-8b89-2cee13a8ba5f', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 14:01:06.276545+00', ''),
	('********-0000-0000-0000-********0000', '1bbc62ee-5c39-44aa-bc9c-32e2c25481da', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 14:01:06.290859+00', ''),
	('********-0000-0000-0000-********0000', 'f57ecc84-1955-487e-b403-f1aad46a1ea1', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 14:59:14.984262+00', ''),
	('********-0000-0000-0000-********0000', '0c54761c-5dad-4872-a127-178e8ef0e0c6', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-08 14:59:14.992725+00', ''),
	('********-0000-0000-0000-********0000', 'b4e91f54-82e3-4228-bbf6-1786305453d5', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 05:54:03.658421+00', ''),
	('********-0000-0000-0000-********0000', 'd2eb70e5-6a4d-44bc-a35f-2cdb4aec1940', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 05:54:03.667288+00', ''),
	('********-0000-0000-0000-********0000', '4f3ed4a2-1465-4313-9659-0ee60a8888ee', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 06:52:54.770382+00', ''),
	('********-0000-0000-0000-********0000', '82c69b6c-1a64-4fb0-9505-9d122e77fb32', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 06:52:54.788978+00', ''),
	('********-0000-0000-0000-********0000', '78f18e10-8426-44c9-97a4-f1f598c7799e', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 08:09:48.560522+00', ''),
	('********-0000-0000-0000-********0000', '9a7e2ac0-4ab4-40e0-a2bf-23c3e31cd473', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 08:09:48.568121+00', ''),
	('********-0000-0000-0000-********0000', '887f912f-9733-4dbc-8ae4-4be32f84d504', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 11:04:33.575562+00', ''),
	('********-0000-0000-0000-********0000', '71aa2679-9356-4229-9232-08b095257e8a', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 11:04:33.586739+00', ''),
	('********-0000-0000-0000-********0000', '799c5ec5-2ed7-4dba-95f3-dc5f2d295ba6', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 12:58:56.220003+00', ''),
	('********-0000-0000-0000-********0000', '2097cd93-4f4d-42a9-91d5-be96ae58b05c', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 12:58:56.229442+00', ''),
	('********-0000-0000-0000-********0000', '9d047dd2-f7be-45fa-800e-a9486da04cb3', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 13:57:08.135576+00', ''),
	('********-0000-0000-0000-********0000', '7a63acd9-a632-4eae-8ed5-e2913a0b999d', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 13:57:08.148229+00', ''),
	('********-0000-0000-0000-********0000', '9a0ed677-f2f4-4301-837f-476165473fbf', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 14:59:37.98672+00', ''),
	('********-0000-0000-0000-********0000', 'cc9c97f0-45b0-4a9b-9a4e-d291451f0361', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 14:59:38.011537+00', ''),
	('********-0000-0000-0000-********0000', 'f9c50b81-13f8-45bd-9668-14e5fa069b09', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 16:26:07.175735+00', ''),
	('********-0000-0000-0000-********0000', 'a01c1a23-a671-4982-a22d-9e3c05d2cdb0', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 16:26:07.20006+00', ''),
	('********-0000-0000-0000-********0000', '703cbaad-7bb3-483d-aab7-0a87a8aad403', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 16:44:37.19762+00', ''),
	('********-0000-0000-0000-********0000', '3d06a51b-a42f-454e-97b3-69b3ee4cc054', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 18:49:55.824791+00', ''),
	('********-0000-0000-0000-********0000', '33c426c9-fabd-4a5b-9280-98a080ec3c4c', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 18:49:55.846669+00', ''),
	('********-0000-0000-0000-********0000', 'e0acf84d-2fc1-40ef-8522-b9319a7aa475', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 20:14:57.3984+00', ''),
	('********-0000-0000-0000-********0000', '63752751-e1b2-4f20-8141-baa1c81075a6', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 20:14:57.41851+00', ''),
	('********-0000-0000-0000-********0000', '73c5d8e2-4475-483f-812e-d2e24bf65f85', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 21:13:22.568318+00', ''),
	('********-0000-0000-0000-********0000', 'be213997-9e24-4148-81de-ebd0acea475e', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 21:13:22.580233+00', ''),
	('********-0000-0000-0000-********0000', 'ec275ed1-703a-4c81-aa18-95cebc745194', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 22:11:28.635075+00', ''),
	('********-0000-0000-0000-********0000', 'ed180d01-af3e-4f7b-b021-77fb0962cbb0', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-09 22:11:28.647675+00', ''),
	('********-0000-0000-0000-********0000', 'c415a600-d9e2-4713-9477-6e03afa3563a', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 05:08:28.802983+00', ''),
	('********-0000-0000-0000-********0000', 'c5bd39b7-fc49-4861-9db3-df52aef6ed76', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 05:08:28.824881+00', ''),
	('********-0000-0000-0000-********0000', '753bd502-69ce-4a7b-ad11-e7249fa37c0f', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 06:06:30.975126+00', ''),
	('********-0000-0000-0000-********0000', '8a8f8b6a-9a35-4a36-a922-3c6ff1f27c08', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 06:06:30.993109+00', ''),
	('********-0000-0000-0000-********0000', '84eb5f0f-4d83-4218-ad47-99a9ee5aad24', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 08:53:27.911885+00', ''),
	('********-0000-0000-0000-********0000', 'a7cf0ff3-db77-4240-98fb-7e94a5efedf4', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 08:53:27.937039+00', ''),
	('********-0000-0000-0000-********0000', '2be36599-3e54-4308-a976-17f4f41862c4', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 09:51:54.709871+00', ''),
	('********-0000-0000-0000-********0000', '6ab27e07-6d6e-4771-a7ae-16ee29486a58', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 09:51:54.73165+00', ''),
	('********-0000-0000-0000-********0000', '8d12d1d3-0d7e-4a76-9628-37ffb4c3a636', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 10:50:39.910457+00', ''),
	('********-0000-0000-0000-********0000', '10d9e2f4-6115-49b4-84d4-2072b8b19ce9', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 10:50:39.927492+00', ''),
	('********-0000-0000-0000-********0000', '9ed48152-2155-431e-a7d1-11eea6f93bbb', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 11:48:49.157143+00', ''),
	('********-0000-0000-0000-********0000', '9aded26c-2bf3-448f-a5ee-05e2562e79de', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-10 11:48:49.171182+00', ''),
	('********-0000-0000-0000-********0000', 'a3cb8875-f657-4bd8-9b9f-9c9fb7062c58', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-11 07:59:42.510273+00', ''),
	('********-0000-0000-0000-********0000', '471c09c4-2ac5-444f-82e2-52741cdf3808', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-11 07:59:42.521851+00', ''),
	('********-0000-0000-0000-********0000', 'a6de5b46-751c-428c-9cba-7991c416d932', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-11 09:08:00.524469+00', ''),
	('********-0000-0000-0000-********0000', '1845970b-7271-489f-b967-9bfbf4d9c779', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-11 09:08:00.546443+00', ''),
	('********-0000-0000-0000-********0000', '519895c4-781b-4ef0-9a27-bf3b9b889243', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-12 13:06:46.400179+00', ''),
	('********-0000-0000-0000-********0000', '1218d001-488c-4b9f-932c-58af1a5123f7', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-12 13:06:46.416504+00', ''),
	('********-0000-0000-0000-********0000', '0b71dc16-1f4a-48ec-b4f9-0ec6b12bae28', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 07:57:14.748798+00', ''),
	('********-0000-0000-0000-********0000', 'c246a0e1-c2a4-4db3-9f05-29ac975ffce6', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 07:57:14.758382+00', ''),
	('********-0000-0000-0000-********0000', '92b694c8-9a5b-45fc-aa45-3c5477fd17d5', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 08:55:33.211046+00', ''),
	('********-0000-0000-0000-********0000', 'da37fc65-f472-4643-83b2-f82bf0e9b646', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 08:55:33.236462+00', ''),
	('********-0000-0000-0000-********0000', '4742cc5b-f552-4b65-a0c3-7cb214b496a2', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 10:26:41.954093+00', ''),
	('********-0000-0000-0000-********0000', '4fa789fb-11d8-4fb2-b573-890a8dc07fe6', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 10:26:41.967634+00', ''),
	('********-0000-0000-0000-********0000', '26ef4e0e-a7a4-47c5-b697-4ae63912a08d', '{"action":"user_signedup","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"team","traits":{"provider":"email"}}', '2025-08-13 11:25:35.920528+00', ''),
	('********-0000-0000-0000-********0000', '9cb59bc3-734e-4648-a27b-5ae4b3ad175f', '{"action":"login","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-13 11:25:35.950611+00', ''),
	('********-0000-0000-0000-********0000', 'd9afbfd9-fb85-4374-b2f4-05b0637897aa', '{"action":"login","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-13 11:26:25.17237+00', ''),
	('********-0000-0000-0000-********0000', '7ba2ea5e-0f34-4d01-a6b1-a6da0a32071e', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 11:28:38.809013+00', ''),
	('********-0000-0000-0000-********0000', '9b704b1b-3bde-40c8-8597-76674b38c81c', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 11:28:38.810281+00', ''),
	('********-0000-0000-0000-********0000', 'ed1092f3-9d3d-44b9-9dca-6dd23e405a22', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 14:38:36.671869+00', ''),
	('********-0000-0000-0000-********0000', '3e81693a-3ac8-4c55-a81c-ded7d598c8e3', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 14:38:36.681341+00', ''),
	('********-0000-0000-0000-********0000', 'b66366f3-0be3-49ee-91e5-6a806504536c', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 17:04:08.135232+00', ''),
	('********-0000-0000-0000-********0000', '39083051-914c-4fde-ace2-6dc8427bde40', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-13 17:04:08.149887+00', ''),
	('********-0000-0000-0000-********0000', '39f5c165-0ac0-477b-bd45-fa38de148ecd', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 07:26:13.591426+00', ''),
	('********-0000-0000-0000-********0000', '7a7ef40a-9c6f-488c-9834-4e1531eebcfd', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 07:26:13.618313+00', ''),
	('********-0000-0000-0000-********0000', '8436cd46-d1fc-4eea-be4a-3d4e4c4f5100', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 09:34:34.664111+00', ''),
	('********-0000-0000-0000-********0000', '49941b37-a5c6-4b90-9ce3-62d8713b1dd7', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 09:34:34.674751+00', ''),
	('********-0000-0000-0000-********0000', 'd8a1356e-fed3-4cf3-9fd6-c7a9aed211f0', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 10:43:42.263274+00', ''),
	('********-0000-0000-0000-********0000', '15217059-0d9c-4e61-b65c-836394918147', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 10:43:42.288469+00', ''),
	('********-0000-0000-0000-********0000', '4a8f855d-526b-4381-b51e-643a5fbdf3bb', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 15:21:10.35361+00', ''),
	('********-0000-0000-0000-********0000', '4dc7428b-64ad-4fa9-839f-bd7722f03bdb', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 15:21:10.361493+00', ''),
	('********-0000-0000-0000-********0000', '6b7e5e3a-68e9-4a6e-a4ff-8b3b81bb615e', '{"action":"token_refreshed","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 17:07:45.85263+00', ''),
	('********-0000-0000-0000-********0000', '34098f66-1c0d-4cd5-9dad-e45888bbbc3d', '{"action":"token_revoked","actor_id":"c7e745d1-f09e-4819-936a-a15c06171e6d","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 17:07:45.879713+00', ''),
	('********-0000-0000-0000-********0000', '659cc8c3-980a-4f4b-b36b-862ab7508597', '{"action":"user_signedup","actor_id":"********-0000-0000-0000-********0000","actor_username":"service_role","actor_via_sso":false,"log_type":"team","traits":{"provider":"email","user_email":"<EMAIL>","user_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","user_phone":""}}', '2025-08-14 17:10:13.449631+00', ''),
	('********-0000-0000-0000-********0000', '7bd62bd0-bcd2-41d1-b226-0777a063ec9f', '{"action":"login","actor_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-14 17:18:54.507762+00', ''),
	('********-0000-0000-0000-********0000', '29e01fdc-01e4-4bef-a8df-3c5191f279d1', '{"action":"login","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-14 18:12:46.040564+00', ''),
	('********-0000-0000-0000-********0000', 'e09e9619-a28d-47d6-b4d4-da81b1b09642', '{"action":"login","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"account","traits":{"provider":"email"}}', '2025-08-14 18:19:43.792067+00', ''),
	('********-0000-0000-0000-********0000', '5ed8ad97-73ad-4fd5-b905-4dbc144a1548', '{"action":"token_refreshed","actor_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 18:25:28.310784+00', ''),
	('********-0000-0000-0000-********0000', 'd41c2a42-3356-4247-bd29-b4810ab0fb90', '{"action":"token_revoked","actor_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 18:25:28.324225+00', ''),
	('********-0000-0000-0000-********0000', '35a553cd-6240-421b-8e6b-2a710c4d6a61', '{"action":"token_refreshed","actor_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 19:40:29.560096+00', ''),
	('********-0000-0000-0000-********0000', '66e00d81-dc98-434d-82fd-911be22b7670', '{"action":"token_revoked","actor_id":"939e5d34-2907-4e99-b0b1-24ff4ec72339","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 19:40:29.583221+00', ''),
	('********-0000-0000-0000-********0000', '0b6bc556-2909-476f-a13b-3187f7032f87', '{"action":"token_refreshed","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 19:56:43.73484+00', ''),
	('********-0000-0000-0000-********0000', '56e951fd-9766-4a28-938f-00e96d23bee8', '{"action":"token_revoked","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 19:56:43.740397+00', ''),
	('********-0000-0000-0000-********0000', '7ffbb20a-73ab-4488-a441-39586d997401', '{"action":"user_recovery_requested","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"user"}', '2025-08-14 19:57:39.52256+00', ''),
	('********-0000-0000-0000-********0000', '356bd0b0-4448-4fb8-8120-f8ee538f82e0', '{"action":"token_refreshed","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 20:03:08.554604+00', ''),
	('********-0000-0000-0000-********0000', 'cd1657a8-b0b7-460a-9b33-1da46e0f89d6', '{"action":"token_revoked","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-14 20:03:08.556207+00', ''),
	('********-0000-0000-0000-********0000', 'bc4a5bfb-dc05-4b24-845b-33f9b2e4303b', '{"action":"token_refreshed","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-15 09:05:25.163077+00', ''),
	('********-0000-0000-0000-********0000', '2f0fa91c-84cc-4cd7-af29-04e649b2e10f', '{"action":"token_revoked","actor_id":"f87864aa-6f6e-4ab7-972d-ad442155d4f3","actor_username":"<EMAIL>","actor_via_sso":false,"log_type":"token"}', '2025-08-15 09:05:25.173745+00', '');


--
-- Data for Name: flow_state; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."users" ("instance_id", "id", "aud", "role", "email", "encrypted_password", "email_confirmed_at", "invited_at", "confirmation_token", "confirmation_sent_at", "recovery_token", "recovery_sent_at", "email_change_token_new", "email_change", "email_change_sent_at", "last_sign_in_at", "raw_app_meta_data", "raw_user_meta_data", "is_super_admin", "created_at", "updated_at", "phone", "phone_confirmed_at", "phone_change", "phone_change_token", "phone_change_sent_at", "email_change_token_current", "email_change_confirm_status", "banned_until", "reauthentication_token", "reauthentication_sent_at", "is_sso_user", "deleted_at", "is_anonymous") VALUES
	('********-0000-0000-0000-********0000', '939e5d34-2907-4e99-b0b1-24ff4ec72339', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$oj4e1XtxEooqxrFqDoTvLOTOskqWipHeU3aUk1kBhpTnJenAp98J.', '2025-08-14 17:10:13.451951+00', NULL, '', NULL, '', NULL, '', '', NULL, '2025-08-14 17:18:54.51135+00', '{"provider": "email", "providers": ["email"]}', '{"email_verified": true}', NULL, '2025-08-14 17:10:13.424861+00', '2025-08-14 19:40:29.612441+00', NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, NULL, false),
	('********-0000-0000-0000-********0000', 'c7e745d1-f09e-4819-936a-a15c06171e6d', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$6gUE1MdY4esZN94TsaDKOOef1NmHxfCWHux90uviNTdd0omcpuf.G', '2025-08-07 14:25:54.231525+00', NULL, '', NULL, '', NULL, '', '', NULL, '2025-08-07 14:25:54.243573+00', '{"provider": "email", "providers": ["email"]}', '{"sub": "c7e745d1-f09e-4819-936a-a15c06171e6d", "city": "Bad Homburg", "email": "<EMAIL>", "street": "Landgrafenstr. ", "last_name": "Lampe", "birth_date": "2025-08-02", "first_name": "Dennis", "postal_code": "61350", "house_number": "56B", "email_verified": true, "phone_verified": false, "membership_category": "Erwachsener"}', NULL, '2025-08-07 14:25:54.188609+00', '2025-08-14 17:07:45.911112+00', NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, NULL, false),
	('********-0000-0000-0000-********0000', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'authenticated', 'authenticated', '<EMAIL>', '$2a$10$f7uEY1/AaKJQu2tYvNxNUuDaIJz95kkFlcmRtelD/7bxr.D2VzfHe', '2025-08-13 11:25:35.933656+00', NULL, '', NULL, 'b6425ece0e957e07a7f6243e6a86e119c4dc6a373c949e3138fcf58b', '2025-08-14 19:57:39.525478+00', '', '', NULL, '2025-08-14 18:19:43.793139+00', '{"provider": "email", "providers": ["email"]}', '{"sub": "f87864aa-6f6e-4ab7-972d-ad442155d4f3", "city": "yxcysdasdas", "email": "<EMAIL>", "phone": "123123", "title": "Herr", "gender": "männlich", "street": "asdasd", "last_name": "Piesker", "birth_date": "2025-08-06", "first_name": "Felix", "nationality": "asdasd", "postal_code": "12345", "account_type": "member", "house_number": "23", "applicant_type": "self", "email_verified": true, "phone_verified": false, "membership_category": "Erwachsener"}', NULL, '2025-08-13 11:25:35.808038+00', '2025-08-15 09:05:25.202006+00', NULL, NULL, '', '', NULL, '', 0, NULL, '', NULL, false, NULL, false);


--
-- Data for Name: identities; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."identities" ("provider_id", "user_id", "identity_data", "provider", "last_sign_in_at", "created_at", "updated_at", "id") VALUES
	('c7e745d1-f09e-4819-936a-a15c06171e6d', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '{"sub": "c7e745d1-f09e-4819-936a-a15c06171e6d", "city": "Bad Homburg", "email": "<EMAIL>", "street": "Landgrafenstr. ", "last_name": "Lampe", "birth_date": "2025-08-02", "first_name": "Dennis", "postal_code": "61350", "house_number": "56B", "email_verified": false, "phone_verified": false, "membership_category": "Erwachsener"}', 'email', '2025-08-07 14:25:54.212616+00', '2025-08-07 14:25:54.212671+00', '2025-08-07 14:25:54.212671+00', '9bcd75b9-b346-44e7-8688-11bd841b1354'),
	('f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '{"sub": "f87864aa-6f6e-4ab7-972d-ad442155d4f3", "city": "yxcysdasdas", "email": "<EMAIL>", "phone": "123123", "title": "Herr", "gender": "männlich", "street": "asdasd", "last_name": "Piesker", "birth_date": "2025-08-06", "first_name": "Felix", "nationality": "asdasd", "postal_code": "12345", "account_type": "member", "house_number": "23", "applicant_type": "self", "email_verified": false, "phone_verified": false, "membership_category": "Erwachsener"}', 'email', '2025-08-13 11:25:35.899547+00', '2025-08-13 11:25:35.8996+00', '2025-08-13 11:25:35.8996+00', '2a2cdf8b-0174-473f-b592-26513753fdcf'),
	('939e5d34-2907-4e99-b0b1-24ff4ec72339', '939e5d34-2907-4e99-b0b1-24ff4ec72339', '{"sub": "939e5d34-2907-4e99-b0b1-24ff4ec72339", "email": "<EMAIL>", "email_verified": false, "phone_verified": false}', 'email', '2025-08-14 17:10:13.446741+00', '2025-08-14 17:10:13.447416+00', '2025-08-14 17:10:13.447416+00', 'c40765aa-a5fc-4db8-b044-cfd8dae87201');


--
-- Data for Name: instances; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sessions; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."sessions" ("id", "user_id", "created_at", "updated_at", "factor_id", "aal", "not_after", "refreshed_at", "user_agent", "ip", "tag") VALUES
	('5fbd758a-36fd-495b-993c-e9d05d52dba6', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-13 11:25:35.951286+00', '2025-08-13 11:25:35.951286+00', NULL, 'aal1', NULL, NULL, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:141.0) Gecko/20100101 Firefox/141.0', '************', NULL),
	('fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-07 14:25:54.24374+00', '2025-08-14 17:07:45.924408+00', NULL, 'aal1', NULL, '2025-08-14 17:07:45.924331', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '************', NULL),
	('74316341-9540-4220-bcd0-4d24ee69250b', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-14 18:12:46.054012+00', '2025-08-14 18:12:46.054012+00', NULL, 'aal1', NULL, NULL, 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:141.0) Gecko/20100101 Firefox/141.0', '*************', NULL),
	('bc68545e-7afd-46d1-a122-c9e7fe2c4a66', '939e5d34-2907-4e99-b0b1-24ff4ec72339', '2025-08-14 17:18:54.512095+00', '2025-08-14 19:40:29.627177+00', NULL, 'aal1', NULL, '2025-08-14 19:40:29.627095', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '************', NULL),
	('24fee51c-1295-439b-a760-32783a9e62a1', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-13 11:26:25.174808+00', '2025-08-14 20:03:08.561985+00', NULL, 'aal1', NULL, '2025-08-14 20:03:08.561914', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:141.0) Gecko/20100101 Firefox/141.0', '83.135.177.188', NULL),
	('910996ad-3caa-4339-9b31-b8bd26b3140a', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-14 18:19:43.793213+00', '2025-08-15 09:05:25.214956+00', NULL, 'aal1', NULL, '2025-08-15 09:05:25.213786', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:141.0) Gecko/20100101 Firefox/141.0', '89.244.85.47', NULL);


--
-- Data for Name: mfa_amr_claims; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."mfa_amr_claims" ("session_id", "created_at", "updated_at", "authentication_method", "id") VALUES
	('fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1', '2025-08-07 14:25:54.281589+00', '2025-08-07 14:25:54.281589+00', 'password', 'f35faec8-af51-4f9d-adec-cee132ab5658'),
	('5fbd758a-36fd-495b-993c-e9d05d52dba6', '2025-08-13 11:25:35.985865+00', '2025-08-13 11:25:35.985865+00', 'password', 'bbb44415-99f0-4749-8023-6e28ced402e9'),
	('24fee51c-1295-439b-a760-32783a9e62a1', '2025-08-13 11:26:25.178579+00', '2025-08-13 11:26:25.178579+00', 'password', 'b6f13c03-577c-4a6a-b340-e71e8b646d72'),
	('bc68545e-7afd-46d1-a122-c9e7fe2c4a66', '2025-08-14 17:18:54.524955+00', '2025-08-14 17:18:54.524955+00', 'password', 'e09f51ef-413e-48c8-ad8d-1b2c0deaede0'),
	('74316341-9540-4220-bcd0-4d24ee69250b', '2025-08-14 18:12:46.0913+00', '2025-08-14 18:12:46.0913+00', 'password', '3e5fb9df-3130-429f-9ac2-299848b1023d'),
	('910996ad-3caa-4339-9b31-b8bd26b3140a', '2025-08-14 18:19:43.801616+00', '2025-08-14 18:19:43.801616+00', 'password', 'a7a2472a-8a7c-4f2f-bfa2-cd1839058081');


--
-- Data for Name: mfa_factors; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: mfa_challenges; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: one_time_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."one_time_tokens" ("id", "user_id", "token_type", "token_hash", "relates_to", "created_at", "updated_at") VALUES
	('e568e18d-2092-40b1-90ba-871cd1804fe3', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'recovery_token', 'b6425ece0e957e07a7f6243e6a86e119c4dc6a373c949e3138fcf58b', '<EMAIL>', '2025-08-14 19:57:40.532968', '2025-08-14 19:57:40.532968');


--
-- Data for Name: refresh_tokens; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--

INSERT INTO "auth"."refresh_tokens" ("instance_id", "id", "token", "user_id", "revoked", "created_at", "updated_at", "parent", "session_id") VALUES
	('********-0000-0000-0000-********0000', 1, 'vwaleebyi7ks', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 14:25:54.260432+00', '2025-08-07 15:23:55.90941+00', NULL, 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 2, 'u2buouxef6jz', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 15:23:55.932713+00', '2025-08-07 16:23:13.849321+00', 'vwaleebyi7ks', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 3, 'coyy6grsv2qf', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 16:23:13.858419+00', '2025-08-07 19:07:22.944484+00', 'u2buouxef6jz', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 4, 'ooy366y7hqoy', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 19:07:22.952225+00', '2025-08-07 20:07:46.483768+00', 'coyy6grsv2qf', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 5, '4kj72plidsh4', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 20:07:46.494812+00', '2025-08-07 21:09:38.465472+00', 'ooy366y7hqoy', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 6, '4cjver2i4w6z', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-07 21:09:38.476761+00', '2025-08-08 07:10:19.312268+00', '4kj72plidsh4', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 7, 'l5at46uyt5bl', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 07:10:19.326493+00', '2025-08-08 08:08:44.912634+00', '4cjver2i4w6z', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 8, 'rxfmf7ze3ukm', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 08:08:44.91849+00', '2025-08-08 09:06:58.118845+00', 'l5at46uyt5bl', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 9, 'hbz7pp7t7vcy', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 09:06:58.12779+00', '2025-08-08 10:05:15.463411+00', 'rxfmf7ze3ukm', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 10, 'ruvq5suclfup', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 10:05:15.476401+00', '2025-08-08 11:03:53.947172+00', 'hbz7pp7t7vcy', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 11, 'xc2noiqxztkt', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 11:03:53.957484+00', '2025-08-08 12:02:16.639017+00', 'ruvq5suclfup', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 12, '6scljzg4dfhm', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 12:02:16.656955+00', '2025-08-08 13:01:06.924944+00', 'xc2noiqxztkt', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 13, 'zugox36ktbpz', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 13:01:06.934818+00', '2025-08-08 14:01:06.293203+00', '6scljzg4dfhm', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 14, 'zqkf7u3irav6', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 14:01:06.309728+00', '2025-08-08 14:59:14.993377+00', 'zugox36ktbpz', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 15, '4upaye57pnte', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-08 14:59:15.005205+00', '2025-08-09 05:54:03.670785+00', 'zqkf7u3irav6', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 16, 'ptheawhlp7na', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 05:54:03.680767+00', '2025-08-09 06:52:54.799471+00', '4upaye57pnte', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 17, '2kwqus3ncu5q', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 06:52:54.816781+00', '2025-08-09 08:09:48.569298+00', 'ptheawhlp7na', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 18, '35welpsrjz4p', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 08:09:48.577658+00', '2025-08-09 11:04:33.588575+00', '2kwqus3ncu5q', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 19, '2x7ruqxhn7l7', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 11:04:33.597219+00', '2025-08-09 12:58:56.232865+00', '35welpsrjz4p', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 20, 'dorrewjtzz7s', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 12:58:56.240092+00', '2025-08-09 13:57:08.148863+00', '2x7ruqxhn7l7', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 21, 'bejpdss3vomd', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 13:57:08.158811+00', '2025-08-09 14:59:38.012178+00', 'dorrewjtzz7s', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 22, 'np6azxtrewdw', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 14:59:38.028011+00', '2025-08-09 16:26:07.201916+00', 'bejpdss3vomd', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 23, 'h7drxogqbidk', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 16:26:07.219547+00', '2025-08-09 18:49:55.847854+00', 'np6azxtrewdw', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 24, 'rjos6r6euwrs', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 18:49:55.867943+00', '2025-08-09 20:14:57.41974+00', 'h7drxogqbidk', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 25, 'bwde347nodjh', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 20:14:57.437518+00', '2025-08-09 21:13:22.580914+00', 'rjos6r6euwrs', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 26, 'opejvopbl3aa', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 21:13:22.592304+00', '2025-08-09 22:11:28.648299+00', 'bwde347nodjh', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 27, 'plqqrz4zluni', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-09 22:11:28.65864+00', '2025-08-10 05:08:28.826794+00', 'opejvopbl3aa', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 28, 'vndmpjl4witg', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 05:08:28.852993+00', '2025-08-10 06:06:30.993777+00', 'plqqrz4zluni', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 29, 'akzbqvj4oobn', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 06:06:31.009938+00', '2025-08-10 08:53:27.941404+00', 'vndmpjl4witg', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 30, 'fqgiaf3bt3jq', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 08:53:27.963458+00', '2025-08-10 09:51:54.732322+00', 'akzbqvj4oobn', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 31, 'dxkzriap7nny', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 09:51:54.751943+00', '2025-08-10 10:50:39.929884+00', 'fqgiaf3bt3jq', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 32, 'saz3xtglezzl', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 10:50:39.945142+00', '2025-08-10 11:48:49.173677+00', 'dxkzriap7nny', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 33, 'w47z7t252pd5', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-10 11:48:49.191088+00', '2025-08-11 07:59:42.523091+00', 'saz3xtglezzl', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 34, 'cyvlxqkxinvi', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-11 07:59:42.53948+00', '2025-08-11 09:08:00.547722+00', 'w47z7t252pd5', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 35, 'pzhyjgk7cyw5', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-11 09:08:00.57043+00', '2025-08-12 13:06:46.417213+00', 'cyvlxqkxinvi', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 36, '2k24gdzrhko6', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-12 13:06:46.439338+00', '2025-08-13 07:57:14.759764+00', 'pzhyjgk7cyw5', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 37, '6vcwiqcxiyxx', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 07:57:14.77656+00', '2025-08-13 08:55:33.237761+00', '2k24gdzrhko6', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 38, 'ppokberelavd', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 08:55:33.2599+00', '2025-08-13 10:26:41.970074+00', '6vcwiqcxiyxx', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 40, 'ta74kpfhd2mk', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', false, '2025-08-13 11:25:35.964316+00', '2025-08-13 11:25:35.964316+00', NULL, '5fbd758a-36fd-495b-993c-e9d05d52dba6'),
	('********-0000-0000-0000-********0000', 39, 'pu5unrpbvgan', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 10:26:41.98155+00', '2025-08-13 11:28:38.810857+00', 'ppokberelavd', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 42, '74blm33ja5kj', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 11:28:38.812599+00', '2025-08-13 14:38:36.683631+00', 'pu5unrpbvgan', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 43, 'xkp7gcnodgxq', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 14:38:36.700682+00', '2025-08-13 17:04:08.151131+00', '74blm33ja5kj', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 44, 'ftp4kck4ngh4', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-13 17:04:08.16497+00', '2025-08-14 07:26:13.619644+00', 'xkp7gcnodgxq', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 45, 'nnpoim3ued7q', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-14 07:26:13.647988+00', '2025-08-14 09:34:34.678273+00', 'ftp4kck4ngh4', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 46, 'nlykf7nv3d6c', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-14 09:34:34.690145+00', '2025-08-14 10:43:42.290759+00', 'nnpoim3ued7q', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 47, 'qa5owslbitu2', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-14 10:43:42.314987+00', '2025-08-14 15:21:10.362162+00', 'nlykf7nv3d6c', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 48, 'rrs2eutxgwdy', 'c7e745d1-f09e-4819-936a-a15c06171e6d', true, '2025-08-14 15:21:10.370833+00', '2025-08-14 17:07:45.881464+00', 'qa5owslbitu2', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 49, '2wplmydwg2wz', 'c7e745d1-f09e-4819-936a-a15c06171e6d', false, '2025-08-14 17:07:45.905428+00', '2025-08-14 17:07:45.905428+00', 'rrs2eutxgwdy', 'fd6b91a5-0357-4c70-8ab9-ccbe5d8c5bb1'),
	('********-0000-0000-0000-********0000', 50, '2ahswzf5ay3z', '939e5d34-2907-4e99-b0b1-24ff4ec72339', true, '2025-08-14 17:18:54.516879+00', '2025-08-14 18:25:28.324837+00', NULL, 'bc68545e-7afd-46d1-a122-c9e7fe2c4a66'),
	('********-0000-0000-0000-********0000', 41, 'sl3nbek6ae3v', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', true, '2025-08-13 11:26:25.1762+00', '2025-08-14 20:03:08.556817+00', NULL, '24fee51c-1295-439b-a760-32783a9e62a1'),
	('********-0000-0000-0000-********0000', 51, 'w3ohjtq522ot', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', false, '2025-08-14 18:12:46.061151+00', '2025-08-14 18:12:46.061151+00', NULL, '74316341-9540-4220-bcd0-4d24ee69250b'),
	('********-0000-0000-0000-********0000', 53, '2tal2om52czz', '939e5d34-2907-4e99-b0b1-24ff4ec72339', true, '2025-08-14 18:25:28.344261+00', '2025-08-14 19:40:29.584433+00', '2ahswzf5ay3z', 'bc68545e-7afd-46d1-a122-c9e7fe2c4a66'),
	('********-0000-0000-0000-********0000', 54, 'jlxpmh6wude5', '939e5d34-2907-4e99-b0b1-24ff4ec72339', false, '2025-08-14 19:40:29.60372+00', '2025-08-14 19:40:29.60372+00', '2tal2om52czz', 'bc68545e-7afd-46d1-a122-c9e7fe2c4a66'),
	('********-0000-0000-0000-********0000', 52, 'nzvhtwlyqrkd', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', true, '2025-08-14 18:19:43.79488+00', '2025-08-14 19:56:43.741547+00', NULL, '910996ad-3caa-4339-9b31-b8bd26b3140a'),
	('********-0000-0000-0000-********0000', 56, 'kmiu5huzbh7i', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', false, '2025-08-14 20:03:08.559483+00', '2025-08-14 20:03:08.559483+00', 'sl3nbek6ae3v', '24fee51c-1295-439b-a760-32783a9e62a1'),
	('********-0000-0000-0000-********0000', 55, 'mkai5zrsicqn', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', true, '2025-08-14 19:56:43.747353+00', '2025-08-15 09:05:25.175338+00', 'nzvhtwlyqrkd', '910996ad-3caa-4339-9b31-b8bd26b3140a'),
	('********-0000-0000-0000-********0000', 57, 'anoeehvzum6z', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', false, '2025-08-15 09:05:25.192574+00', '2025-08-15 09:05:25.192574+00', 'mkai5zrsicqn', '910996ad-3caa-4339-9b31-b8bd26b3140a');


--
-- Data for Name: sso_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_providers; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: saml_relay_states; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: sso_domains; Type: TABLE DATA; Schema: auth; Owner: supabase_auth_admin
--



--
-- Data for Name: clubs; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."clubs" ("id", "name", "slug", "custom_domain", "subdomain", "logo_url", "description", "settings", "is_active", "created_at", "updated_at") VALUES
	('e3946bb7-3608-42d9-917b-394022455822', 'Tennis Club München', 'tcm', NULL, 'tcm', NULL, 'Premium Tennis Club in München', '{}', true, '2025-08-13 17:10:42.138607+00', '2025-08-13 17:10:42.138607+00'),
	('bb953193-b040-448e-9260-0314a751d441', 'Berliner Tennis Verein', 'btv', NULL, 'btv', NULL, 'Traditioneller Tennis Verein in Berlin', '{}', true, '2025-08-13 17:10:42.138607+00', '2025-08-13 17:10:42.138607+00'),
	('c6b7ffe1-33af-4f95-8a6c-ddd3d19af578', 'Hamburg Tennis Club', 'htc', NULL, 'htc', NULL, 'Exklusiver Tennis Club in Hamburg', '{}', true, '2025-08-13 17:10:42.138607+00', '2025-08-13 17:10:42.138607+00');


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."accounts" ("id", "account_type", "first_name", "last_name", "email", "phone", "address_full", "address_street", "address_house_number", "address_postal_code", "address_city", "birth_date", "membership_type", "age_type_mismatch_flag", "created_at", "updated_at", "billing_member_id", "user_id", "club_id") VALUES
	('225e978b-f317-44e9-a2ee-36c3d59d776e', 'Admin', 'Thomas', 'Admin', '<EMAIL>', '+49 123 456801', NULL, 'Verwaltungsstraße 1', NULL, '10115', 'Berlin', '1975-08-20', NULL, false, '2025-08-08 08:08:31.667987+00', '2025-08-08 08:08:31.667987+00', NULL, NULL, NULL),
	('04d03031-6733-4daa-9fa8-5be546999cc2', 'Admin', 'Petra', 'Verwaltung', '<EMAIL>', '+49 123 456802', NULL, 'Bürostraße 25', NULL, '80331', 'München', '1980-11-12', NULL, false, '2025-08-08 08:08:31.667987+00', '2025-08-08 08:08:31.667987+00', NULL, NULL, NULL),
	('b614956e-c70a-421f-a3e5-81da2a9fc7d8', 'Trainer', 'Michael', 'Coach', '<EMAIL>', '+49 123 456803', NULL, 'Sportstraße 10', NULL, '20095', 'Hamburg', '1983-04-07', NULL, false, '2025-08-08 08:08:31.667987+00', '2025-08-08 08:08:31.667987+00', NULL, NULL, NULL),
	('073296bb-5c74-49e8-9df3-479206016814', 'Guest', 'Dennis', 'Lampe', '<EMAIL>', '015258528888', 'Landgrafenstr. 56b 61350 Bad Homburg', 'Landgrafenstr. ', '56B', '61350', 'Bad Homburg', '1986-08-08', 'Jugendliche', false, '2025-08-06 16:00:38.177739+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('e663d566-8b69-4477-b4d0-c2a92fe6d0a1', 'Member', 'Max', 'Müller', '<EMAIL>', '+49 123 456789', NULL, 'Hauptstraße 1', NULL, '10115', 'Berlin', '1985-03-15', 'Erwachsene', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('ae068b72-19d4-4308-8c53-dd49f260b70b', 'Member', 'Anna', 'Schmidt', '<EMAIL>', '+49 123 456790', NULL, 'Musterstraße 12', NULL, '80331', 'München', '1990-07-22', 'Erwachsene', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('b8d07cf2-64ee-4ec4-a899-8b20ec6c5713', 'Member', 'Tom', 'Weber', '<EMAIL>', '+49 123 456791', NULL, 'Parkweg 8', NULL, '20095', 'Hamburg', '2010-11-08', 'Kinder', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('a9de7a63-308c-49e1-aef6-92f2f8c99d0e', 'Member', 'Lisa', 'Fischer', '<EMAIL>', '+49 123 456792', NULL, 'Rosenstraße 5', NULL, '50667', 'Köln', '2005-02-14', 'Jugendliche', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('eb923d94-74bf-497e-94fc-6ff2ea1e887e', 'Member', 'Paul', 'Wagner', '<EMAIL>', '+49 123 456793', NULL, 'Bergstraße 23', NULL, '70173', 'Stuttgart', '1995-09-30', 'Studenten', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('9b56697d-ab64-4495-b9de-a92d39ea6a53', 'Member', 'Marie', 'Becker', '<EMAIL>', '+49 123 456794', NULL, 'Waldweg 15', NULL, '40210', 'Düsseldorf', '1982-12-05', 'Erwachsene', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('2549a1a5-10d4-4d39-bd5f-cba03b325cc1', 'Member', 'Felix', 'Schulz', '<EMAIL>', '+49 123 456795', NULL, 'Sonnenallee 42', NULL, '60311', 'Frankfurt', '2008-04-18', 'Kinder', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('9eb3e0dd-d51a-4f69-b06f-26c282e45a35', 'Member', 'Sophie', 'Richter', '<EMAIL>', '+49 123 456796', NULL, 'Lindenstraße 7', NULL, '04109', 'Leipzig', '2002-08-12', 'Jugendliche', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('07719414-11f8-4f00-8a00-4c82063a42bc', 'Member', 'Tim', 'Klein', '<EMAIL>', '+49 123 456797', NULL, 'Kirchstraße 33', NULL, '30159', 'Hannover', '1988-01-25', 'Erwachsene', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('eae6e101-0a82-4863-a2cf-e7530a940d17', 'Member', 'Emma', 'Wolf', '<EMAIL>', '+49 123 456798', NULL, 'Gartenstraße 19', NULL, '01067', 'Dresden', '1992-06-03', 'Erwachsene', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('e17665bc-03ab-42ec-8530-7a5a62ca595b', 'Member', 'Lukas', 'Neumann', '<EMAIL>', '+49 123 456799', NULL, 'Bahnhofstraße 11', NULL, '90402', 'Nürnberg', '2012-10-27', 'Kinder', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL),
	('15e0c233-ee35-446f-843c-21c728a18713', 'Member', 'Clara', 'Zimmermann', '<EMAIL>', '+49 123 456800', NULL, 'Schlossstraße 6', NULL, '76131', 'Karlsruhe', '1999-05-16', 'Studenten', false, '2025-08-08 08:08:31.667987+00', '2025-08-08 11:51:20.841301+00', NULL, NULL, NULL);


--
-- Data for Name: activities; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."activities" ("id", "name", "description", "hourly_rate", "created_at", "status", "requested_by", "approved_by", "approved_at", "created_by", "club_id") VALUES
	('3e259e1d-d19a-4aa0-aeeb-866e50d0f7ac', 'Platz 1 abziehen', 'Clean Court 1', NULL, '2025-08-06 13:13:14.857308+00', 'approved', NULL, NULL, NULL, NULL, NULL);


--
-- Data for Name: groups; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: members; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."members" ("id", "first_name", "last_name", "birth_date", "address", "email", "phone", "iban", "membership_type", "annual_fee", "payment_status", "group_id", "created_at", "updated_at", "club_id") VALUES
	('1aaa5e16-0cf6-4d04-a787-c0ac4553ad57', 'Dennis', 'Lampe', '1986-08-08', 'Landgrafenstr. 56b, 61350 Bad Homburg', '<EMAIL>', '015258528888', 'DE 1001 10001 34500632', 'Erwachsene', 230.00, 'paid', NULL, '2025-08-06 13:12:31.979496+00', '2025-08-08 11:51:20.841301+00', NULL);


--
-- Data for Name: activity_registrations; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: audit_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: billing_agreements; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: courts; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."courts" ("id", "number", "locked", "lock_reason", "created_at", "updated_at", "court_group", "surface_type", "club_id") VALUES
	('c781e256-101a-4837-a580-bc4895d6f17a', 2, false, NULL, '2025-08-06 15:40:20.486638+00', '2025-08-06 15:40:20.486638+00', NULL, NULL, NULL),
	('7a31d304-b9a1-4b1b-9b38-941a268e6595', 5, false, NULL, '2025-08-14 15:27:28.345363+00', '2025-08-14 15:27:28.345363+00', NULL, NULL, NULL),
	('431efaf7-cc4e-49a8-bddd-482f4299e417', 4, false, NULL, '2025-08-14 15:27:27.987073+00', '2025-08-14 15:28:04.045864+00', NULL, NULL, NULL),
	('acade00d-9b9e-4281-b1ca-fa648e10d13f', 3, false, NULL, '2025-08-07 16:47:36.143226+00', '2025-08-07 16:47:36.143226+00', NULL, NULL, NULL),
	('5cd4ecec-4a58-4526-84e1-4adca69d773c', 1, false, NULL, '2025-08-06 15:40:20.486638+00', '2025-08-08 09:09:49.093912+00', 'Outdoor', 'Keramiksand', NULL);


--
-- Data for Name: profiles; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."profiles" ("id", "first_name", "last_name", "birth_date", "street", "house_number", "postal_code", "city", "email", "membership_category", "join_date", "profile_image_url", "created_at", "updated_at") VALUES
	('c7e745d1-f09e-4819-936a-a15c06171e6d', 'Dennis', 'Lampe', '2025-08-02', 'Landgrafenstr. ', '56B', '61350', 'Bad Homburg', '<EMAIL>', 'Erwachsener', '2025-08-07', NULL, '2025-08-07 14:25:54.188241+00', '2025-08-07 14:25:54.188241+00'),
	('f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'Felix', 'Piesker', '2025-08-06', 'asdasd', '23', '12345', 'yxcysdasdas', '<EMAIL>', 'Erwachsener', '2025-08-13', NULL, '2025-08-13 11:25:35.803163+00', '2025-08-13 11:25:35.803163+00'),
	('939e5d34-2907-4e99-b0b1-24ff4ec72339', 'Unknown', 'Unknown', '2025-08-14', '', '', '', '', '<EMAIL>', 'Erwachsener', '2025-08-14', NULL, '2025-08-14 17:10:13.424485+00', '2025-08-14 17:10:13.424485+00');


--
-- Data for Name: bookings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."bookings" ("id", "court_id", "booking_date", "start_time", "end_time", "player_name", "partner_name", "created_at", "updated_at", "created_by", "start_at", "end_at", "acting_account_id", "booked_for_account_id", "club_id") VALUES
	('63b6aba6-2955-408d-ab94-2f83be25b38e', '5cd4ecec-4a58-4526-84e1-4adca69d773c', '2025-08-05', '08:00:00', '09:00:00', 'Dennis Lampe', 'Dennis Lampe', '2025-08-09 22:07:34.310061+00', '2025-08-10 12:07:03.310169+00', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-05 08:00:00+00', '2025-08-05 09:00:00+00', NULL, NULL, NULL),
	('1c2c6c63-ed07-4147-a6ab-7ecef4ab8439', 'acade00d-9b9e-4281-b1ca-fa648e10d13f', '2025-08-06', '10:00:00', '11:00:00', 'Dennis Lampe', 'Dennis Lampe', '2025-08-09 22:07:51.521427+00', '2025-08-10 12:07:03.310169+00', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-06 10:00:00+00', '2025-08-06 11:00:00+00', NULL, NULL, NULL),
	('96f524c7-bab3-4e94-aaaa-7a8f87618c96', 'c781e256-101a-4837-a580-bc4895d6f17a', '2025-08-10', '11:00:00', '12:00:00', 'Dennis Lampe', 'Dennis Lampe', '2025-08-09 22:25:54.373617+00', '2025-08-10 12:07:03.310169+00', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-10 11:00:00+00', '2025-08-10 12:00:00+00', NULL, NULL, NULL),
	('10b44b8d-fb0a-4dbf-8c82-f46e1e9fb327', 'c781e256-101a-4837-a580-bc4895d6f17a', '2025-08-12', '08:00:00', '09:00:00', 'Dennis Lampe', 'Dennis Lampe', '2025-08-12 13:15:00.161941+00', '2025-08-12 13:15:00.161941+00', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-12 06:00:00+00', '2025-08-12 07:00:00+00', NULL, NULL, NULL),
	('a88233ef-ac75-4329-a539-45c1c55f45e3', 'c781e256-101a-4837-a580-bc4895d6f17a', '2025-08-12', '10:00:00', '11:00:00', 'Felix Piesker', 'Dennis Lampe', '2025-08-13 11:27:33.367292+00', '2025-08-13 11:27:33.367292+00', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-12 08:00:00+00', '2025-08-12 09:00:00+00', NULL, NULL, NULL),
	('9966af98-a170-4d89-8e71-7d43c0d88fd4', '5cd4ecec-4a58-4526-84e1-4adca69d773c', '2025-08-13', '09:00:00', '10:00:00', 'Dennis Lampe', 'Dennis Lampe', '2025-08-14 15:23:26.918768+00', '2025-08-14 15:23:26.918768+00', 'c7e745d1-f09e-4819-936a-a15c06171e6d', '2025-08-13 07:00:00+00', '2025-08-13 08:00:00+00', NULL, NULL, NULL);


--
-- Data for Name: club_memberships; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: plans; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."plans" ("id", "name", "description", "price_monthly", "price_yearly", "limits", "features", "is_active", "sort_order", "created_at", "updated_at") VALUES
	('94e974e3-3e04-4504-8bb4-4e319789910b', 'Starter', 'Für kleine Vereine', 29.00, 290.00, '{"courts": 5, "members": 100, "storage_gb": 1, "api_requests": 1000}', '["Buchungsverwaltung", "Mitgliederverwaltung", "E-Mail Support"]', true, 0, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00'),
	('6022335d-2b9b-4864-93eb-c028eb125eb7', 'Professional', 'Für mittlere Vereine', 79.00, 790.00, '{"courts": 20, "members": 500, "storage_gb": 10, "api_requests": 10000}', '["Erweiterte Berichte", "API Zugang", "Custom Branding", "Telefon Support"]', true, 0, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00'),
	('4ddcbb5c-d7a9-483e-8be1-4a1de824254c', 'Enterprise', 'Für große Vereine', 199.00, 1990.00, '{"courts": -1, "members": -1, "storage_gb": 100, "api_requests": 100000}', '["Unlimited", "Dedicated Support", "Custom Domains", "White Label"]', true, 0, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00');


--
-- Data for Name: club_subscriptions; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: court_availability; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."court_availability" ("id", "court_id", "start_time", "end_time", "days_of_week", "created_at") VALUES
	('b62a5548-9bff-4d7a-8097-11e44bfd8175', '5cd4ecec-4a58-4526-84e1-4adca69d773c', '06:00:00', '22:00:00', '{1,2,3,4,5,6,0}', '2025-08-08 14:59:57.761843+00'),
	('9515afc2-93d2-42bd-92fd-0c98aa88e9bc', 'c781e256-101a-4837-a580-bc4895d6f17a', '06:00:00', '22:00:00', '{1,2,3,4,5,6,0}', '2025-08-08 14:59:57.761843+00'),
	('c145dc29-7c23-4750-ba02-f5fac28d0ada', 'acade00d-9b9e-4281-b1ca-fa648e10d13f', '06:00:00', '22:00:00', '{1,2,3,4,5,6,0}', '2025-08-08 14:59:57.761843+00');


--
-- Data for Name: court_blocks; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: domains; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: email_templates; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."email_templates" ("id", "name", "subject", "content", "template_type", "is_active", "created_at", "updated_at") VALUES
	('0bf4a2dc-5872-47f6-9238-302811f51bba', 'welcome_email', 'Willkommen bei {{club_name}}', 'Hallo {{user_name}},\n\nwillkommen bei {{club_name}}! Wir freuen uns, Sie als neues Mitglied begrüßen zu dürfen.\n\nIhr Team von {{club_name}}', 'member_notification', true, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00'),
	('ff6a20b3-3c5f-46bf-992a-e22635e61b2d', 'booking_confirmation', 'Buchungsbestätigung für {{court_name}}', 'Hallo {{user_name}},\n\nIhre Buchung wurde bestätigt:\n\nPlatz: {{court_name}}\nDatum: {{booking_date}}\nUhrzeit: {{booking_time}}\n\nViel Spaß beim Spiel!', 'booking_notification', true, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00'),
	('014d28dd-4da7-4d23-81a5-731a3dfff61d', 'password_reset', 'Passwort zurücksetzen', 'Hallo,\n\nSie haben eine Passwort-Zurücksetzung angefordert. Klicken Sie auf den folgenden Link: {{reset_link}}\n\nFalls Sie diese Anfrage nicht gestellt haben, ignorieren Sie diese E-Mail.', 'authentication', true, '2025-08-14 09:34:13.381902+00', '2025-08-14 09:34:13.381902+00');


--
-- Data for Name: fee_categories; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."fee_categories" ("id", "value", "display_name", "color", "sort_order", "is_active", "created_at", "updated_at") VALUES
	('250ff6b6-5a2e-4fa5-90ce-a0bbbd437cc7', 'membership', 'Mitgliedschaft', NULL, 1, true, '2025-08-09 19:02:52.669333+00', '2025-08-09 19:02:52.669333+00'),
	('747afa02-9198-4472-b384-fde3cccb124d', 'course', 'Kurs/Training', NULL, 2, true, '2025-08-09 19:02:52.669333+00', '2025-08-09 19:02:52.669333+00'),
	('140575c2-6dd0-470a-aac7-b7c00721fd2e', 'event', 'Event/Turnier', NULL, 3, true, '2025-08-09 19:02:52.669333+00', '2025-08-09 19:02:52.669333+00'),
	('5fb2d49a-cb7f-448a-8b54-dda75c6063d7', 'other', 'Sonstiges', NULL, 99, true, '2025-08-09 19:02:52.669333+00', '2025-08-09 19:02:52.669333+00');


--
-- Data for Name: membership_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."membership_types" ("id", "value", "display_name", "description", "is_active", "created_at", "updated_at", "is_group_type") VALUES
	('07bef344-1727-41b9-a91d-c8fbdee51f50', 'Erwachsener', 'Erwachsener', 'Vollmitgliedschaft für Erwachsene', true, '2025-08-08 13:08:20.486935+00', '2025-08-08 13:08:20.486935+00', false),
	('7b108355-0bb5-4b21-bf18-9931216b8e79', 'Jugendlicher', 'Jugendlicher', 'Mitgliedschaft für Jugendliche unter 18 Jahren', true, '2025-08-08 13:08:20.486935+00', '2025-08-08 13:08:20.486935+00', false),
	('f77eef62-9ce6-4b8d-a494-c89ee45a9931', 'Student', 'Student', 'Vergünstigte Mitgliedschaft für Studenten', true, '2025-08-08 13:08:20.486935+00', '2025-08-08 13:08:20.486935+00', false),
	('16b8f404-a10e-4388-9679-e6a87bb23442', 'Familie', 'Familie', 'Familienmitgliedschaft', true, '2025-08-08 13:08:20.486935+00', '2025-08-08 13:08:20.486935+00', false),
	('fb6f660b-8baa-4feb-bea2-51caa44a127d', 'Ehrenmitglied', 'Ehrenmitglied', 'Kostenlose Ehrenmitgliedschaft', true, '2025-08-08 13:08:20.486935+00', '2025-08-08 13:08:20.486935+00', false);


--
-- Data for Name: fee_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."fee_types" ("id", "name", "description", "amount", "currency", "billing_cycle", "category", "is_active", "requires_membership", "age_restrictions", "validity_period_days", "created_at", "updated_at", "membership_category", "membership_type_id", "category_id") VALUES
	('aba3581b-58be-4443-8328-fd909df2f904', 'Familienbeitrag', NULL, 520.00, 'EUR', 'annual', 'membership', true, false, NULL, NULL, '2025-08-09 14:42:32.200573+00', '2025-08-09 19:02:52.669333+00', NULL, 'Familie', '250ff6b6-5a2e-4fa5-90ce-a0bbbd437cc7'),
	('6c9a43b1-e7d1-476d-9352-05db88b0374e', 'Halbjahresmitgliedschaft', 'Mitgliedschaft für 6 Monate', 110.00, 'EUR', 'semi_annual', 'membership', true, false, NULL, 150, '2025-08-08 12:19:23.129746+00', '2025-08-09 19:02:52.669333+00', NULL, NULL, '250ff6b6-5a2e-4fa5-90ce-a0bbbd437cc7'),
	('724ddd1e-8430-46fa-b417-e1941f8c042c', 'Erwachsene', 'Vollmitgliedschaft für Erwachsene ab 18 Jahren', 230.00, 'EUR', 'annual', 'membership', true, false, NULL, NULL, '2025-08-08 12:19:23.129746+00', '2025-08-09 19:02:52.669333+00', 'Erwachsener', 'Erwachsener', '250ff6b6-5a2e-4fa5-90ce-a0bbbd437cc7'),
	('0cf41172-d0d8-4cb9-b3d8-9739b81879cb', 'Jugendliche', 'Jahresmitgliedschaft für Jugendliche bis 18 Jahre', 160.00, 'EUR', 'annual', 'membership', true, false, '{"max_age": 18, "min_age": 14}', NULL, '2025-08-08 12:19:23.129746+00', '2025-08-09 19:02:52.669333+00', 'Jugendlicher', 'Jugendlicher', '250ff6b6-5a2e-4fa5-90ce-a0bbbd437cc7'),
	('bc267fef-7de0-446c-bafa-7974630d4b04', 'Aufnahmegebühr', NULL, 110.00, 'EUR', 'one_time', 'other', true, false, NULL, NULL, '2025-08-09 14:45:21.52073+00', '2025-08-09 19:02:52.669333+00', NULL, 'Erwachsener', '5fb2d49a-cb7f-448a-8b54-dda75c6063d7'),
	('2d911189-af6b-4a2d-914e-fac19b15caf2', 'Aufnahmegebühr', NULL, 50.00, 'EUR', 'one_time', 'other', true, false, NULL, NULL, '2025-08-09 14:45:41.080824+00', '2025-08-09 19:02:52.669333+00', NULL, 'Jugendlicher', '5fb2d49a-cb7f-448a-8b54-dda75c6063d7');


--
-- Data for Name: fee_assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: fee_type_membership_types; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."fee_type_membership_types" ("id", "fee_type_id", "membership_type_id", "created_at") VALUES
	('80328a77-3803-405f-9e30-1a72625ea9be', '0cf41172-d0d8-4cb9-b3d8-9739b81879cb', '7b108355-0bb5-4b21-bf18-9931216b8e79', '2025-08-09 17:09:23.960069+00'),
	('01a6b3a3-f636-454c-964e-1db86b9fd47f', '2d911189-af6b-4a2d-914e-fac19b15caf2', '7b108355-0bb5-4b21-bf18-9931216b8e79', '2025-08-09 17:09:23.960069+00'),
	('470b84db-2733-401d-819f-196c05393b2b', '724ddd1e-8430-46fa-b417-e1941f8c042c', '07bef344-1727-41b9-a91d-c8fbdee51f50', '2025-08-09 17:09:23.960069+00'),
	('3d08d3c8-a4da-4b11-a00c-b00ecfa91152', 'aba3581b-58be-4443-8328-fd909df2f904', '16b8f404-a10e-4388-9679-e6a87bb23442', '2025-08-09 17:09:23.960069+00'),
	('f3ccef61-c111-4033-a090-4d0140a58d7f', 'bc267fef-7de0-446c-bafa-7974630d4b04', '07bef344-1727-41b9-a91d-c8fbdee51f50', '2025-08-09 17:09:23.960069+00');


--
-- Data for Name: group_members; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: guardianships; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: households; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: household_members; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: invites; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: login_identities; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: membership_cancellations; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: meta_admin_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."meta_admin_permissions" ("id", "user_id", "role", "granted_by", "granted_at", "expires_at", "created_at", "updated_at") VALUES
	('4788df70-c070-4b3b-b8aa-5545eda4838a', '939e5d34-2907-4e99-b0b1-24ff4ec72339', 'SUPER_ADMIN', '939e5d34-2907-4e99-b0b1-24ff4ec72339', '2025-08-14 17:15:40.228312+00', NULL, '2025-08-14 17:15:40.228312+00', '2025-08-14 17:15:40.228312+00'),
	('cfad9296-6822-4830-872a-25ceb250104f', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'SUPER_ADMIN', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', '2025-08-14 18:18:28.97538+00', '2030-01-01 00:00:00+00', '2025-08-14 18:18:28.97538+00', '2025-08-14 18:41:27.30742+00');


--
-- Data for Name: role_permissions; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."role_permissions" ("id", "role", "permission", "created_at") VALUES
	('51f8eb51-4570-4676-bb8d-68d0cfd3d492', 'admin', 'members_view', '2025-08-07 14:34:27.248097+00'),
	('54009f52-1657-49bc-9d71-32742908d28e', 'admin', 'members_create', '2025-08-07 14:34:27.248097+00'),
	('32048107-d2bc-4b37-b942-a84435fe0742', 'admin', 'members_edit', '2025-08-07 14:34:27.248097+00'),
	('b594e147-8cab-426e-ac1d-2468ad49a866', 'admin', 'members_delete', '2025-08-07 14:34:27.248097+00'),
	('368d4ea2-d1dd-4df8-bceb-b2deaa174689', 'admin', 'courts_view', '2025-08-07 14:34:27.248097+00'),
	('5cfcd033-938e-40bb-aae5-d1ee33f8a009', 'admin', 'courts_book', '2025-08-07 14:34:27.248097+00'),
	('c047af97-c073-4f4d-9dad-135b156a5bf4', 'admin', 'courts_manage', '2025-08-07 14:34:27.248097+00'),
	('0b883c96-6a1c-43f8-a9e1-a4f7bc3c9804', 'admin', 'courts_config', '2025-08-07 14:34:27.248097+00'),
	('2b277e5e-c3f8-405b-902a-d4b611cf52f1', 'admin', 'payments_view', '2025-08-07 14:34:27.248097+00'),
	('6579d810-017e-45cf-9734-8de6571c82a0', 'admin', 'payments_process', '2025-08-07 14:34:27.248097+00'),
	('2115c6e0-359b-42c8-8a68-ad111fe0cd57', 'admin', 'payments_manage', '2025-08-07 14:34:27.248097+00'),
	('a31572ff-7949-4059-a557-d2e42ba0b6b7', 'admin', 'tournaments_view', '2025-08-07 14:34:27.248097+00'),
	('9acd92a9-00cb-4cf2-9234-2ab1372ac7ea', 'admin', 'tournaments_create', '2025-08-07 14:34:27.248097+00'),
	('171d5fd5-36d0-4111-b57a-b06339bf080a', 'admin', 'tournaments_manage', '2025-08-07 14:34:27.248097+00'),
	('e4f8a920-a23c-48f5-b152-173ac55f571c', 'admin', 'courses_view', '2025-08-07 14:34:27.248097+00'),
	('ecd0f950-8d07-447e-affe-4c387a8b0fc4', 'admin', 'courses_create', '2025-08-07 14:34:27.248097+00'),
	('7e5d9eed-645b-4525-80c8-c40219653859', 'admin', 'courses_manage', '2025-08-07 14:34:27.248097+00'),
	('de755b9b-58e7-4e33-84b9-437efa242562', 'admin', 'courses_participate', '2025-08-07 14:34:27.248097+00'),
	('4a2e2a42-4d52-4a35-aebb-72c4a0d8b1c1', 'admin', 'community_view', '2025-08-07 14:34:27.248097+00'),
	('71bb6d2a-4bbf-4f2f-b429-9e94a5117d23', 'admin', 'community_post', '2025-08-07 14:34:27.248097+00'),
	('31b9a556-9c22-4b3e-99c6-91eab5e0e35b', 'admin', 'community_moderate', '2025-08-07 14:34:27.248097+00'),
	('5965e2e9-f259-4667-916d-6ff751738c9f', 'admin', 'reports_view', '2025-08-07 14:34:27.248097+00'),
	('01991444-ecca-4487-b101-3f209c92f1d9', 'admin', 'reports_generate', '2025-08-07 14:34:27.248097+00'),
	('a3cf5e19-baa4-4f19-9af8-eda61f2bddd6', 'admin', 'reports_export', '2025-08-07 14:34:27.248097+00'),
	('9d7ea21f-5bcf-42fa-839e-f4995e74ef1f', 'admin', 'system_config', '2025-08-07 14:34:27.248097+00'),
	('05e3ecb7-e219-4034-9463-cc5271d2f588', 'admin', 'user_roles_manage', '2025-08-07 14:34:27.248097+00'),
	('44b921c8-4225-4765-86d8-ca8dfe66fe87', 'admin', 'profile_edit_own', '2025-08-07 14:34:27.248097+00'),
	('4de095af-ba55-490d-b8cf-9f5f22db5c37', 'admin', 'profile_view_others', '2025-08-07 14:34:27.248097+00'),
	('1b2da1df-596a-493b-a32b-6a02b3162f3a', 'trainer', 'courses_view', '2025-08-07 14:34:27.248097+00'),
	('f1f203f0-1427-4c7c-a60b-ff7db90379c0', 'trainer', 'courses_create', '2025-08-07 14:34:27.248097+00'),
	('ddde3d65-969e-473e-9ed0-587a0f15fd64', 'trainer', 'courses_manage', '2025-08-07 14:34:27.248097+00'),
	('c2d0cb10-48da-4533-8e23-4ff7964fb921', 'trainer', 'courses_participate', '2025-08-07 14:34:27.248097+00'),
	('fccb07af-7b6a-4eb0-afa8-1bdaeae80498', 'trainer', 'courts_view', '2025-08-07 14:34:27.248097+00'),
	('76686088-d71a-438e-90e1-54a9b38483e7', 'trainer', 'courts_book', '2025-08-07 14:34:27.248097+00'),
	('82a9f4be-7b18-4abf-94ac-4f2d89138ded', 'trainer', 'tournaments_view', '2025-08-07 14:34:27.248097+00'),
	('38a1e5ec-2c07-4f27-a1ee-270183ee82a4', 'trainer', 'tournaments_create', '2025-08-07 14:34:27.248097+00'),
	('26d6481a-76af-41e5-b8c1-3781bf04d34d', 'trainer', 'community_view', '2025-08-07 14:34:27.248097+00'),
	('cae3d3ca-1f79-4fe5-b740-9b46e2744b9c', 'trainer', 'community_post', '2025-08-07 14:34:27.248097+00'),
	('3c84a538-1a31-4894-b887-e6d1bc1359b8', 'trainer', 'profile_edit_own', '2025-08-07 14:34:27.248097+00'),
	('ee994dc4-3c11-4c38-9181-84ecfc4f23f0', 'trainer', 'members_view', '2025-08-07 14:34:27.248097+00'),
	('f5fa29fd-df3d-46f5-aa8a-4b5ffa6417c7', 'mitglied', 'courts_view', '2025-08-07 14:34:27.248097+00'),
	('82c17a7c-162d-4f32-96a9-dbd14f906666', 'mitglied', 'courts_book', '2025-08-07 14:34:27.248097+00'),
	('a7ce277f-92c7-4cc5-8941-a1f04e7f84c8', 'mitglied', 'tournaments_view', '2025-08-07 14:34:27.248097+00'),
	('ff2af938-6ca2-449c-a44c-116f7a5d31c9', 'mitglied', 'courses_view', '2025-08-07 14:34:27.248097+00'),
	('53c551b9-957e-46d0-bdbe-a0677dcd3896', 'mitglied', 'courses_participate', '2025-08-07 14:34:27.248097+00'),
	('3e9224e1-7b3e-4553-8095-da9a4c2dd9b2', 'mitglied', 'community_view', '2025-08-07 14:34:27.248097+00'),
	('0f0c08d7-42a3-496f-9223-3e824409645c', 'mitglied', 'community_post', '2025-08-07 14:34:27.248097+00'),
	('de500c91-4117-44e6-ab60-873f4e5c7592', 'mitglied', 'profile_edit_own', '2025-08-07 14:34:27.248097+00'),
	('004677a8-e791-41c8-93b7-7540f367f501', 'mitglied', 'payments_view', '2025-08-07 14:34:27.248097+00');


--
-- Data for Name: support_tickets; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: system_metrics; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: system_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."system_settings" ("id", "key", "value", "created_at", "updated_at") VALUES
	('0586473a-1879-4e68-a7ac-d6ffa0c81031', 'total_courts', '2', '2025-08-06 15:40:20.486638+00', '2025-08-06 15:40:20.486638+00'),
	('0a32b244-b96b-45db-9fdb-f1b30e451588', 'timezone', '"Europe/Berlin"', '2025-08-06 15:40:20.486638+00', '2025-08-06 15:40:20.486638+00'),
	('84b88f32-3b2f-4bed-adb0-bd5f9d432cd9', 'default_standard_times', '"{\"start_time\":\"06:00\",\"end_time\":\"22:00\",\"days_of_week\":[1,2,3,4,5,6,0]}"', '2025-08-06 20:24:34.520042+00', '2025-08-06 20:24:34.520042+00');


--
-- Data for Name: user_roles; Type: TABLE DATA; Schema: public; Owner: postgres
--

INSERT INTO "public"."user_roles" ("id", "user_id", "role", "assigned_at", "assigned_by") VALUES
	('7566084f-8ea9-4cea-845c-a6fd33ea9350', 'c7e745d1-f09e-4819-936a-a15c06171e6d', 'admin', '2025-08-07 20:06:36.047623+00', NULL),
	('6dbb2ed9-8cbc-4993-8455-b1b1ea5b279f', 'c7e745d1-f09e-4819-936a-a15c06171e6d', 'mitglied', '2025-08-07 20:06:36.047623+00', NULL),
	('4173c124-cb6b-4de1-a9ce-009dad30ebd7', 'f87864aa-6f6e-4ab7-972d-ad442155d4f3', 'mitglied', '2025-08-13 11:25:35.803163+00', NULL),
	('607468cb-0d5d-4861-a062-2d847781fa7e', '939e5d34-2907-4e99-b0b1-24ff4ec72339', 'mitglied', '2025-08-14 17:10:13.424485+00', NULL);


--
-- Data for Name: work_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: work_log_assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--



--
-- Data for Name: buckets; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--

INSERT INTO "storage"."buckets" ("id", "name", "owner", "created_at", "updated_at", "public", "avif_autodetection", "file_size_limit", "allowed_mime_types", "owner_id", "type") VALUES
	('profile-images', 'profile-images', NULL, '2025-08-07 12:57:23.045313+00', '2025-08-07 12:57:23.045313+00', true, false, NULL, NULL, NULL, 'STANDARD');


--
-- Data for Name: buckets_analytics; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: objects; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: prefixes; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Data for Name: s3_multipart_uploads_parts; Type: TABLE DATA; Schema: storage; Owner: supabase_storage_admin
--



--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE SET; Schema: auth; Owner: supabase_auth_admin
--

SELECT pg_catalog.setval('"auth"."refresh_tokens_id_seq"', 57, true);


--
-- PostgreSQL database dump complete
--

RESET ALL;
