create extension if not exists "btree_gist" with schema "extensions";


create type "public"."account_type" as enum ('Member', 'Guest', 'Trainer', 'Admin');

create type "public"."app_permission" as enum ('members_view', 'members_create', 'members_edit', 'members_delete', 'courts_view', 'courts_book', 'courts_manage', 'courts_config', 'payments_view', 'payments_process', 'payments_manage', 'tournaments_view', 'tournaments_create', 'tournaments_manage', 'courses_view', 'courses_create', 'courses_manage', 'courses_participate', 'community_view', 'community_post', 'community_moderate', 'reports_view', 'reports_generate', 'reports_export', 'system_config', 'user_roles_manage', 'profile_edit_own', 'profile_view_others');

create type "public"."app_role" as enum ('admin', 'trainer', 'mitglied', 'guest', 'super_admin');

create type "public"."cancellation_status" as enum ('pending', 'approved', 'rejected');

create type "public"."membership_category" as enum ('Kind', 'Jugendlicher', 'Student', 'Erwachsener', 'Familie');

create type "public"."membership_type" as enum ('Child', 'Youth', 'Student', 'Adult', 'Couple', 'Family', 'Kinder', 'Jugendliche', 'Studenten', 'Erwachsene', 'Senioren', 'Paare', 'Familien');

create type "public"."meta_admin_role" as enum ('SUPER_ADMIN', 'SUPPORT_ADMIN', 'BILLING_ADMIN', 'READONLY');

create table "public"."accounts" (
    "id" uuid not null default gen_random_uuid(),
    "account_type" account_type not null default 'Member'::account_type,
    "first_name" text not null,
    "last_name" text not null,
    "email" text not null,
    "phone" text not null,
    "address_full" text,
    "address_street" text,
    "address_house_number" text,
    "address_postal_code" text,
    "address_city" text,
    "birth_date" date,
    "membership_type" membership_type,
    "age_type_mismatch_flag" boolean default false,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "billing_member_id" uuid,
    "user_id" uuid,
    "club_id" uuid
);


alter table "public"."accounts" enable row level security;

create table "public"."activities" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "hourly_rate" numeric(10,2),
    "created_at" timestamp with time zone not null default now(),
    "status" text not null default 'approved'::text,
    "requested_by" uuid,
    "approved_by" uuid,
    "approved_at" timestamp with time zone,
    "created_by" uuid,
    "club_id" uuid
);


alter table "public"."activities" enable row level security;

create table "public"."activity_registrations" (
    "id" uuid not null default gen_random_uuid(),
    "activity_id" uuid not null,
    "member_id" uuid not null,
    "registered_at" timestamp with time zone not null default now(),
    "status" text not null default 'registered'::text,
    "notes" text
);


alter table "public"."activity_registrations" enable row level security;

create table "public"."audit_logs" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "club_id" uuid,
    "action" text not null,
    "resource_type" text not null,
    "resource_id" text,
    "old_values" jsonb,
    "new_values" jsonb,
    "ip_address" inet,
    "user_agent" text,
    "session_id" text,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."audit_logs" enable row level security;

create table "public"."billing_agreements" (
    "id" uuid not null default gen_random_uuid(),
    "payer_account_id" uuid not null,
    "beneficiary_account_id" uuid not null,
    "share_percent" numeric(5,2),
    "valid_from" date not null default CURRENT_DATE,
    "valid_to" date
);


alter table "public"."billing_agreements" enable row level security;

create table "public"."bookings" (
    "id" uuid not null default gen_random_uuid(),
    "court_id" uuid not null,
    "booking_date" date not null,
    "start_time" time without time zone not null,
    "end_time" time without time zone not null,
    "player_name" text not null,
    "partner_name" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "created_by" uuid,
    "start_at" timestamp with time zone,
    "end_at" timestamp with time zone,
    "acting_account_id" uuid,
    "booked_for_account_id" uuid,
    "time_range" tstzrange generated always as (tstzrange(start_at, end_at, '[)'::text)) stored,
    "club_id" uuid
);


alter table "public"."bookings" enable row level security;

create table "public"."club_memberships" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "club_id" uuid not null,
    "role" text not null default 'member'::text,
    "joined_at" timestamp with time zone not null default now(),
    "is_active" boolean not null default true
);


alter table "public"."club_memberships" enable row level security;

create table "public"."club_subscriptions" (
    "id" uuid not null default gen_random_uuid(),
    "club_id" uuid not null,
    "plan_id" uuid not null,
    "stripe_subscription_id" text,
    "status" text not null default 'active'::text,
    "current_period_start" timestamp with time zone,
    "current_period_end" timestamp with time zone,
    "trial_end" timestamp with time zone,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."club_subscriptions" enable row level security;

create table "public"."clubs" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "slug" text not null,
    "custom_domain" text,
    "subdomain" text,
    "logo_url" text,
    "description" text,
    "settings" jsonb default '{}'::jsonb,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."clubs" enable row level security;

create table "public"."court_availability" (
    "id" uuid not null default gen_random_uuid(),
    "court_id" uuid not null,
    "start_time" time without time zone not null,
    "end_time" time without time zone not null,
    "days_of_week" integer[] not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."court_availability" enable row level security;

create table "public"."court_blocks" (
    "id" uuid not null default gen_random_uuid(),
    "court_id" uuid not null,
    "start_time" time without time zone,
    "end_time" time without time zone,
    "reason" text not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "start_date" date not null,
    "end_date" date not null
);


alter table "public"."court_blocks" enable row level security;

create table "public"."courts" (
    "id" uuid not null default gen_random_uuid(),
    "number" integer not null,
    "locked" boolean not null default false,
    "lock_reason" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "court_group" text,
    "surface_type" text,
    "club_id" uuid
);


alter table "public"."courts" enable row level security;

create table "public"."domains" (
    "id" uuid not null default gen_random_uuid(),
    "club_id" uuid not null,
    "domain_name" text not null,
    "domain_type" text not null,
    "is_primary" boolean not null default false,
    "is_verified" boolean not null default false,
    "ssl_status" text not null default 'pending'::text,
    "ssl_expires_at" timestamp with time zone,
    "dns_verified_at" timestamp with time zone,
    "verification_token" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."domains" enable row level security;

create table "public"."email_templates" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "subject" text not null,
    "content" text not null,
    "template_type" text not null,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."email_templates" enable row level security;

create table "public"."fee_assignments" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "fee_type_id" uuid not null,
    "assigned_at" timestamp with time zone not null default now(),
    "due_date" date,
    "paid_at" timestamp with time zone,
    "amount_paid" numeric(10,2),
    "payment_method" text,
    "payment_reference" text,
    "status" text not null default 'pending'::text,
    "notes" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "account_id" uuid
);


alter table "public"."fee_assignments" enable row level security;

create table "public"."fee_categories" (
    "id" uuid not null default gen_random_uuid(),
    "value" text not null,
    "display_name" text not null,
    "color" text,
    "sort_order" integer not null default 0,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."fee_categories" enable row level security;

create table "public"."fee_type_membership_types" (
    "id" uuid not null default gen_random_uuid(),
    "fee_type_id" uuid not null,
    "membership_type_id" uuid not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."fee_type_membership_types" enable row level security;

create table "public"."fee_types" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "amount" numeric(10,2) not null,
    "currency" text not null default 'EUR'::text,
    "billing_cycle" text not null default 'one_time'::text,
    "category" text not null default 'membership'::text,
    "is_active" boolean not null default true,
    "requires_membership" boolean not null default false,
    "age_restrictions" jsonb,
    "validity_period_days" integer,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "membership_category" membership_category,
    "membership_type_id" text,
    "category_id" uuid
);


alter table "public"."fee_types" enable row level security;

create table "public"."group_members" (
    "id" uuid not null default gen_random_uuid(),
    "group_id" uuid not null,
    "account_id" uuid not null,
    "role" text not null default 'member'::text,
    "added_at" timestamp with time zone not null default now()
);


alter table "public"."group_members" enable row level security;

create table "public"."groups" (
    "id" uuid not null default gen_random_uuid(),
    "type" membership_type not null,
    "total_fee" numeric(10,2) not null,
    "created_at" timestamp with time zone not null default now(),
    "billing_member_id" uuid
);


alter table "public"."groups" enable row level security;

create table "public"."guardianships" (
    "id" uuid not null default gen_random_uuid(),
    "guardian_account_id" uuid not null,
    "dependent_account_id" uuid not null,
    "valid_from" date not null default CURRENT_DATE,
    "valid_to" date
);


alter table "public"."guardianships" enable row level security;

create table "public"."household_members" (
    "id" uuid not null default gen_random_uuid(),
    "household_id" uuid not null,
    "account_id" uuid not null,
    "role" text not null,
    "valid_from" date not null default CURRENT_DATE,
    "valid_to" date
);


alter table "public"."household_members" enable row level security;

create table "public"."households" (
    "id" uuid not null default gen_random_uuid(),
    "name" text,
    "created_at" timestamp with time zone default now()
);


alter table "public"."households" enable row level security;

create table "public"."invites" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "method" text not null,
    "target" text,
    "status" text default 'pending'::text,
    "created_at" timestamp with time zone default now(),
    "expires_at" timestamp with time zone
);


alter table "public"."invites" enable row level security;

create table "public"."login_identities" (
    "id" uuid not null default gen_random_uuid(),
    "account_id" uuid not null,
    "type" text not null,
    "value" text,
    "is_primary" boolean default false,
    "created_at" timestamp with time zone default now()
);


alter table "public"."login_identities" enable row level security;

create table "public"."members" (
    "id" uuid not null default gen_random_uuid(),
    "first_name" text not null,
    "last_name" text not null,
    "birth_date" date not null,
    "address" text not null,
    "email" text not null,
    "phone" text not null,
    "iban" text not null,
    "membership_type" membership_type not null,
    "annual_fee" numeric(10,2) not null,
    "payment_status" text not null default 'unpaid'::text,
    "group_id" uuid,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "club_id" uuid
);


alter table "public"."members" enable row level security;

create table "public"."membership_cancellations" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "account_id" uuid,
    "group_id" uuid,
    "effective_date" date not null,
    "reason" text,
    "status" cancellation_status not null default 'pending'::cancellation_status,
    "requested_at" timestamp with time zone not null default now(),
    "processed_at" timestamp with time zone,
    "processed_by" uuid,
    "notes" text
);


alter table "public"."membership_cancellations" enable row level security;

create table "public"."membership_types" (
    "id" uuid not null default gen_random_uuid(),
    "value" text not null,
    "display_name" text not null,
    "description" text,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "is_group_type" boolean not null default false
);


alter table "public"."membership_types" enable row level security;

create table "public"."meta_admin_permissions" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "role" meta_admin_role not null,
    "granted_by" uuid not null,
    "granted_at" timestamp with time zone not null default now(),
    "expires_at" timestamp with time zone,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."meta_admin_permissions" enable row level security;

create table "public"."plans" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "price_monthly" numeric(10,2),
    "price_yearly" numeric(10,2),
    "limits" jsonb not null default '{}'::jsonb,
    "features" jsonb not null default '[]'::jsonb,
    "is_active" boolean not null default true,
    "sort_order" integer not null default 0,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."plans" enable row level security;

create table "public"."profiles" (
    "id" uuid not null,
    "first_name" text not null,
    "last_name" text not null,
    "birth_date" date not null,
    "street" text not null,
    "house_number" text not null,
    "postal_code" text not null,
    "city" text not null,
    "email" text not null,
    "membership_category" membership_category not null,
    "join_date" date not null default CURRENT_DATE,
    "profile_image_url" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."profiles" enable row level security;

create table "public"."role_permissions" (
    "id" uuid not null default gen_random_uuid(),
    "role" app_role not null,
    "permission" app_permission not null,
    "created_at" timestamp with time zone default now()
);


alter table "public"."role_permissions" enable row level security;

create table "public"."support_tickets" (
    "id" uuid not null default gen_random_uuid(),
    "club_id" uuid,
    "user_id" uuid,
    "subject" text not null,
    "description" text not null,
    "status" text not null default 'open'::text,
    "priority" text not null default 'medium'::text,
    "assigned_to" uuid,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."support_tickets" enable row level security;

create table "public"."system_metrics" (
    "id" uuid not null default gen_random_uuid(),
    "metric_name" text not null,
    "metric_value" numeric not null,
    "labels" jsonb default '{}'::jsonb,
    "recorded_at" timestamp with time zone not null default now()
);


alter table "public"."system_metrics" enable row level security;

create table "public"."system_settings" (
    "id" uuid not null default gen_random_uuid(),
    "key" text not null,
    "value" jsonb not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."system_settings" enable row level security;

create table "public"."user_roles" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "role" app_role not null,
    "assigned_at" timestamp with time zone default now(),
    "assigned_by" uuid
);


alter table "public"."user_roles" enable row level security;

create table "public"."work_log_assignments" (
    "id" uuid not null default gen_random_uuid(),
    "work_log_id" uuid not null,
    "member_id" uuid not null
);


alter table "public"."work_log_assignments" enable row level security;

create table "public"."work_logs" (
    "id" uuid not null default gen_random_uuid(),
    "activity_id" uuid not null,
    "date" date not null,
    "duration_hours" numeric(5,2) not null,
    "created_at" timestamp with time zone not null default now()
);


alter table "public"."work_logs" enable row level security;

CREATE UNIQUE INDEX accounts_email_key ON public.accounts USING btree (email);

CREATE UNIQUE INDEX accounts_pkey ON public.accounts USING btree (id);

CREATE UNIQUE INDEX accounts_user_id_unique ON public.accounts USING btree (user_id);

CREATE UNIQUE INDEX activities_pkey ON public.activities USING btree (id);

CREATE UNIQUE INDEX activity_registrations_activity_id_member_id_key ON public.activity_registrations USING btree (activity_id, member_id);

CREATE UNIQUE INDEX activity_registrations_pkey ON public.activity_registrations USING btree (id);

CREATE UNIQUE INDEX audit_logs_pkey ON public.audit_logs USING btree (id);

CREATE UNIQUE INDEX billing_agreements_pkey ON public.billing_agreements USING btree (id);

select 1; -- CREATE INDEX bookings_no_overlap ON public.bookings USING gist (court_id, time_range);

CREATE UNIQUE INDEX bookings_pkey ON public.bookings USING btree (id);

CREATE UNIQUE INDEX club_memberships_pkey ON public.club_memberships USING btree (id);

CREATE UNIQUE INDEX club_memberships_user_id_club_id_key ON public.club_memberships USING btree (user_id, club_id);

CREATE UNIQUE INDEX club_subscriptions_pkey ON public.club_subscriptions USING btree (id);

CREATE UNIQUE INDEX club_subscriptions_stripe_subscription_id_key ON public.club_subscriptions USING btree (stripe_subscription_id);

CREATE UNIQUE INDEX clubs_custom_domain_key ON public.clubs USING btree (custom_domain);

CREATE UNIQUE INDEX clubs_pkey ON public.clubs USING btree (id);

CREATE UNIQUE INDEX clubs_slug_key ON public.clubs USING btree (slug);

CREATE UNIQUE INDEX clubs_subdomain_key ON public.clubs USING btree (subdomain);

CREATE UNIQUE INDEX court_availability_pkey ON public.court_availability USING btree (id);

CREATE UNIQUE INDEX court_blocks_pkey ON public.court_blocks USING btree (id);

CREATE UNIQUE INDEX courts_number_key ON public.courts USING btree (number);

CREATE UNIQUE INDEX courts_pkey ON public.courts USING btree (id);

CREATE UNIQUE INDEX domains_domain_name_key ON public.domains USING btree (domain_name);

CREATE UNIQUE INDEX domains_pkey ON public.domains USING btree (id);

CREATE UNIQUE INDEX email_templates_pkey ON public.email_templates USING btree (id);

CREATE UNIQUE INDEX fee_assignments_pkey ON public.fee_assignments USING btree (id);

CREATE UNIQUE INDEX fee_categories_pkey ON public.fee_categories USING btree (id);

CREATE UNIQUE INDEX fee_categories_value_key ON public.fee_categories USING btree (value);

CREATE UNIQUE INDEX fee_type_membership_types_pkey ON public.fee_type_membership_types USING btree (id);

CREATE UNIQUE INDEX fee_type_membership_types_unique ON public.fee_type_membership_types USING btree (fee_type_id, membership_type_id);

CREATE UNIQUE INDEX fee_types_pkey ON public.fee_types USING btree (id);

CREATE UNIQUE INDEX group_members_group_id_account_id_key ON public.group_members USING btree (group_id, account_id);

CREATE UNIQUE INDEX group_members_pkey ON public.group_members USING btree (id);

CREATE UNIQUE INDEX groups_pkey ON public.groups USING btree (id);

CREATE UNIQUE INDEX guardianships_pkey ON public.guardianships USING btree (id);

CREATE UNIQUE INDEX household_members_pkey ON public.household_members USING btree (id);

CREATE UNIQUE INDEX households_pkey ON public.households USING btree (id);

CREATE INDEX idx_accounts_billing_member ON public.accounts USING btree (billing_member_id);

CREATE INDEX idx_activity_registrations_activity_id ON public.activity_registrations USING btree (activity_id);

CREATE INDEX idx_activity_registrations_member_id ON public.activity_registrations USING btree (member_id);

CREATE INDEX idx_bookings_booked_for ON public.bookings USING btree (booked_for_account_id, start_at);

CREATE INDEX idx_bookings_court_start ON public.bookings USING btree (court_id, start_at);

CREATE INDEX idx_courts_court_group ON public.courts USING btree (court_group);

CREATE INDEX idx_courts_surface_type ON public.courts USING btree (surface_type);

CREATE INDEX idx_fee_types_category_id ON public.fee_types USING btree (category_id);

CREATE INDEX idx_fee_types_membership_category ON public.fee_types USING btree (membership_category);

CREATE INDEX idx_groups_billing_member ON public.groups USING btree (billing_member_id);

CREATE INDEX idx_guardianships_guardian_active ON public.guardianships USING btree (guardian_account_id) WHERE (valid_to IS NULL);

CREATE INDEX idx_members_email ON public.members USING btree (email);

CREATE INDEX idx_members_group_id ON public.members USING btree (group_id);

CREATE INDEX idx_members_membership_type ON public.members USING btree (membership_type);

CREATE INDEX idx_membership_types_is_group_type ON public.membership_types USING btree (is_group_type);

CREATE INDEX idx_work_log_assignments_member_id ON public.work_log_assignments USING btree (member_id);

CREATE INDEX idx_work_logs_date ON public.work_logs USING btree (date);

CREATE UNIQUE INDEX invites_pkey ON public.invites USING btree (id);

CREATE UNIQUE INDEX login_identities_pkey ON public.login_identities USING btree (id);

CREATE UNIQUE INDEX login_identities_type_value_key ON public.login_identities USING btree (type, value);

CREATE UNIQUE INDEX members_email_key ON public.members USING btree (email);

CREATE UNIQUE INDEX members_pkey ON public.members USING btree (id);

CREATE UNIQUE INDEX membership_cancellations_pkey ON public.membership_cancellations USING btree (id);

CREATE UNIQUE INDEX membership_types_pkey ON public.membership_types USING btree (id);

CREATE UNIQUE INDEX membership_types_value_key ON public.membership_types USING btree (value);

CREATE UNIQUE INDEX meta_admin_permissions_pkey ON public.meta_admin_permissions USING btree (id);

CREATE UNIQUE INDEX meta_admin_permissions_user_id_role_key ON public.meta_admin_permissions USING btree (user_id, role);

CREATE UNIQUE INDEX plans_pkey ON public.plans USING btree (id);

CREATE UNIQUE INDEX profiles_email_key ON public.profiles USING btree (email);

CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (id);

CREATE UNIQUE INDEX role_permissions_pkey ON public.role_permissions USING btree (id);

CREATE UNIQUE INDEX role_permissions_role_permission_key ON public.role_permissions USING btree (role, permission);

CREATE UNIQUE INDEX support_tickets_pkey ON public.support_tickets USING btree (id);

CREATE UNIQUE INDEX system_metrics_pkey ON public.system_metrics USING btree (id);

CREATE UNIQUE INDEX system_settings_key_key ON public.system_settings USING btree (key);

CREATE UNIQUE INDEX system_settings_pkey ON public.system_settings USING btree (id);

CREATE UNIQUE INDEX uniq_guard_active ON public.guardianships USING btree (guardian_account_id, dependent_account_id) WHERE (valid_to IS NULL);

CREATE UNIQUE INDEX uniq_household_member_active ON public.household_members USING btree (household_id, account_id) WHERE (valid_to IS NULL);

CREATE UNIQUE INDEX user_roles_pkey ON public.user_roles USING btree (id);

CREATE UNIQUE INDEX user_roles_user_id_role_key ON public.user_roles USING btree (user_id, role);

CREATE UNIQUE INDEX work_log_assignments_pkey ON public.work_log_assignments USING btree (id);

CREATE UNIQUE INDEX work_log_assignments_work_log_id_member_id_key ON public.work_log_assignments USING btree (work_log_id, member_id);

CREATE UNIQUE INDEX work_logs_pkey ON public.work_logs USING btree (id);

alter table "public"."accounts" add constraint "accounts_pkey" PRIMARY KEY using index "accounts_pkey";

alter table "public"."activities" add constraint "activities_pkey" PRIMARY KEY using index "activities_pkey";

alter table "public"."activity_registrations" add constraint "activity_registrations_pkey" PRIMARY KEY using index "activity_registrations_pkey";

alter table "public"."audit_logs" add constraint "audit_logs_pkey" PRIMARY KEY using index "audit_logs_pkey";

alter table "public"."billing_agreements" add constraint "billing_agreements_pkey" PRIMARY KEY using index "billing_agreements_pkey";

alter table "public"."bookings" add constraint "bookings_pkey" PRIMARY KEY using index "bookings_pkey";

alter table "public"."club_memberships" add constraint "club_memberships_pkey" PRIMARY KEY using index "club_memberships_pkey";

alter table "public"."club_subscriptions" add constraint "club_subscriptions_pkey" PRIMARY KEY using index "club_subscriptions_pkey";

alter table "public"."clubs" add constraint "clubs_pkey" PRIMARY KEY using index "clubs_pkey";

alter table "public"."court_availability" add constraint "court_availability_pkey" PRIMARY KEY using index "court_availability_pkey";

alter table "public"."court_blocks" add constraint "court_blocks_pkey" PRIMARY KEY using index "court_blocks_pkey";

alter table "public"."courts" add constraint "courts_pkey" PRIMARY KEY using index "courts_pkey";

alter table "public"."domains" add constraint "domains_pkey" PRIMARY KEY using index "domains_pkey";

alter table "public"."email_templates" add constraint "email_templates_pkey" PRIMARY KEY using index "email_templates_pkey";

alter table "public"."fee_assignments" add constraint "fee_assignments_pkey" PRIMARY KEY using index "fee_assignments_pkey";

alter table "public"."fee_categories" add constraint "fee_categories_pkey" PRIMARY KEY using index "fee_categories_pkey";

alter table "public"."fee_type_membership_types" add constraint "fee_type_membership_types_pkey" PRIMARY KEY using index "fee_type_membership_types_pkey";

alter table "public"."fee_types" add constraint "fee_types_pkey" PRIMARY KEY using index "fee_types_pkey";

alter table "public"."group_members" add constraint "group_members_pkey" PRIMARY KEY using index "group_members_pkey";

alter table "public"."groups" add constraint "groups_pkey" PRIMARY KEY using index "groups_pkey";

alter table "public"."guardianships" add constraint "guardianships_pkey" PRIMARY KEY using index "guardianships_pkey";

alter table "public"."household_members" add constraint "household_members_pkey" PRIMARY KEY using index "household_members_pkey";

alter table "public"."households" add constraint "households_pkey" PRIMARY KEY using index "households_pkey";

alter table "public"."invites" add constraint "invites_pkey" PRIMARY KEY using index "invites_pkey";

alter table "public"."login_identities" add constraint "login_identities_pkey" PRIMARY KEY using index "login_identities_pkey";

alter table "public"."members" add constraint "members_pkey" PRIMARY KEY using index "members_pkey";

alter table "public"."membership_cancellations" add constraint "membership_cancellations_pkey" PRIMARY KEY using index "membership_cancellations_pkey";

alter table "public"."membership_types" add constraint "membership_types_pkey" PRIMARY KEY using index "membership_types_pkey";

alter table "public"."meta_admin_permissions" add constraint "meta_admin_permissions_pkey" PRIMARY KEY using index "meta_admin_permissions_pkey";

alter table "public"."plans" add constraint "plans_pkey" PRIMARY KEY using index "plans_pkey";

alter table "public"."profiles" add constraint "profiles_pkey" PRIMARY KEY using index "profiles_pkey";

alter table "public"."role_permissions" add constraint "role_permissions_pkey" PRIMARY KEY using index "role_permissions_pkey";

alter table "public"."support_tickets" add constraint "support_tickets_pkey" PRIMARY KEY using index "support_tickets_pkey";

alter table "public"."system_metrics" add constraint "system_metrics_pkey" PRIMARY KEY using index "system_metrics_pkey";

alter table "public"."system_settings" add constraint "system_settings_pkey" PRIMARY KEY using index "system_settings_pkey";

alter table "public"."user_roles" add constraint "user_roles_pkey" PRIMARY KEY using index "user_roles_pkey";

alter table "public"."work_log_assignments" add constraint "work_log_assignments_pkey" PRIMARY KEY using index "work_log_assignments_pkey";

alter table "public"."work_logs" add constraint "work_logs_pkey" PRIMARY KEY using index "work_logs_pkey";

alter table "public"."accounts" add constraint "accounts_billing_member_fk" FOREIGN KEY (billing_member_id) REFERENCES accounts(id) ON DELETE SET NULL not valid;

alter table "public"."accounts" validate constraint "accounts_billing_member_fk";

alter table "public"."accounts" add constraint "accounts_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."accounts" validate constraint "accounts_club_id_fkey";

alter table "public"."accounts" add constraint "accounts_email_key" UNIQUE using index "accounts_email_key";

alter table "public"."accounts" add constraint "accounts_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."accounts" validate constraint "accounts_user_id_fkey";

alter table "public"."accounts" add constraint "accounts_user_id_unique" UNIQUE using index "accounts_user_id_unique";

alter table "public"."activities" add constraint "activities_approved_by_fkey" FOREIGN KEY (approved_by) REFERENCES auth.users(id) not valid;

alter table "public"."activities" validate constraint "activities_approved_by_fkey";

alter table "public"."activities" add constraint "activities_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."activities" validate constraint "activities_club_id_fkey";

alter table "public"."activities" add constraint "activities_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."activities" validate constraint "activities_created_by_fkey";

alter table "public"."activities" add constraint "activities_requested_by_fkey" FOREIGN KEY (requested_by) REFERENCES auth.users(id) not valid;

alter table "public"."activities" validate constraint "activities_requested_by_fkey";

alter table "public"."activity_registrations" add constraint "activity_registrations_activity_id_fkey" FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE not valid;

alter table "public"."activity_registrations" validate constraint "activity_registrations_activity_id_fkey";

alter table "public"."activity_registrations" add constraint "activity_registrations_activity_id_member_id_key" UNIQUE using index "activity_registrations_activity_id_member_id_key";

alter table "public"."activity_registrations" add constraint "activity_registrations_member_id_fkey" FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE not valid;

alter table "public"."activity_registrations" validate constraint "activity_registrations_member_id_fkey";

alter table "public"."audit_logs" add constraint "audit_logs_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."audit_logs" validate constraint "audit_logs_club_id_fkey";

alter table "public"."audit_logs" add constraint "audit_logs_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."audit_logs" validate constraint "audit_logs_user_id_fkey";

alter table "public"."billing_agreements" add constraint "billing_agreements_beneficiary_account_id_fkey" FOREIGN KEY (beneficiary_account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."billing_agreements" validate constraint "billing_agreements_beneficiary_account_id_fkey";

alter table "public"."billing_agreements" add constraint "billing_agreements_payer_account_id_fkey" FOREIGN KEY (payer_account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."billing_agreements" validate constraint "billing_agreements_payer_account_id_fkey";

alter table "public"."billing_agreements" add constraint "billing_agreements_share_percent_check" CHECK (((share_percent >= (0)::numeric) AND (share_percent <= (100)::numeric))) not valid;

alter table "public"."billing_agreements" validate constraint "billing_agreements_share_percent_check";

alter table "public"."billing_agreements" add constraint "payer_not_beneficiary" CHECK ((payer_account_id <> beneficiary_account_id)) not valid;

alter table "public"."billing_agreements" validate constraint "payer_not_beneficiary";

alter table "public"."bookings" add constraint "bookings_acting_account_id_fkey" FOREIGN KEY (acting_account_id) REFERENCES accounts(id) not valid;

alter table "public"."bookings" validate constraint "bookings_acting_account_id_fkey";

alter table "public"."bookings" add constraint "bookings_booked_for_account_id_fkey" FOREIGN KEY (booked_for_account_id) REFERENCES accounts(id) not valid;

alter table "public"."bookings" validate constraint "bookings_booked_for_account_id_fkey";

alter table "public"."bookings" add constraint "bookings_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."bookings" validate constraint "bookings_club_id_fkey";

alter table "public"."bookings" add constraint "bookings_court_id_fkey" FOREIGN KEY (court_id) REFERENCES courts(id) ON DELETE CASCADE not valid;

alter table "public"."bookings" validate constraint "bookings_court_id_fkey";

alter table "public"."bookings" add constraint "bookings_created_by_fkey" FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL not valid;

alter table "public"."bookings" validate constraint "bookings_created_by_fkey";

alter table "public"."bookings" add constraint "bookings_no_overlap" EXCLUDE USING gist (court_id WITH =, time_range WITH &&);

alter table "public"."club_memberships" add constraint "club_memberships_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE CASCADE not valid;

alter table "public"."club_memberships" validate constraint "club_memberships_club_id_fkey";

alter table "public"."club_memberships" add constraint "club_memberships_user_id_club_id_key" UNIQUE using index "club_memberships_user_id_club_id_key";

alter table "public"."club_subscriptions" add constraint "club_subscriptions_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE CASCADE not valid;

alter table "public"."club_subscriptions" validate constraint "club_subscriptions_club_id_fkey";

alter table "public"."club_subscriptions" add constraint "club_subscriptions_plan_id_fkey" FOREIGN KEY (plan_id) REFERENCES plans(id) not valid;

alter table "public"."club_subscriptions" validate constraint "club_subscriptions_plan_id_fkey";

alter table "public"."club_subscriptions" add constraint "club_subscriptions_status_check" CHECK ((status = ANY (ARRAY['active'::text, 'trialing'::text, 'past_due'::text, 'canceled'::text, 'unpaid'::text]))) not valid;

alter table "public"."club_subscriptions" validate constraint "club_subscriptions_status_check";

alter table "public"."club_subscriptions" add constraint "club_subscriptions_stripe_subscription_id_key" UNIQUE using index "club_subscriptions_stripe_subscription_id_key";

alter table "public"."clubs" add constraint "clubs_custom_domain_key" UNIQUE using index "clubs_custom_domain_key";

alter table "public"."clubs" add constraint "clubs_slug_key" UNIQUE using index "clubs_slug_key";

alter table "public"."clubs" add constraint "clubs_subdomain_key" UNIQUE using index "clubs_subdomain_key";

alter table "public"."court_availability" add constraint "court_availability_court_id_fkey" FOREIGN KEY (court_id) REFERENCES courts(id) ON DELETE CASCADE not valid;

alter table "public"."court_availability" validate constraint "court_availability_court_id_fkey";

alter table "public"."court_blocks" add constraint "check_valid_date_range" CHECK ((end_date >= start_date)) not valid;

alter table "public"."court_blocks" validate constraint "check_valid_date_range";

alter table "public"."court_blocks" add constraint "court_blocks_court_id_fkey" FOREIGN KEY (court_id) REFERENCES courts(id) ON DELETE CASCADE not valid;

alter table "public"."court_blocks" validate constraint "court_blocks_court_id_fkey";

alter table "public"."courts" add constraint "courts_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."courts" validate constraint "courts_club_id_fkey";

alter table "public"."courts" add constraint "courts_number_key" UNIQUE using index "courts_number_key";

alter table "public"."domains" add constraint "domains_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) ON DELETE CASCADE not valid;

alter table "public"."domains" validate constraint "domains_club_id_fkey";

alter table "public"."domains" add constraint "domains_domain_name_key" UNIQUE using index "domains_domain_name_key";

alter table "public"."domains" add constraint "domains_domain_type_check" CHECK ((domain_type = ANY (ARRAY['custom'::text, 'subdomain'::text]))) not valid;

alter table "public"."domains" validate constraint "domains_domain_type_check";

alter table "public"."domains" add constraint "domains_ssl_status_check" CHECK ((ssl_status = ANY (ARRAY['pending'::text, 'active'::text, 'failed'::text, 'expired'::text]))) not valid;

alter table "public"."domains" validate constraint "domains_ssl_status_check";

alter table "public"."fee_assignments" add constraint "fee_assignments_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) not valid;

alter table "public"."fee_assignments" validate constraint "fee_assignments_account_id_fkey";

alter table "public"."fee_assignments" add constraint "fee_assignments_fee_type_id_fkey" FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE not valid;

alter table "public"."fee_assignments" validate constraint "fee_assignments_fee_type_id_fkey";

alter table "public"."fee_assignments" add constraint "fee_assignments_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."fee_assignments" validate constraint "fee_assignments_user_id_fkey";

alter table "public"."fee_categories" add constraint "fee_categories_value_key" UNIQUE using index "fee_categories_value_key";

alter table "public"."fee_type_membership_types" add constraint "fee_type_membership_types_fee_type_id_fkey" FOREIGN KEY (fee_type_id) REFERENCES fee_types(id) ON DELETE CASCADE not valid;

alter table "public"."fee_type_membership_types" validate constraint "fee_type_membership_types_fee_type_id_fkey";

alter table "public"."fee_type_membership_types" add constraint "fee_type_membership_types_membership_type_id_fkey" FOREIGN KEY (membership_type_id) REFERENCES membership_types(id) ON DELETE CASCADE not valid;

alter table "public"."fee_type_membership_types" validate constraint "fee_type_membership_types_membership_type_id_fkey";

alter table "public"."fee_type_membership_types" add constraint "fee_type_membership_types_unique" UNIQUE using index "fee_type_membership_types_unique";

alter table "public"."fee_types" add constraint "fee_types_category_id_fkey" FOREIGN KEY (category_id) REFERENCES fee_categories(id) ON DELETE SET NULL not valid;

alter table "public"."fee_types" validate constraint "fee_types_category_id_fkey";

alter table "public"."fee_types" add constraint "fee_types_membership_type_id_fkey" FOREIGN KEY (membership_type_id) REFERENCES membership_types(value) not valid;

alter table "public"."fee_types" validate constraint "fee_types_membership_type_id_fkey";

alter table "public"."group_members" add constraint "group_members_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."group_members" validate constraint "group_members_account_id_fkey";

alter table "public"."group_members" add constraint "group_members_group_id_account_id_key" UNIQUE using index "group_members_group_id_account_id_key";

alter table "public"."group_members" add constraint "group_members_group_id_fkey" FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE CASCADE not valid;

alter table "public"."group_members" validate constraint "group_members_group_id_fkey";

alter table "public"."groups" add constraint "groups_billing_member_fk" FOREIGN KEY (billing_member_id) REFERENCES accounts(id) ON DELETE SET NULL not valid;

alter table "public"."groups" validate constraint "groups_billing_member_fk";

alter table "public"."groups" add constraint "groups_type_check" CHECK ((type = ANY (ARRAY['Couple'::membership_type, 'Family'::membership_type]))) not valid;

alter table "public"."groups" validate constraint "groups_type_check";

alter table "public"."guardianships" add constraint "guardian_not_self" CHECK ((guardian_account_id <> dependent_account_id)) not valid;

alter table "public"."guardianships" validate constraint "guardian_not_self";

alter table "public"."guardianships" add constraint "guardianships_dependent_account_id_fkey" FOREIGN KEY (dependent_account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."guardianships" validate constraint "guardianships_dependent_account_id_fkey";

alter table "public"."guardianships" add constraint "guardianships_guardian_account_id_fkey" FOREIGN KEY (guardian_account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."guardianships" validate constraint "guardianships_guardian_account_id_fkey";

alter table "public"."household_members" add constraint "household_members_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."household_members" validate constraint "household_members_account_id_fkey";

alter table "public"."household_members" add constraint "household_members_household_id_fkey" FOREIGN KEY (household_id) REFERENCES households(id) ON DELETE CASCADE not valid;

alter table "public"."household_members" validate constraint "household_members_household_id_fkey";

alter table "public"."household_members" add constraint "household_members_role_check" CHECK ((role = ANY (ARRAY['owner'::text, 'adult'::text, 'child'::text]))) not valid;

alter table "public"."household_members" validate constraint "household_members_role_check";

alter table "public"."invites" add constraint "invites_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."invites" validate constraint "invites_account_id_fkey";

alter table "public"."invites" add constraint "invites_method_check" CHECK ((method = ANY (ARRAY['email'::text, 'phone'::text, 'passkey'::text]))) not valid;

alter table "public"."invites" validate constraint "invites_method_check";

alter table "public"."invites" add constraint "invites_status_check" CHECK ((status = ANY (ARRAY['pending'::text, 'accepted'::text, 'expired'::text, 'revoked'::text]))) not valid;

alter table "public"."invites" validate constraint "invites_status_check";

alter table "public"."login_identities" add constraint "login_identities_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE not valid;

alter table "public"."login_identities" validate constraint "login_identities_account_id_fkey";

alter table "public"."login_identities" add constraint "login_identities_type_check" CHECK ((type = ANY (ARRAY['email'::text, 'phone'::text, 'passkey'::text, 'username'::text]))) not valid;

alter table "public"."login_identities" validate constraint "login_identities_type_check";

alter table "public"."login_identities" add constraint "login_identities_type_value_key" UNIQUE using index "login_identities_type_value_key";

alter table "public"."members" add constraint "fk_members_group" FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE SET NULL not valid;

alter table "public"."members" validate constraint "fk_members_group";

alter table "public"."members" add constraint "members_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."members" validate constraint "members_club_id_fkey";

alter table "public"."members" add constraint "members_email_key" UNIQUE using index "members_email_key";

alter table "public"."members" add constraint "members_payment_status_check" CHECK ((payment_status = ANY (ARRAY['paid'::text, 'unpaid'::text]))) not valid;

alter table "public"."members" validate constraint "members_payment_status_check";

alter table "public"."membership_cancellations" add constraint "membership_cancellations_account_id_fkey" FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE SET NULL not valid;

alter table "public"."membership_cancellations" validate constraint "membership_cancellations_account_id_fkey";

alter table "public"."membership_cancellations" add constraint "membership_cancellations_group_id_fkey" FOREIGN KEY (group_id) REFERENCES groups(id) ON DELETE SET NULL not valid;

alter table "public"."membership_cancellations" validate constraint "membership_cancellations_group_id_fkey";

alter table "public"."membership_cancellations" add constraint "membership_cancellations_processed_by_fkey" FOREIGN KEY (processed_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."membership_cancellations" validate constraint "membership_cancellations_processed_by_fkey";

alter table "public"."membership_cancellations" add constraint "membership_cancellations_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."membership_cancellations" validate constraint "membership_cancellations_user_id_fkey";

alter table "public"."membership_types" add constraint "membership_types_value_key" UNIQUE using index "membership_types_value_key";

alter table "public"."meta_admin_permissions" add constraint "meta_admin_permissions_granted_by_fkey" FOREIGN KEY (granted_by) REFERENCES auth.users(id) not valid;

alter table "public"."meta_admin_permissions" validate constraint "meta_admin_permissions_granted_by_fkey";

alter table "public"."meta_admin_permissions" add constraint "meta_admin_permissions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."meta_admin_permissions" validate constraint "meta_admin_permissions_user_id_fkey";

alter table "public"."meta_admin_permissions" add constraint "meta_admin_permissions_user_id_role_key" UNIQUE using index "meta_admin_permissions_user_id_role_key";

alter table "public"."profiles" add constraint "profiles_email_key" UNIQUE using index "profiles_email_key";

alter table "public"."profiles" add constraint "profiles_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."profiles" validate constraint "profiles_id_fkey";

alter table "public"."role_permissions" add constraint "role_permissions_role_permission_key" UNIQUE using index "role_permissions_role_permission_key";

alter table "public"."support_tickets" add constraint "support_tickets_assigned_to_fkey" FOREIGN KEY (assigned_to) REFERENCES auth.users(id) not valid;

alter table "public"."support_tickets" validate constraint "support_tickets_assigned_to_fkey";

alter table "public"."support_tickets" add constraint "support_tickets_club_id_fkey" FOREIGN KEY (club_id) REFERENCES clubs(id) not valid;

alter table "public"."support_tickets" validate constraint "support_tickets_club_id_fkey";

alter table "public"."support_tickets" add constraint "support_tickets_priority_check" CHECK ((priority = ANY (ARRAY['low'::text, 'medium'::text, 'high'::text, 'urgent'::text]))) not valid;

alter table "public"."support_tickets" validate constraint "support_tickets_priority_check";

alter table "public"."support_tickets" add constraint "support_tickets_status_check" CHECK ((status = ANY (ARRAY['open'::text, 'in_progress'::text, 'resolved'::text, 'closed'::text]))) not valid;

alter table "public"."support_tickets" validate constraint "support_tickets_status_check";

alter table "public"."support_tickets" add constraint "support_tickets_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."support_tickets" validate constraint "support_tickets_user_id_fkey";

alter table "public"."system_settings" add constraint "system_settings_key_key" UNIQUE using index "system_settings_key_key";

alter table "public"."user_roles" add constraint "user_roles_assigned_by_fkey" FOREIGN KEY (assigned_by) REFERENCES auth.users(id) not valid;

alter table "public"."user_roles" validate constraint "user_roles_assigned_by_fkey";

alter table "public"."user_roles" add constraint "user_roles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."user_roles" validate constraint "user_roles_user_id_fkey";

alter table "public"."user_roles" add constraint "user_roles_user_id_role_key" UNIQUE using index "user_roles_user_id_role_key";

alter table "public"."work_log_assignments" add constraint "work_log_assignments_member_id_fkey" FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE not valid;

alter table "public"."work_log_assignments" validate constraint "work_log_assignments_member_id_fkey";

alter table "public"."work_log_assignments" add constraint "work_log_assignments_work_log_id_fkey" FOREIGN KEY (work_log_id) REFERENCES work_logs(id) ON DELETE CASCADE not valid;

alter table "public"."work_log_assignments" validate constraint "work_log_assignments_work_log_id_fkey";

alter table "public"."work_log_assignments" add constraint "work_log_assignments_work_log_id_member_id_key" UNIQUE using index "work_log_assignments_work_log_id_member_id_key";

alter table "public"."work_logs" add constraint "work_logs_activity_id_fkey" FOREIGN KEY (activity_id) REFERENCES activities(id) ON DELETE CASCADE not valid;

alter table "public"."work_logs" validate constraint "work_logs_activity_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.account_belongs_to_user(_account_id uuid, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.accounts a
    WHERE a.id = _account_id
      AND a.user_id = _user_id
  );
$function$
;

CREATE OR REPLACE FUNCTION public.effective_rights(u uuid)
 RETURNS TABLE(account_id uuid)
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  -- Eigener Account (mit Login)
  SELECT a.id
  FROM public.accounts a
  WHERE a.user_id = u
  UNION
  -- Abhängige (ohne eigenen Login) aus aktiven Sorgeverhältnissen
  SELECT g.dependent_account_id
  FROM public.guardianships g
  JOIN public.accounts dep ON dep.id = g.dependent_account_id
  WHERE g.guardian_account_id IN (
    SELECT id FROM public.accounts WHERE user_id = u
  )
    AND g.valid_to IS NULL
    AND dep.user_id IS NULL
$function$
;

CREATE OR REPLACE FUNCTION public.get_current_club_id()
 RETURNS uuid
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT COALESCE(
    current_setting('app.current_club_id', true)::uuid,
    null
  );
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_permissions(_user_id uuid)
 RETURNS TABLE(permission app_permission)
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT DISTINCT rp.permission
  FROM public.user_roles ur
  JOIN public.role_permissions rp ON ur.role = rp.role
  WHERE ur.user_id = _user_id;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_roles(_user_id uuid)
 RETURNS TABLE(role app_role)
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT ur.role
  FROM public.user_roles ur
  WHERE ur.user_id = _user_id;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- Add some logging to help debug
  RAISE LOG 'Creating profile for user: %', NEW.id;
  
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    membership_category
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
    CASE 
      WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
      THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
      ELSE CURRENT_DATE
    END,
    COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'house_number', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
    NEW.email,
    COALESCE((NEW.raw_user_meta_data ->> 'membership_category')::public.membership_category, 'Erwachsener'::public.membership_category)
  );
  
  -- Assign default 'mitglied' role to new users
  INSERT INTO public.user_roles (user_id, role)
  VALUES (NEW.id, 'mitglied');
  
  RAISE LOG 'Profile and default role created successfully for user: %', NEW.id;
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
    RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.has_meta_admin_role(_user_id uuid, _role meta_admin_role)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.meta_admin_permissions
    WHERE user_id = _user_id
      AND role = _role
      AND (expires_at IS NULL OR expires_at > now())
  )
$function$
;

CREATE OR REPLACE FUNCTION public.has_permission(_user_id uuid, _permission app_permission)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles ur
    JOIN public.role_permissions rp ON ur.role = rp.role
    WHERE ur.user_id = _user_id
      AND rp.permission = _permission
  );
$function$
;

CREATE OR REPLACE FUNCTION public.has_role(_user_id uuid, _role app_role)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.user_roles
    WHERE user_id = _user_id
      AND role = _role
  );
$function$
;

CREATE OR REPLACE FUNCTION public.is_billing_member_for_group(_group_id uuid, _user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.groups g
    JOIN public.accounts a ON a.id = g.billing_member_id
    WHERE g.id = _group_id
      AND a.user_id = _user_id
  );
$function$
;

CREATE OR REPLACE FUNCTION public.is_meta_admin(_user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM public.meta_admin_permissions
    WHERE user_id = _user_id
      AND (expires_at IS NULL OR expires_at > now())
  )
$function$
;

CREATE OR REPLACE FUNCTION public.prevent_overlapping_bookings()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM public.bookings b
    WHERE b.court_id = NEW.court_id
      AND b.booking_date = NEW.booking_date
      -- Zeit-Überlappung prüfen (NICHT: Ende <= Start ODER Start >= Ende)
      AND NOT (NEW.end_time <= b.start_time OR NEW.start_time >= b.end_time)
      AND (TG_OP = 'INSERT' OR b.id <> NEW.id)
  ) THEN
    RAISE EXCEPTION 'Zeitfenster überschneidet sich mit bestehender Buchung';
  END IF;
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$function$
;

grant delete on table "public"."accounts" to "anon";

grant insert on table "public"."accounts" to "anon";

grant references on table "public"."accounts" to "anon";

grant select on table "public"."accounts" to "anon";

grant trigger on table "public"."accounts" to "anon";

grant truncate on table "public"."accounts" to "anon";

grant update on table "public"."accounts" to "anon";

grant delete on table "public"."accounts" to "authenticated";

grant insert on table "public"."accounts" to "authenticated";

grant references on table "public"."accounts" to "authenticated";

grant select on table "public"."accounts" to "authenticated";

grant trigger on table "public"."accounts" to "authenticated";

grant truncate on table "public"."accounts" to "authenticated";

grant update on table "public"."accounts" to "authenticated";

grant delete on table "public"."accounts" to "service_role";

grant insert on table "public"."accounts" to "service_role";

grant references on table "public"."accounts" to "service_role";

grant select on table "public"."accounts" to "service_role";

grant trigger on table "public"."accounts" to "service_role";

grant truncate on table "public"."accounts" to "service_role";

grant update on table "public"."accounts" to "service_role";

grant delete on table "public"."activities" to "anon";

grant insert on table "public"."activities" to "anon";

grant references on table "public"."activities" to "anon";

grant select on table "public"."activities" to "anon";

grant trigger on table "public"."activities" to "anon";

grant truncate on table "public"."activities" to "anon";

grant update on table "public"."activities" to "anon";

grant delete on table "public"."activities" to "authenticated";

grant insert on table "public"."activities" to "authenticated";

grant references on table "public"."activities" to "authenticated";

grant select on table "public"."activities" to "authenticated";

grant trigger on table "public"."activities" to "authenticated";

grant truncate on table "public"."activities" to "authenticated";

grant update on table "public"."activities" to "authenticated";

grant delete on table "public"."activities" to "service_role";

grant insert on table "public"."activities" to "service_role";

grant references on table "public"."activities" to "service_role";

grant select on table "public"."activities" to "service_role";

grant trigger on table "public"."activities" to "service_role";

grant truncate on table "public"."activities" to "service_role";

grant update on table "public"."activities" to "service_role";

grant delete on table "public"."activity_registrations" to "anon";

grant insert on table "public"."activity_registrations" to "anon";

grant references on table "public"."activity_registrations" to "anon";

grant select on table "public"."activity_registrations" to "anon";

grant trigger on table "public"."activity_registrations" to "anon";

grant truncate on table "public"."activity_registrations" to "anon";

grant update on table "public"."activity_registrations" to "anon";

grant delete on table "public"."activity_registrations" to "authenticated";

grant insert on table "public"."activity_registrations" to "authenticated";

grant references on table "public"."activity_registrations" to "authenticated";

grant select on table "public"."activity_registrations" to "authenticated";

grant trigger on table "public"."activity_registrations" to "authenticated";

grant truncate on table "public"."activity_registrations" to "authenticated";

grant update on table "public"."activity_registrations" to "authenticated";

grant delete on table "public"."activity_registrations" to "service_role";

grant insert on table "public"."activity_registrations" to "service_role";

grant references on table "public"."activity_registrations" to "service_role";

grant select on table "public"."activity_registrations" to "service_role";

grant trigger on table "public"."activity_registrations" to "service_role";

grant truncate on table "public"."activity_registrations" to "service_role";

grant update on table "public"."activity_registrations" to "service_role";

grant delete on table "public"."audit_logs" to "anon";

grant insert on table "public"."audit_logs" to "anon";

grant references on table "public"."audit_logs" to "anon";

grant select on table "public"."audit_logs" to "anon";

grant trigger on table "public"."audit_logs" to "anon";

grant truncate on table "public"."audit_logs" to "anon";

grant update on table "public"."audit_logs" to "anon";

grant delete on table "public"."audit_logs" to "authenticated";

grant insert on table "public"."audit_logs" to "authenticated";

grant references on table "public"."audit_logs" to "authenticated";

grant select on table "public"."audit_logs" to "authenticated";

grant trigger on table "public"."audit_logs" to "authenticated";

grant truncate on table "public"."audit_logs" to "authenticated";

grant update on table "public"."audit_logs" to "authenticated";

grant delete on table "public"."audit_logs" to "service_role";

grant insert on table "public"."audit_logs" to "service_role";

grant references on table "public"."audit_logs" to "service_role";

grant select on table "public"."audit_logs" to "service_role";

grant trigger on table "public"."audit_logs" to "service_role";

grant truncate on table "public"."audit_logs" to "service_role";

grant update on table "public"."audit_logs" to "service_role";

grant delete on table "public"."billing_agreements" to "anon";

grant insert on table "public"."billing_agreements" to "anon";

grant references on table "public"."billing_agreements" to "anon";

grant select on table "public"."billing_agreements" to "anon";

grant trigger on table "public"."billing_agreements" to "anon";

grant truncate on table "public"."billing_agreements" to "anon";

grant update on table "public"."billing_agreements" to "anon";

grant delete on table "public"."billing_agreements" to "authenticated";

grant insert on table "public"."billing_agreements" to "authenticated";

grant references on table "public"."billing_agreements" to "authenticated";

grant select on table "public"."billing_agreements" to "authenticated";

grant trigger on table "public"."billing_agreements" to "authenticated";

grant truncate on table "public"."billing_agreements" to "authenticated";

grant update on table "public"."billing_agreements" to "authenticated";

grant delete on table "public"."billing_agreements" to "service_role";

grant insert on table "public"."billing_agreements" to "service_role";

grant references on table "public"."billing_agreements" to "service_role";

grant select on table "public"."billing_agreements" to "service_role";

grant trigger on table "public"."billing_agreements" to "service_role";

grant truncate on table "public"."billing_agreements" to "service_role";

grant update on table "public"."billing_agreements" to "service_role";

grant delete on table "public"."bookings" to "anon";

grant insert on table "public"."bookings" to "anon";

grant references on table "public"."bookings" to "anon";

grant select on table "public"."bookings" to "anon";

grant trigger on table "public"."bookings" to "anon";

grant truncate on table "public"."bookings" to "anon";

grant update on table "public"."bookings" to "anon";

grant delete on table "public"."bookings" to "authenticated";

grant insert on table "public"."bookings" to "authenticated";

grant references on table "public"."bookings" to "authenticated";

grant select on table "public"."bookings" to "authenticated";

grant trigger on table "public"."bookings" to "authenticated";

grant truncate on table "public"."bookings" to "authenticated";

grant update on table "public"."bookings" to "authenticated";

grant delete on table "public"."bookings" to "service_role";

grant insert on table "public"."bookings" to "service_role";

grant references on table "public"."bookings" to "service_role";

grant select on table "public"."bookings" to "service_role";

grant trigger on table "public"."bookings" to "service_role";

grant truncate on table "public"."bookings" to "service_role";

grant update on table "public"."bookings" to "service_role";

grant delete on table "public"."club_memberships" to "anon";

grant insert on table "public"."club_memberships" to "anon";

grant references on table "public"."club_memberships" to "anon";

grant select on table "public"."club_memberships" to "anon";

grant trigger on table "public"."club_memberships" to "anon";

grant truncate on table "public"."club_memberships" to "anon";

grant update on table "public"."club_memberships" to "anon";

grant delete on table "public"."club_memberships" to "authenticated";

grant insert on table "public"."club_memberships" to "authenticated";

grant references on table "public"."club_memberships" to "authenticated";

grant select on table "public"."club_memberships" to "authenticated";

grant trigger on table "public"."club_memberships" to "authenticated";

grant truncate on table "public"."club_memberships" to "authenticated";

grant update on table "public"."club_memberships" to "authenticated";

grant delete on table "public"."club_memberships" to "service_role";

grant insert on table "public"."club_memberships" to "service_role";

grant references on table "public"."club_memberships" to "service_role";

grant select on table "public"."club_memberships" to "service_role";

grant trigger on table "public"."club_memberships" to "service_role";

grant truncate on table "public"."club_memberships" to "service_role";

grant update on table "public"."club_memberships" to "service_role";

grant delete on table "public"."club_subscriptions" to "anon";

grant insert on table "public"."club_subscriptions" to "anon";

grant references on table "public"."club_subscriptions" to "anon";

grant select on table "public"."club_subscriptions" to "anon";

grant trigger on table "public"."club_subscriptions" to "anon";

grant truncate on table "public"."club_subscriptions" to "anon";

grant update on table "public"."club_subscriptions" to "anon";

grant delete on table "public"."club_subscriptions" to "authenticated";

grant insert on table "public"."club_subscriptions" to "authenticated";

grant references on table "public"."club_subscriptions" to "authenticated";

grant select on table "public"."club_subscriptions" to "authenticated";

grant trigger on table "public"."club_subscriptions" to "authenticated";

grant truncate on table "public"."club_subscriptions" to "authenticated";

grant update on table "public"."club_subscriptions" to "authenticated";

grant delete on table "public"."club_subscriptions" to "service_role";

grant insert on table "public"."club_subscriptions" to "service_role";

grant references on table "public"."club_subscriptions" to "service_role";

grant select on table "public"."club_subscriptions" to "service_role";

grant trigger on table "public"."club_subscriptions" to "service_role";

grant truncate on table "public"."club_subscriptions" to "service_role";

grant update on table "public"."club_subscriptions" to "service_role";

grant delete on table "public"."clubs" to "anon";

grant insert on table "public"."clubs" to "anon";

grant references on table "public"."clubs" to "anon";

grant select on table "public"."clubs" to "anon";

grant trigger on table "public"."clubs" to "anon";

grant truncate on table "public"."clubs" to "anon";

grant update on table "public"."clubs" to "anon";

grant delete on table "public"."clubs" to "authenticated";

grant insert on table "public"."clubs" to "authenticated";

grant references on table "public"."clubs" to "authenticated";

grant select on table "public"."clubs" to "authenticated";

grant trigger on table "public"."clubs" to "authenticated";

grant truncate on table "public"."clubs" to "authenticated";

grant update on table "public"."clubs" to "authenticated";

grant delete on table "public"."clubs" to "service_role";

grant insert on table "public"."clubs" to "service_role";

grant references on table "public"."clubs" to "service_role";

grant select on table "public"."clubs" to "service_role";

grant trigger on table "public"."clubs" to "service_role";

grant truncate on table "public"."clubs" to "service_role";

grant update on table "public"."clubs" to "service_role";

grant delete on table "public"."court_availability" to "anon";

grant insert on table "public"."court_availability" to "anon";

grant references on table "public"."court_availability" to "anon";

grant select on table "public"."court_availability" to "anon";

grant trigger on table "public"."court_availability" to "anon";

grant truncate on table "public"."court_availability" to "anon";

grant update on table "public"."court_availability" to "anon";

grant delete on table "public"."court_availability" to "authenticated";

grant insert on table "public"."court_availability" to "authenticated";

grant references on table "public"."court_availability" to "authenticated";

grant select on table "public"."court_availability" to "authenticated";

grant trigger on table "public"."court_availability" to "authenticated";

grant truncate on table "public"."court_availability" to "authenticated";

grant update on table "public"."court_availability" to "authenticated";

grant delete on table "public"."court_availability" to "service_role";

grant insert on table "public"."court_availability" to "service_role";

grant references on table "public"."court_availability" to "service_role";

grant select on table "public"."court_availability" to "service_role";

grant trigger on table "public"."court_availability" to "service_role";

grant truncate on table "public"."court_availability" to "service_role";

grant update on table "public"."court_availability" to "service_role";

grant delete on table "public"."court_blocks" to "anon";

grant insert on table "public"."court_blocks" to "anon";

grant references on table "public"."court_blocks" to "anon";

grant select on table "public"."court_blocks" to "anon";

grant trigger on table "public"."court_blocks" to "anon";

grant truncate on table "public"."court_blocks" to "anon";

grant update on table "public"."court_blocks" to "anon";

grant delete on table "public"."court_blocks" to "authenticated";

grant insert on table "public"."court_blocks" to "authenticated";

grant references on table "public"."court_blocks" to "authenticated";

grant select on table "public"."court_blocks" to "authenticated";

grant trigger on table "public"."court_blocks" to "authenticated";

grant truncate on table "public"."court_blocks" to "authenticated";

grant update on table "public"."court_blocks" to "authenticated";

grant delete on table "public"."court_blocks" to "service_role";

grant insert on table "public"."court_blocks" to "service_role";

grant references on table "public"."court_blocks" to "service_role";

grant select on table "public"."court_blocks" to "service_role";

grant trigger on table "public"."court_blocks" to "service_role";

grant truncate on table "public"."court_blocks" to "service_role";

grant update on table "public"."court_blocks" to "service_role";

grant delete on table "public"."courts" to "anon";

grant insert on table "public"."courts" to "anon";

grant references on table "public"."courts" to "anon";

grant select on table "public"."courts" to "anon";

grant trigger on table "public"."courts" to "anon";

grant truncate on table "public"."courts" to "anon";

grant update on table "public"."courts" to "anon";

grant delete on table "public"."courts" to "authenticated";

grant insert on table "public"."courts" to "authenticated";

grant references on table "public"."courts" to "authenticated";

grant select on table "public"."courts" to "authenticated";

grant trigger on table "public"."courts" to "authenticated";

grant truncate on table "public"."courts" to "authenticated";

grant update on table "public"."courts" to "authenticated";

grant delete on table "public"."courts" to "service_role";

grant insert on table "public"."courts" to "service_role";

grant references on table "public"."courts" to "service_role";

grant select on table "public"."courts" to "service_role";

grant trigger on table "public"."courts" to "service_role";

grant truncate on table "public"."courts" to "service_role";

grant update on table "public"."courts" to "service_role";

grant delete on table "public"."domains" to "anon";

grant insert on table "public"."domains" to "anon";

grant references on table "public"."domains" to "anon";

grant select on table "public"."domains" to "anon";

grant trigger on table "public"."domains" to "anon";

grant truncate on table "public"."domains" to "anon";

grant update on table "public"."domains" to "anon";

grant delete on table "public"."domains" to "authenticated";

grant insert on table "public"."domains" to "authenticated";

grant references on table "public"."domains" to "authenticated";

grant select on table "public"."domains" to "authenticated";

grant trigger on table "public"."domains" to "authenticated";

grant truncate on table "public"."domains" to "authenticated";

grant update on table "public"."domains" to "authenticated";

grant delete on table "public"."domains" to "service_role";

grant insert on table "public"."domains" to "service_role";

grant references on table "public"."domains" to "service_role";

grant select on table "public"."domains" to "service_role";

grant trigger on table "public"."domains" to "service_role";

grant truncate on table "public"."domains" to "service_role";

grant update on table "public"."domains" to "service_role";

grant delete on table "public"."email_templates" to "anon";

grant insert on table "public"."email_templates" to "anon";

grant references on table "public"."email_templates" to "anon";

grant select on table "public"."email_templates" to "anon";

grant trigger on table "public"."email_templates" to "anon";

grant truncate on table "public"."email_templates" to "anon";

grant update on table "public"."email_templates" to "anon";

grant delete on table "public"."email_templates" to "authenticated";

grant insert on table "public"."email_templates" to "authenticated";

grant references on table "public"."email_templates" to "authenticated";

grant select on table "public"."email_templates" to "authenticated";

grant trigger on table "public"."email_templates" to "authenticated";

grant truncate on table "public"."email_templates" to "authenticated";

grant update on table "public"."email_templates" to "authenticated";

grant delete on table "public"."email_templates" to "service_role";

grant insert on table "public"."email_templates" to "service_role";

grant references on table "public"."email_templates" to "service_role";

grant select on table "public"."email_templates" to "service_role";

grant trigger on table "public"."email_templates" to "service_role";

grant truncate on table "public"."email_templates" to "service_role";

grant update on table "public"."email_templates" to "service_role";

grant delete on table "public"."fee_assignments" to "anon";

grant insert on table "public"."fee_assignments" to "anon";

grant references on table "public"."fee_assignments" to "anon";

grant select on table "public"."fee_assignments" to "anon";

grant trigger on table "public"."fee_assignments" to "anon";

grant truncate on table "public"."fee_assignments" to "anon";

grant update on table "public"."fee_assignments" to "anon";

grant delete on table "public"."fee_assignments" to "authenticated";

grant insert on table "public"."fee_assignments" to "authenticated";

grant references on table "public"."fee_assignments" to "authenticated";

grant select on table "public"."fee_assignments" to "authenticated";

grant trigger on table "public"."fee_assignments" to "authenticated";

grant truncate on table "public"."fee_assignments" to "authenticated";

grant update on table "public"."fee_assignments" to "authenticated";

grant delete on table "public"."fee_assignments" to "service_role";

grant insert on table "public"."fee_assignments" to "service_role";

grant references on table "public"."fee_assignments" to "service_role";

grant select on table "public"."fee_assignments" to "service_role";

grant trigger on table "public"."fee_assignments" to "service_role";

grant truncate on table "public"."fee_assignments" to "service_role";

grant update on table "public"."fee_assignments" to "service_role";

grant delete on table "public"."fee_categories" to "anon";

grant insert on table "public"."fee_categories" to "anon";

grant references on table "public"."fee_categories" to "anon";

grant select on table "public"."fee_categories" to "anon";

grant trigger on table "public"."fee_categories" to "anon";

grant truncate on table "public"."fee_categories" to "anon";

grant update on table "public"."fee_categories" to "anon";

grant delete on table "public"."fee_categories" to "authenticated";

grant insert on table "public"."fee_categories" to "authenticated";

grant references on table "public"."fee_categories" to "authenticated";

grant select on table "public"."fee_categories" to "authenticated";

grant trigger on table "public"."fee_categories" to "authenticated";

grant truncate on table "public"."fee_categories" to "authenticated";

grant update on table "public"."fee_categories" to "authenticated";

grant delete on table "public"."fee_categories" to "service_role";

grant insert on table "public"."fee_categories" to "service_role";

grant references on table "public"."fee_categories" to "service_role";

grant select on table "public"."fee_categories" to "service_role";

grant trigger on table "public"."fee_categories" to "service_role";

grant truncate on table "public"."fee_categories" to "service_role";

grant update on table "public"."fee_categories" to "service_role";

grant delete on table "public"."fee_type_membership_types" to "anon";

grant insert on table "public"."fee_type_membership_types" to "anon";

grant references on table "public"."fee_type_membership_types" to "anon";

grant select on table "public"."fee_type_membership_types" to "anon";

grant trigger on table "public"."fee_type_membership_types" to "anon";

grant truncate on table "public"."fee_type_membership_types" to "anon";

grant update on table "public"."fee_type_membership_types" to "anon";

grant delete on table "public"."fee_type_membership_types" to "authenticated";

grant insert on table "public"."fee_type_membership_types" to "authenticated";

grant references on table "public"."fee_type_membership_types" to "authenticated";

grant select on table "public"."fee_type_membership_types" to "authenticated";

grant trigger on table "public"."fee_type_membership_types" to "authenticated";

grant truncate on table "public"."fee_type_membership_types" to "authenticated";

grant update on table "public"."fee_type_membership_types" to "authenticated";

grant delete on table "public"."fee_type_membership_types" to "service_role";

grant insert on table "public"."fee_type_membership_types" to "service_role";

grant references on table "public"."fee_type_membership_types" to "service_role";

grant select on table "public"."fee_type_membership_types" to "service_role";

grant trigger on table "public"."fee_type_membership_types" to "service_role";

grant truncate on table "public"."fee_type_membership_types" to "service_role";

grant update on table "public"."fee_type_membership_types" to "service_role";

grant delete on table "public"."fee_types" to "anon";

grant insert on table "public"."fee_types" to "anon";

grant references on table "public"."fee_types" to "anon";

grant select on table "public"."fee_types" to "anon";

grant trigger on table "public"."fee_types" to "anon";

grant truncate on table "public"."fee_types" to "anon";

grant update on table "public"."fee_types" to "anon";

grant delete on table "public"."fee_types" to "authenticated";

grant insert on table "public"."fee_types" to "authenticated";

grant references on table "public"."fee_types" to "authenticated";

grant select on table "public"."fee_types" to "authenticated";

grant trigger on table "public"."fee_types" to "authenticated";

grant truncate on table "public"."fee_types" to "authenticated";

grant update on table "public"."fee_types" to "authenticated";

grant delete on table "public"."fee_types" to "service_role";

grant insert on table "public"."fee_types" to "service_role";

grant references on table "public"."fee_types" to "service_role";

grant select on table "public"."fee_types" to "service_role";

grant trigger on table "public"."fee_types" to "service_role";

grant truncate on table "public"."fee_types" to "service_role";

grant update on table "public"."fee_types" to "service_role";

grant delete on table "public"."group_members" to "anon";

grant insert on table "public"."group_members" to "anon";

grant references on table "public"."group_members" to "anon";

grant select on table "public"."group_members" to "anon";

grant trigger on table "public"."group_members" to "anon";

grant truncate on table "public"."group_members" to "anon";

grant update on table "public"."group_members" to "anon";

grant delete on table "public"."group_members" to "authenticated";

grant insert on table "public"."group_members" to "authenticated";

grant references on table "public"."group_members" to "authenticated";

grant select on table "public"."group_members" to "authenticated";

grant trigger on table "public"."group_members" to "authenticated";

grant truncate on table "public"."group_members" to "authenticated";

grant update on table "public"."group_members" to "authenticated";

grant delete on table "public"."group_members" to "service_role";

grant insert on table "public"."group_members" to "service_role";

grant references on table "public"."group_members" to "service_role";

grant select on table "public"."group_members" to "service_role";

grant trigger on table "public"."group_members" to "service_role";

grant truncate on table "public"."group_members" to "service_role";

grant update on table "public"."group_members" to "service_role";

grant delete on table "public"."groups" to "anon";

grant insert on table "public"."groups" to "anon";

grant references on table "public"."groups" to "anon";

grant select on table "public"."groups" to "anon";

grant trigger on table "public"."groups" to "anon";

grant truncate on table "public"."groups" to "anon";

grant update on table "public"."groups" to "anon";

grant delete on table "public"."groups" to "authenticated";

grant insert on table "public"."groups" to "authenticated";

grant references on table "public"."groups" to "authenticated";

grant select on table "public"."groups" to "authenticated";

grant trigger on table "public"."groups" to "authenticated";

grant truncate on table "public"."groups" to "authenticated";

grant update on table "public"."groups" to "authenticated";

grant delete on table "public"."groups" to "service_role";

grant insert on table "public"."groups" to "service_role";

grant references on table "public"."groups" to "service_role";

grant select on table "public"."groups" to "service_role";

grant trigger on table "public"."groups" to "service_role";

grant truncate on table "public"."groups" to "service_role";

grant update on table "public"."groups" to "service_role";

grant delete on table "public"."guardianships" to "anon";

grant insert on table "public"."guardianships" to "anon";

grant references on table "public"."guardianships" to "anon";

grant select on table "public"."guardianships" to "anon";

grant trigger on table "public"."guardianships" to "anon";

grant truncate on table "public"."guardianships" to "anon";

grant update on table "public"."guardianships" to "anon";

grant delete on table "public"."guardianships" to "authenticated";

grant insert on table "public"."guardianships" to "authenticated";

grant references on table "public"."guardianships" to "authenticated";

grant select on table "public"."guardianships" to "authenticated";

grant trigger on table "public"."guardianships" to "authenticated";

grant truncate on table "public"."guardianships" to "authenticated";

grant update on table "public"."guardianships" to "authenticated";

grant delete on table "public"."guardianships" to "service_role";

grant insert on table "public"."guardianships" to "service_role";

grant references on table "public"."guardianships" to "service_role";

grant select on table "public"."guardianships" to "service_role";

grant trigger on table "public"."guardianships" to "service_role";

grant truncate on table "public"."guardianships" to "service_role";

grant update on table "public"."guardianships" to "service_role";

grant delete on table "public"."household_members" to "anon";

grant insert on table "public"."household_members" to "anon";

grant references on table "public"."household_members" to "anon";

grant select on table "public"."household_members" to "anon";

grant trigger on table "public"."household_members" to "anon";

grant truncate on table "public"."household_members" to "anon";

grant update on table "public"."household_members" to "anon";

grant delete on table "public"."household_members" to "authenticated";

grant insert on table "public"."household_members" to "authenticated";

grant references on table "public"."household_members" to "authenticated";

grant select on table "public"."household_members" to "authenticated";

grant trigger on table "public"."household_members" to "authenticated";

grant truncate on table "public"."household_members" to "authenticated";

grant update on table "public"."household_members" to "authenticated";

grant delete on table "public"."household_members" to "service_role";

grant insert on table "public"."household_members" to "service_role";

grant references on table "public"."household_members" to "service_role";

grant select on table "public"."household_members" to "service_role";

grant trigger on table "public"."household_members" to "service_role";

grant truncate on table "public"."household_members" to "service_role";

grant update on table "public"."household_members" to "service_role";

grant delete on table "public"."households" to "anon";

grant insert on table "public"."households" to "anon";

grant references on table "public"."households" to "anon";

grant select on table "public"."households" to "anon";

grant trigger on table "public"."households" to "anon";

grant truncate on table "public"."households" to "anon";

grant update on table "public"."households" to "anon";

grant delete on table "public"."households" to "authenticated";

grant insert on table "public"."households" to "authenticated";

grant references on table "public"."households" to "authenticated";

grant select on table "public"."households" to "authenticated";

grant trigger on table "public"."households" to "authenticated";

grant truncate on table "public"."households" to "authenticated";

grant update on table "public"."households" to "authenticated";

grant delete on table "public"."households" to "service_role";

grant insert on table "public"."households" to "service_role";

grant references on table "public"."households" to "service_role";

grant select on table "public"."households" to "service_role";

grant trigger on table "public"."households" to "service_role";

grant truncate on table "public"."households" to "service_role";

grant update on table "public"."households" to "service_role";

grant delete on table "public"."invites" to "anon";

grant insert on table "public"."invites" to "anon";

grant references on table "public"."invites" to "anon";

grant select on table "public"."invites" to "anon";

grant trigger on table "public"."invites" to "anon";

grant truncate on table "public"."invites" to "anon";

grant update on table "public"."invites" to "anon";

grant delete on table "public"."invites" to "authenticated";

grant insert on table "public"."invites" to "authenticated";

grant references on table "public"."invites" to "authenticated";

grant select on table "public"."invites" to "authenticated";

grant trigger on table "public"."invites" to "authenticated";

grant truncate on table "public"."invites" to "authenticated";

grant update on table "public"."invites" to "authenticated";

grant delete on table "public"."invites" to "service_role";

grant insert on table "public"."invites" to "service_role";

grant references on table "public"."invites" to "service_role";

grant select on table "public"."invites" to "service_role";

grant trigger on table "public"."invites" to "service_role";

grant truncate on table "public"."invites" to "service_role";

grant update on table "public"."invites" to "service_role";

grant delete on table "public"."login_identities" to "anon";

grant insert on table "public"."login_identities" to "anon";

grant references on table "public"."login_identities" to "anon";

grant select on table "public"."login_identities" to "anon";

grant trigger on table "public"."login_identities" to "anon";

grant truncate on table "public"."login_identities" to "anon";

grant update on table "public"."login_identities" to "anon";

grant delete on table "public"."login_identities" to "authenticated";

grant insert on table "public"."login_identities" to "authenticated";

grant references on table "public"."login_identities" to "authenticated";

grant select on table "public"."login_identities" to "authenticated";

grant trigger on table "public"."login_identities" to "authenticated";

grant truncate on table "public"."login_identities" to "authenticated";

grant update on table "public"."login_identities" to "authenticated";

grant delete on table "public"."login_identities" to "service_role";

grant insert on table "public"."login_identities" to "service_role";

grant references on table "public"."login_identities" to "service_role";

grant select on table "public"."login_identities" to "service_role";

grant trigger on table "public"."login_identities" to "service_role";

grant truncate on table "public"."login_identities" to "service_role";

grant update on table "public"."login_identities" to "service_role";

grant delete on table "public"."members" to "anon";

grant insert on table "public"."members" to "anon";

grant references on table "public"."members" to "anon";

grant select on table "public"."members" to "anon";

grant trigger on table "public"."members" to "anon";

grant truncate on table "public"."members" to "anon";

grant update on table "public"."members" to "anon";

grant delete on table "public"."members" to "authenticated";

grant insert on table "public"."members" to "authenticated";

grant references on table "public"."members" to "authenticated";

grant select on table "public"."members" to "authenticated";

grant trigger on table "public"."members" to "authenticated";

grant truncate on table "public"."members" to "authenticated";

grant update on table "public"."members" to "authenticated";

grant delete on table "public"."members" to "service_role";

grant insert on table "public"."members" to "service_role";

grant references on table "public"."members" to "service_role";

grant select on table "public"."members" to "service_role";

grant trigger on table "public"."members" to "service_role";

grant truncate on table "public"."members" to "service_role";

grant update on table "public"."members" to "service_role";

grant delete on table "public"."membership_cancellations" to "anon";

grant insert on table "public"."membership_cancellations" to "anon";

grant references on table "public"."membership_cancellations" to "anon";

grant select on table "public"."membership_cancellations" to "anon";

grant trigger on table "public"."membership_cancellations" to "anon";

grant truncate on table "public"."membership_cancellations" to "anon";

grant update on table "public"."membership_cancellations" to "anon";

grant delete on table "public"."membership_cancellations" to "authenticated";

grant insert on table "public"."membership_cancellations" to "authenticated";

grant references on table "public"."membership_cancellations" to "authenticated";

grant select on table "public"."membership_cancellations" to "authenticated";

grant trigger on table "public"."membership_cancellations" to "authenticated";

grant truncate on table "public"."membership_cancellations" to "authenticated";

grant update on table "public"."membership_cancellations" to "authenticated";

grant delete on table "public"."membership_cancellations" to "service_role";

grant insert on table "public"."membership_cancellations" to "service_role";

grant references on table "public"."membership_cancellations" to "service_role";

grant select on table "public"."membership_cancellations" to "service_role";

grant trigger on table "public"."membership_cancellations" to "service_role";

grant truncate on table "public"."membership_cancellations" to "service_role";

grant update on table "public"."membership_cancellations" to "service_role";

grant delete on table "public"."membership_types" to "anon";

grant insert on table "public"."membership_types" to "anon";

grant references on table "public"."membership_types" to "anon";

grant select on table "public"."membership_types" to "anon";

grant trigger on table "public"."membership_types" to "anon";

grant truncate on table "public"."membership_types" to "anon";

grant update on table "public"."membership_types" to "anon";

grant delete on table "public"."membership_types" to "authenticated";

grant insert on table "public"."membership_types" to "authenticated";

grant references on table "public"."membership_types" to "authenticated";

grant select on table "public"."membership_types" to "authenticated";

grant trigger on table "public"."membership_types" to "authenticated";

grant truncate on table "public"."membership_types" to "authenticated";

grant update on table "public"."membership_types" to "authenticated";

grant delete on table "public"."membership_types" to "service_role";

grant insert on table "public"."membership_types" to "service_role";

grant references on table "public"."membership_types" to "service_role";

grant select on table "public"."membership_types" to "service_role";

grant trigger on table "public"."membership_types" to "service_role";

grant truncate on table "public"."membership_types" to "service_role";

grant update on table "public"."membership_types" to "service_role";

grant delete on table "public"."meta_admin_permissions" to "anon";

grant insert on table "public"."meta_admin_permissions" to "anon";

grant references on table "public"."meta_admin_permissions" to "anon";

grant select on table "public"."meta_admin_permissions" to "anon";

grant trigger on table "public"."meta_admin_permissions" to "anon";

grant truncate on table "public"."meta_admin_permissions" to "anon";

grant update on table "public"."meta_admin_permissions" to "anon";

grant delete on table "public"."meta_admin_permissions" to "authenticated";

grant insert on table "public"."meta_admin_permissions" to "authenticated";

grant references on table "public"."meta_admin_permissions" to "authenticated";

grant select on table "public"."meta_admin_permissions" to "authenticated";

grant trigger on table "public"."meta_admin_permissions" to "authenticated";

grant truncate on table "public"."meta_admin_permissions" to "authenticated";

grant update on table "public"."meta_admin_permissions" to "authenticated";

grant delete on table "public"."meta_admin_permissions" to "service_role";

grant insert on table "public"."meta_admin_permissions" to "service_role";

grant references on table "public"."meta_admin_permissions" to "service_role";

grant select on table "public"."meta_admin_permissions" to "service_role";

grant trigger on table "public"."meta_admin_permissions" to "service_role";

grant truncate on table "public"."meta_admin_permissions" to "service_role";

grant update on table "public"."meta_admin_permissions" to "service_role";

grant delete on table "public"."plans" to "anon";

grant insert on table "public"."plans" to "anon";

grant references on table "public"."plans" to "anon";

grant select on table "public"."plans" to "anon";

grant trigger on table "public"."plans" to "anon";

grant truncate on table "public"."plans" to "anon";

grant update on table "public"."plans" to "anon";

grant delete on table "public"."plans" to "authenticated";

grant insert on table "public"."plans" to "authenticated";

grant references on table "public"."plans" to "authenticated";

grant select on table "public"."plans" to "authenticated";

grant trigger on table "public"."plans" to "authenticated";

grant truncate on table "public"."plans" to "authenticated";

grant update on table "public"."plans" to "authenticated";

grant delete on table "public"."plans" to "service_role";

grant insert on table "public"."plans" to "service_role";

grant references on table "public"."plans" to "service_role";

grant select on table "public"."plans" to "service_role";

grant trigger on table "public"."plans" to "service_role";

grant truncate on table "public"."plans" to "service_role";

grant update on table "public"."plans" to "service_role";

grant delete on table "public"."profiles" to "anon";

grant insert on table "public"."profiles" to "anon";

grant references on table "public"."profiles" to "anon";

grant select on table "public"."profiles" to "anon";

grant trigger on table "public"."profiles" to "anon";

grant truncate on table "public"."profiles" to "anon";

grant update on table "public"."profiles" to "anon";

grant delete on table "public"."profiles" to "authenticated";

grant insert on table "public"."profiles" to "authenticated";

grant references on table "public"."profiles" to "authenticated";

grant select on table "public"."profiles" to "authenticated";

grant trigger on table "public"."profiles" to "authenticated";

grant truncate on table "public"."profiles" to "authenticated";

grant update on table "public"."profiles" to "authenticated";

grant delete on table "public"."profiles" to "service_role";

grant insert on table "public"."profiles" to "service_role";

grant references on table "public"."profiles" to "service_role";

grant select on table "public"."profiles" to "service_role";

grant trigger on table "public"."profiles" to "service_role";

grant truncate on table "public"."profiles" to "service_role";

grant update on table "public"."profiles" to "service_role";

grant delete on table "public"."role_permissions" to "anon";

grant insert on table "public"."role_permissions" to "anon";

grant references on table "public"."role_permissions" to "anon";

grant select on table "public"."role_permissions" to "anon";

grant trigger on table "public"."role_permissions" to "anon";

grant truncate on table "public"."role_permissions" to "anon";

grant update on table "public"."role_permissions" to "anon";

grant delete on table "public"."role_permissions" to "authenticated";

grant insert on table "public"."role_permissions" to "authenticated";

grant references on table "public"."role_permissions" to "authenticated";

grant select on table "public"."role_permissions" to "authenticated";

grant trigger on table "public"."role_permissions" to "authenticated";

grant truncate on table "public"."role_permissions" to "authenticated";

grant update on table "public"."role_permissions" to "authenticated";

grant delete on table "public"."role_permissions" to "service_role";

grant insert on table "public"."role_permissions" to "service_role";

grant references on table "public"."role_permissions" to "service_role";

grant select on table "public"."role_permissions" to "service_role";

grant trigger on table "public"."role_permissions" to "service_role";

grant truncate on table "public"."role_permissions" to "service_role";

grant update on table "public"."role_permissions" to "service_role";

grant delete on table "public"."support_tickets" to "anon";

grant insert on table "public"."support_tickets" to "anon";

grant references on table "public"."support_tickets" to "anon";

grant select on table "public"."support_tickets" to "anon";

grant trigger on table "public"."support_tickets" to "anon";

grant truncate on table "public"."support_tickets" to "anon";

grant update on table "public"."support_tickets" to "anon";

grant delete on table "public"."support_tickets" to "authenticated";

grant insert on table "public"."support_tickets" to "authenticated";

grant references on table "public"."support_tickets" to "authenticated";

grant select on table "public"."support_tickets" to "authenticated";

grant trigger on table "public"."support_tickets" to "authenticated";

grant truncate on table "public"."support_tickets" to "authenticated";

grant update on table "public"."support_tickets" to "authenticated";

grant delete on table "public"."support_tickets" to "service_role";

grant insert on table "public"."support_tickets" to "service_role";

grant references on table "public"."support_tickets" to "service_role";

grant select on table "public"."support_tickets" to "service_role";

grant trigger on table "public"."support_tickets" to "service_role";

grant truncate on table "public"."support_tickets" to "service_role";

grant update on table "public"."support_tickets" to "service_role";

grant delete on table "public"."system_metrics" to "anon";

grant insert on table "public"."system_metrics" to "anon";

grant references on table "public"."system_metrics" to "anon";

grant select on table "public"."system_metrics" to "anon";

grant trigger on table "public"."system_metrics" to "anon";

grant truncate on table "public"."system_metrics" to "anon";

grant update on table "public"."system_metrics" to "anon";

grant delete on table "public"."system_metrics" to "authenticated";

grant insert on table "public"."system_metrics" to "authenticated";

grant references on table "public"."system_metrics" to "authenticated";

grant select on table "public"."system_metrics" to "authenticated";

grant trigger on table "public"."system_metrics" to "authenticated";

grant truncate on table "public"."system_metrics" to "authenticated";

grant update on table "public"."system_metrics" to "authenticated";

grant delete on table "public"."system_metrics" to "service_role";

grant insert on table "public"."system_metrics" to "service_role";

grant references on table "public"."system_metrics" to "service_role";

grant select on table "public"."system_metrics" to "service_role";

grant trigger on table "public"."system_metrics" to "service_role";

grant truncate on table "public"."system_metrics" to "service_role";

grant update on table "public"."system_metrics" to "service_role";

grant delete on table "public"."system_settings" to "anon";

grant insert on table "public"."system_settings" to "anon";

grant references on table "public"."system_settings" to "anon";

grant select on table "public"."system_settings" to "anon";

grant trigger on table "public"."system_settings" to "anon";

grant truncate on table "public"."system_settings" to "anon";

grant update on table "public"."system_settings" to "anon";

grant delete on table "public"."system_settings" to "authenticated";

grant insert on table "public"."system_settings" to "authenticated";

grant references on table "public"."system_settings" to "authenticated";

grant select on table "public"."system_settings" to "authenticated";

grant trigger on table "public"."system_settings" to "authenticated";

grant truncate on table "public"."system_settings" to "authenticated";

grant update on table "public"."system_settings" to "authenticated";

grant delete on table "public"."system_settings" to "service_role";

grant insert on table "public"."system_settings" to "service_role";

grant references on table "public"."system_settings" to "service_role";

grant select on table "public"."system_settings" to "service_role";

grant trigger on table "public"."system_settings" to "service_role";

grant truncate on table "public"."system_settings" to "service_role";

grant update on table "public"."system_settings" to "service_role";

grant delete on table "public"."user_roles" to "anon";

grant insert on table "public"."user_roles" to "anon";

grant references on table "public"."user_roles" to "anon";

grant select on table "public"."user_roles" to "anon";

grant trigger on table "public"."user_roles" to "anon";

grant truncate on table "public"."user_roles" to "anon";

grant update on table "public"."user_roles" to "anon";

grant delete on table "public"."user_roles" to "authenticated";

grant insert on table "public"."user_roles" to "authenticated";

grant references on table "public"."user_roles" to "authenticated";

grant select on table "public"."user_roles" to "authenticated";

grant trigger on table "public"."user_roles" to "authenticated";

grant truncate on table "public"."user_roles" to "authenticated";

grant update on table "public"."user_roles" to "authenticated";

grant delete on table "public"."user_roles" to "service_role";

grant insert on table "public"."user_roles" to "service_role";

grant references on table "public"."user_roles" to "service_role";

grant select on table "public"."user_roles" to "service_role";

grant trigger on table "public"."user_roles" to "service_role";

grant truncate on table "public"."user_roles" to "service_role";

grant update on table "public"."user_roles" to "service_role";

grant delete on table "public"."work_log_assignments" to "anon";

grant insert on table "public"."work_log_assignments" to "anon";

grant references on table "public"."work_log_assignments" to "anon";

grant select on table "public"."work_log_assignments" to "anon";

grant trigger on table "public"."work_log_assignments" to "anon";

grant truncate on table "public"."work_log_assignments" to "anon";

grant update on table "public"."work_log_assignments" to "anon";

grant delete on table "public"."work_log_assignments" to "authenticated";

grant insert on table "public"."work_log_assignments" to "authenticated";

grant references on table "public"."work_log_assignments" to "authenticated";

grant select on table "public"."work_log_assignments" to "authenticated";

grant trigger on table "public"."work_log_assignments" to "authenticated";

grant truncate on table "public"."work_log_assignments" to "authenticated";

grant update on table "public"."work_log_assignments" to "authenticated";

grant delete on table "public"."work_log_assignments" to "service_role";

grant insert on table "public"."work_log_assignments" to "service_role";

grant references on table "public"."work_log_assignments" to "service_role";

grant select on table "public"."work_log_assignments" to "service_role";

grant trigger on table "public"."work_log_assignments" to "service_role";

grant truncate on table "public"."work_log_assignments" to "service_role";

grant update on table "public"."work_log_assignments" to "service_role";

grant delete on table "public"."work_logs" to "anon";

grant insert on table "public"."work_logs" to "anon";

grant references on table "public"."work_logs" to "anon";

grant select on table "public"."work_logs" to "anon";

grant trigger on table "public"."work_logs" to "anon";

grant truncate on table "public"."work_logs" to "anon";

grant update on table "public"."work_logs" to "anon";

grant delete on table "public"."work_logs" to "authenticated";

grant insert on table "public"."work_logs" to "authenticated";

grant references on table "public"."work_logs" to "authenticated";

grant select on table "public"."work_logs" to "authenticated";

grant trigger on table "public"."work_logs" to "authenticated";

grant truncate on table "public"."work_logs" to "authenticated";

grant update on table "public"."work_logs" to "authenticated";

grant delete on table "public"."work_logs" to "service_role";

grant insert on table "public"."work_logs" to "service_role";

grant references on table "public"."work_logs" to "service_role";

grant select on table "public"."work_logs" to "service_role";

grant trigger on table "public"."work_logs" to "service_role";

grant truncate on table "public"."work_logs" to "service_role";

grant update on table "public"."work_logs" to "service_role";

create policy "Account owners select"
on "public"."accounts"
as permissive
for select
to authenticated
using ((user_id = auth.uid()));


create policy "Account owners update"
on "public"."accounts"
as permissive
for update
to authenticated
using ((user_id = auth.uid()))
with check ((user_id = auth.uid()));


create policy "Admins manage accounts"
on "public"."accounts"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Allow all operations on accounts"
on "public"."accounts"
as permissive
for all
to public
using (true)
with check (true);


create policy "Guardians select dependents"
on "public"."accounts"
as permissive
for select
to authenticated
using ((id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))));


create policy "Guardians update dependents"
on "public"."accounts"
as permissive
for update
to authenticated
using ((id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))))
with check ((id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))));


create policy "Allow all operations on activities"
on "public"."activities"
as permissive
for all
to public
using (true)
with check (true);


create policy "Allow all operations on activity_registrations"
on "public"."activity_registrations"
as permissive
for all
to public
using (true)
with check (true);


create policy "Meta admins can view audit logs"
on "public"."audit_logs"
as permissive
for select
to authenticated
using (is_meta_admin(auth.uid()));


create policy "System can insert audit logs"
on "public"."audit_logs"
as permissive
for insert
to authenticated
with check (true);


create policy "Admins manage billing_agreements"
on "public"."billing_agreements"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Users view their billing agreements"
on "public"."billing_agreements"
as permissive
for select
to authenticated
using (((payer_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))) OR (beneficiary_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id)))));


create policy "Admins can delete any booking"
on "public"."bookings"
as permissive
for delete
to authenticated
using (has_role(auth.uid(), 'admin'::app_role));


create policy "Admins can update any booking"
on "public"."bookings"
as permissive
for update
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Admins manage bookings"
on "public"."bookings"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Authenticated users can view all bookings"
on "public"."bookings"
as permissive
for select
to authenticated
using (true);


create policy "Users can create their own bookings"
on "public"."bookings"
as permissive
for insert
to authenticated
with check ((created_by = auth.uid()));


create policy "Users can delete their own bookings"
on "public"."bookings"
as permissive
for delete
to authenticated
using ((created_by = auth.uid()));


create policy "Users create bookings within rights"
on "public"."bookings"
as permissive
for insert
to authenticated
with check (((created_by = auth.uid()) AND (acting_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))) AND (booked_for_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id)))));


create policy "Users delete own bookings"
on "public"."bookings"
as permissive
for delete
to authenticated
using ((created_by = auth.uid()));


create policy "Users view own and represented bookings"
on "public"."bookings"
as permissive
for select
to authenticated
using (((created_by = auth.uid()) OR (booked_for_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id)))));


create policy "Club admins can manage their club memberships"
on "public"."club_memberships"
as permissive
for all
to public
using ((club_id IN ( SELECT cm.club_id
   FROM club_memberships cm
  WHERE ((cm.user_id = auth.uid()) AND (cm.role = 'admin'::text) AND (cm.is_active = true)))))
with check ((club_id IN ( SELECT cm.club_id
   FROM club_memberships cm
  WHERE ((cm.user_id = auth.uid()) AND (cm.role = 'admin'::text) AND (cm.is_active = true)))));


create policy "Super admins can manage all memberships"
on "public"."club_memberships"
as permissive
for all
to public
using (has_role(auth.uid(), 'super_admin'::app_role))
with check (has_role(auth.uid(), 'super_admin'::app_role));


create policy "Users can view their own memberships"
on "public"."club_memberships"
as permissive
for select
to public
using ((user_id = auth.uid()));


create policy "Billing admins can manage subscriptions"
on "public"."club_subscriptions"
as permissive
for all
to authenticated
using ((has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role) OR has_meta_admin_role(auth.uid(), 'BILLING_ADMIN'::meta_admin_role)))
with check ((has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role) OR has_meta_admin_role(auth.uid(), 'BILLING_ADMIN'::meta_admin_role)));


create policy "Meta admins can view subscriptions"
on "public"."club_subscriptions"
as permissive
for select
to authenticated
using (is_meta_admin(auth.uid()));


create policy "Active clubs are viewable by members"
on "public"."clubs"
as permissive
for select
to public
using (((is_active = true) AND (id IN ( SELECT cm.club_id
   FROM club_memberships cm
  WHERE ((cm.user_id = auth.uid()) AND (cm.is_active = true))))));


create policy "Club admins can view their club"
on "public"."clubs"
as permissive
for select
to public
using ((id IN ( SELECT cm.club_id
   FROM club_memberships cm
  WHERE ((cm.user_id = auth.uid()) AND (cm.role = ANY (ARRAY['admin'::text, 'super_admin'::text])) AND (cm.is_active = true)))));


create policy "Super admins can manage all clubs"
on "public"."clubs"
as permissive
for all
to public
using (has_role(auth.uid(), 'super_admin'::app_role))
with check (has_role(auth.uid(), 'super_admin'::app_role));


create policy "Allow all operations on court_availability"
on "public"."court_availability"
as permissive
for all
to public
using (true)
with check (true);


create policy "Allow all operations on court_blocks"
on "public"."court_blocks"
as permissive
for all
to public
using (true)
with check (true);


create policy "Allow all operations on courts"
on "public"."courts"
as permissive
for all
to public
using (true)
with check (true);


create policy "Super admins can manage all domains"
on "public"."domains"
as permissive
for all
to authenticated
using (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role))
with check (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));


create policy "Support admins can view domains"
on "public"."domains"
as permissive
for select
to authenticated
using ((has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN'::meta_admin_role) OR has_meta_admin_role(auth.uid(), 'READONLY'::meta_admin_role)));


create policy "Meta admins can view email templates"
on "public"."email_templates"
as permissive
for select
to authenticated
using (is_meta_admin(auth.uid()));


create policy "Super admins can manage email templates"
on "public"."email_templates"
as permissive
for all
to authenticated
using (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role))
with check (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));


create policy "Admins can manage all fee assignments"
on "public"."fee_assignments"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Users can view their own fee assignments"
on "public"."fee_assignments"
as permissive
for select
to public
using ((user_id = auth.uid()));


create policy "Users view fee assignments for effective accounts"
on "public"."fee_assignments"
as permissive
for select
to authenticated
using ((account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))));


create policy "Admins can manage fee categories"
on "public"."fee_categories"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Everyone can view active categories"
on "public"."fee_categories"
as permissive
for select
to public
using ((is_active = true));


create policy "Admins can manage fee mappings"
on "public"."fee_type_membership_types"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Everyone can view fee mappings"
on "public"."fee_type_membership_types"
as permissive
for select
to public
using (true);


create policy "Admins can manage fee types"
on "public"."fee_types"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Everyone can view active fee types"
on "public"."fee_types"
as permissive
for select
to public
using ((is_active = true));


create policy "Account owner can view own group membership rows"
on "public"."group_members"
as permissive
for select
to public
using (account_belongs_to_user(account_id, auth.uid()));


create policy "Admins manage group_members"
on "public"."group_members"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Billing member manage members of own group"
on "public"."group_members"
as permissive
for all
to public
using (is_billing_member_for_group(group_id, auth.uid()))
with check (is_billing_member_for_group(group_id, auth.uid()));


create policy "Admins manage groups"
on "public"."groups"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Billing member can update group"
on "public"."groups"
as permissive
for update
to public
using (account_belongs_to_user(billing_member_id, auth.uid()))
with check (account_belongs_to_user(billing_member_id, auth.uid()));


create policy "Billing member can view group"
on "public"."groups"
as permissive
for select
to public
using (account_belongs_to_user(billing_member_id, auth.uid()));


create policy "Admins manage guardianships"
on "public"."guardianships"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Guardians view active guardianships"
on "public"."guardianships"
as permissive
for select
to authenticated
using (((guardian_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))) OR (dependent_account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id)))));


create policy "Admins manage household_members"
on "public"."household_members"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Users view their household membership"
on "public"."household_members"
as permissive
for select
to authenticated
using ((account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))));


create policy "Admins manage households"
on "public"."households"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Admins manage invites"
on "public"."invites"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Owners manage their invites"
on "public"."invites"
as permissive
for all
to authenticated
using ((account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))))
with check ((account_id IN ( SELECT effective_rights.account_id
   FROM effective_rights(auth.uid()) effective_rights(account_id))));


create policy "Admins manage login_identities"
on "public"."login_identities"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Owners view/manage their login identities"
on "public"."login_identities"
as permissive
for all
to authenticated
using ((EXISTS ( SELECT 1
   FROM accounts a
  WHERE ((a.id = login_identities.account_id) AND (a.user_id = auth.uid())))))
with check ((EXISTS ( SELECT 1
   FROM accounts a
  WHERE ((a.id = login_identities.account_id) AND (a.user_id = auth.uid())))));


create policy "Admins can manage members"
on "public"."members"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Authenticated users can view members"
on "public"."members"
as permissive
for select
to public
using ((auth.uid() IS NOT NULL));


create policy "Admin manage all cancellations"
on "public"."membership_cancellations"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Users can create own cancellations"
on "public"."membership_cancellations"
as permissive
for insert
to public
with check (((user_id = auth.uid()) AND ((group_id IS NULL) OR is_billing_member_for_group(group_id, auth.uid()))));


create policy "Users can delete pending own cancellations"
on "public"."membership_cancellations"
as permissive
for delete
to public
using (((user_id = auth.uid()) AND (status = 'pending'::cancellation_status)));


create policy "Users can update pending own cancellations"
on "public"."membership_cancellations"
as permissive
for update
to public
using (((user_id = auth.uid()) AND (status = 'pending'::cancellation_status)))
with check ((user_id = auth.uid()));


create policy "Users can view own cancellations"
on "public"."membership_cancellations"
as permissive
for select
to public
using ((user_id = auth.uid()));


create policy "Admins can manage membership types"
on "public"."membership_types"
as permissive
for all
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Allow viewing membership types for authenticated users"
on "public"."membership_types"
as permissive
for select
to public
using ((auth.uid() IS NOT NULL));


create policy "Everyone can view active membership types"
on "public"."membership_types"
as permissive
for select
to public
using ((is_active = true));


create policy "Super admins can insert meta admin permissions"
on "public"."meta_admin_permissions"
as permissive
for insert
to authenticated
with check (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));


create policy "Super admins can manage meta admin permissions"
on "public"."meta_admin_permissions"
as permissive
for all
to authenticated
using (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role))
with check (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));


create policy "Users can view their own meta admin permissions"
on "public"."meta_admin_permissions"
as permissive
for select
to authenticated
using ((user_id = auth.uid()));


create policy "Meta admins can view plans"
on "public"."plans"
as permissive
for select
to authenticated
using (is_meta_admin(auth.uid()));


create policy "Super admins can manage plans"
on "public"."plans"
as permissive
for all
to authenticated
using (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role))
with check (has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role));


create policy "Users can insert their own profile"
on "public"."profiles"
as permissive
for insert
to public
with check ((auth.uid() = id));


create policy "Users can update their own profile"
on "public"."profiles"
as permissive
for update
to public
using ((auth.uid() = id));


create policy "Users can view their own profile"
on "public"."profiles"
as permissive
for select
to public
using ((auth.uid() = id));


create policy "Everyone can view role permissions"
on "public"."role_permissions"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can manage role permissions"
on "public"."role_permissions"
as permissive
for all
to authenticated
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Support admins can manage tickets"
on "public"."support_tickets"
as permissive
for all
to authenticated
using ((has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role) OR has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN'::meta_admin_role)))
with check ((has_meta_admin_role(auth.uid(), 'SUPER_ADMIN'::meta_admin_role) OR has_meta_admin_role(auth.uid(), 'SUPPORT_ADMIN'::meta_admin_role)));


create policy "Meta admins can view metrics"
on "public"."system_metrics"
as permissive
for select
to authenticated
using (is_meta_admin(auth.uid()));


create policy "System can insert metrics"
on "public"."system_metrics"
as permissive
for insert
to authenticated
with check (true);


create policy "Allow all operations on system_settings"
on "public"."system_settings"
as permissive
for all
to public
using (true)
with check (true);


create policy "Admins can delete roles"
on "public"."user_roles"
as permissive
for delete
to public
using (has_role(auth.uid(), 'admin'::app_role));


create policy "Admins can manage roles"
on "public"."user_roles"
as permissive
for insert
to public
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Admins can update roles"
on "public"."user_roles"
as permissive
for update
to public
using (has_role(auth.uid(), 'admin'::app_role))
with check (has_role(auth.uid(), 'admin'::app_role));


create policy "Authenticated users can view roles"
on "public"."user_roles"
as permissive
for select
to public
using ((auth.uid() IS NOT NULL));


create policy "Users can view their own roles"
on "public"."user_roles"
as permissive
for select
to authenticated
using ((user_id = auth.uid()));


create policy "Allow all operations on work_log_assignments"
on "public"."work_log_assignments"
as permissive
for all
to public
using (true)
with check (true);


create policy "Allow all operations on work_logs"
on "public"."work_logs"
as permissive
for all
to public
using (true)
with check (true);


CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON public.accounts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER set_bookings_updated_at BEFORE UPDATE ON public.bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trg_prevent_overlapping_bookings BEFORE INSERT OR UPDATE ON public.bookings FOR EACH ROW EXECUTE FUNCTION prevent_overlapping_bookings();

CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON public.bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_club_subscriptions_updated_at BEFORE UPDATE ON public.club_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_clubs_updated_at BEFORE UPDATE ON public.clubs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_court_blocks_updated_at BEFORE UPDATE ON public.court_blocks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_courts_updated_at BEFORE UPDATE ON public.courts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_domains_updated_at BEFORE UPDATE ON public.domains FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON public.email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fee_assignments_updated_at BEFORE UPDATE ON public.fee_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fee_categories_updated_at BEFORE UPDATE ON public.fee_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fee_types_updated_at BEFORE UPDATE ON public.fee_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_members_updated_at BEFORE UPDATE ON public.members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_membership_types_updated_at BEFORE UPDATE ON public.membership_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_meta_admin_permissions_updated_at BEFORE UPDATE ON public.meta_admin_permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_plans_updated_at BEFORE UPDATE ON public.plans FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON public.support_tickets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_settings_updated_at BEFORE UPDATE ON public.system_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


