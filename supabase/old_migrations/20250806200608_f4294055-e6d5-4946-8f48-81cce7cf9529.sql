-- Remove demo bookings if any exist
-- Since we don't have a bookings table yet, just clean up any test data in court_availability if needed

-- Create bookings table for actual bookings
CREATE TABLE IF NOT EXISTS public.bookings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  court_id UUID NOT NULL REFERENCES public.courts(id) ON DELETE CASCADE,
  booking_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  player_name TEXT NOT NULL,
  partner_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Create policies for bookings
CREATE POLICY "Allow all operations on bookings" 
ON public.bookings 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- <PERSON>reate function to update timestamps
CREATE TRIGGER update_bookings_updated_at
BEFORE UPDATE ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create table for blocking courts for specific dates (maintenance, tournaments, etc.)
CREATE TABLE IF NOT EXISTS public.court_blocks (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  court_id UUID NOT NULL REFERENCES public.courts(id) ON DELETE CASCADE,
  block_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  reason TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.court_blocks ENABLE ROW LEVEL SECURITY;

-- Create policies for court_blocks
CREATE POLICY "Allow all operations on court_blocks" 
ON public.court_blocks 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Create trigger for timestamps
CREATE TRIGGER update_court_blocks_updated_at
BEFORE UPDATE ON public.court_blocks
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();