-- Add new meta admin roles
CREATE TYPE public.meta_admin_role AS ENUM ('SUPER_ADMIN', 'SUPPORT_ADMIN', 'BILLING_ADMIN', 'READONLY');

-- Create meta_admin_permissions table
CREATE TABLE public.meta_admin_permissions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  role meta_admin_role NOT NULL,
  granted_by UUID REFERENCES auth.users(id) NOT NULL,
  granted_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, role)
);

-- Enable RLS
ALTER TABLE public.meta_admin_permissions ENABLE ROW LEVEL SECURITY;

-- Only super admins can manage meta admin permissions
CREATE POLICY "Super admins can manage meta admin permissions"
ON public.meta_admin_permissions
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.meta_admin_permissions mp 
    WHERE mp.user_id = auth.uid() 
    AND mp.role = 'SUPER_ADMIN'
    AND (mp.expires_at IS NULL OR mp.expires_at > now())
  )
);

-- Function to check meta admin role
CREATE OR REPLACE FUNCTION public.has_meta_admin_role(_user_id UUID, _role meta_admin_role)
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.meta_admin_permissions
    WHERE user_id = _user_id
      AND role = _role
      AND (expires_at IS NULL OR expires_at > now())
  )
$$;

-- Function to check if user has any meta admin role
CREATE OR REPLACE FUNCTION public.is_meta_admin(_user_id UUID)
RETURNS BOOLEAN
LANGUAGE SQL
STABLE
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.meta_admin_permissions
    WHERE user_id = _user_id
      AND (expires_at IS NULL OR expires_at > now())
  )
$$;

-- Create trigger for timestamps
CREATE TRIGGER update_meta_admin_permissions_updated_at
  BEFORE UPDATE ON public.meta_admin_permissions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();