-- Move extensions to the 'extensions' schema to satisfy linter recommendations
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM pg_namespace WHERE nspname = 'extensions') THEN
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'btree_gist') THEN
      EXECUTE 'ALTER EXTENSION btree_gist SET SCHEMA extensions';
    END IF;
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
      EXECUTE 'ALTER EXTENSION pgcrypto SET SCHEMA extensions';
    END IF;
  END IF;
END $$;