-- Create fee_types table for flexible fee management
CREATE TABLE public.fee_types (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'EUR',
  billing_cycle TEXT NOT NULL DEFAULT 'one_time', -- 'one_time', 'monthly', 'quarterly', 'semi_annual', 'annual'
  category TEXT NOT NULL DEFAULT 'membership', -- 'membership', 'course', 'event', 'other'
  is_active BOOLEAN NOT NULL DEFAULT true,
  requires_membership BOOLEAN NOT NULL DEFAULT false,
  age_restrictions JSONB, -- {min_age: 18, max_age: 65} or null for no restrictions
  validity_period_days INTEGER, -- null for no expiry, otherwise days
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.fee_types ENABLE ROW LEVEL SECURITY;

-- <PERSON>reate policies
CREATE POLICY "Everyone can view active fee types" 
ON public.fee_types 
FOR SELECT 
USING (is_active = true);

CREATE POLICY "Admins can manage fee types" 
ON public.fee_types 
FOR ALL 
USING (has_role(auth.uid(), 'admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

-- Add trigger for updated_at
CREATE TRIGGER update_fee_types_updated_at
  BEFORE UPDATE ON public.fee_types
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert some default fee types
INSERT INTO public.fee_types (name, description, amount, billing_cycle, category) VALUES
('Jahresmitgliedschaft Erwachsene', 'Vollmitgliedschaft für ein Jahr', 180.00, 'annual', 'membership'),
('Halbjahresmitgliedschaft', 'Mitgliedschaft für 6 Monate', 100.00, 'semi_annual', 'membership'),
('Jugendmitgliedschaft', 'Jahresmitgliedschaft für Jugendliche bis 18 Jahre', 120.00, 'annual', 'membership'),
('Sommer-Camp', 'Tenniscamp in den Sommerferien', 150.00, 'one_time', 'course'),
('Platzgebühr Einzelstunde', 'Gebühr für eine Einzelstunde Platznutzung', 15.00, 'one_time', 'other');

-- Create fee_assignments table to track who pays what
CREATE TABLE public.fee_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  fee_type_id UUID NOT NULL REFERENCES public.fee_types(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  due_date DATE,
  paid_at TIMESTAMP WITH TIME ZONE,
  amount_paid DECIMAL(10,2),
  payment_method TEXT,
  payment_reference TEXT,
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'paid', 'overdue', 'cancelled'
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS for fee_assignments
ALTER TABLE public.fee_assignments ENABLE ROW LEVEL SECURITY;

-- Policies for fee_assignments
CREATE POLICY "Users can view their own fee assignments" 
ON public.fee_assignments 
FOR SELECT 
USING (user_id = auth.uid());

CREATE POLICY "Admins can manage all fee assignments" 
ON public.fee_assignments 
FOR ALL 
USING (has_role(auth.uid(), 'admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

-- Add trigger for updated_at
CREATE TRIGGER update_fee_assignments_updated_at
  BEFORE UPDATE ON public.fee_assignments
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();