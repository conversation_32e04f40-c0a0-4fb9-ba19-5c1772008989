-- Create membership type enum
CREATE TYPE public.membership_type AS ENUM ('Child', 'Youth', 'Student', 'Adult', 'Couple', 'Family');

-- Create members table
CREATE TABLE public.members (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    birth_date DATE NOT NULL,
    address TEXT NOT NULL,
    email TEXT NOT NULL UNIQUE,
    phone TEXT NOT NULL,
    iban TEXT NOT NULL,
    membership_type membership_type NOT NULL,
    annual_fee DECIMAL(10,2) NOT NULL,
    payment_status TEXT NOT NULL DEFAULT 'unpaid' CHECK (payment_status IN ('paid', 'unpaid')),
    group_id UUID,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create groups table for couples and families
CREATE TABLE public.groups (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    type membership_type NOT NULL CHECK (type IN ('Couple', 'Family')),
    total_fee DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create activities table
CREATE TABLE public.activities (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    hourly_rate DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create work logs table
CREATE TABLE public.work_logs (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    activity_id UUID NOT NULL REFERENCES public.activities(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    duration_hours DECIMAL(5,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create work log assignments (many-to-many between work logs and members)
CREATE TABLE public.work_log_assignments (
    id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    work_log_id UUID NOT NULL REFERENCES public.work_logs(id) ON DELETE CASCADE,
    member_id UUID NOT NULL REFERENCES public.members(id) ON DELETE CASCADE,
    UNIQUE(work_log_id, member_id)
);

-- Enable Row Level Security (but allow all operations for now since no auth)
ALTER TABLE public.members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.work_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.work_log_assignments ENABLE ROW LEVEL SECURITY;

-- Create permissive policies (allow all operations for now)
CREATE POLICY "Allow all operations on members" ON public.members FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on groups" ON public.groups FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on activities" ON public.activities FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on work_logs" ON public.work_logs FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all operations on work_log_assignments" ON public.work_log_assignments FOR ALL USING (true) WITH CHECK (true);

-- Add foreign key constraint for group_id after creating groups table
ALTER TABLE public.members ADD CONSTRAINT fk_members_group FOREIGN KEY (group_id) REFERENCES public.groups(id) ON DELETE SET NULL;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates on members
CREATE TRIGGER update_members_updated_at
    BEFORE UPDATE ON public.members
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_members_email ON public.members(email);
CREATE INDEX idx_members_membership_type ON public.members(membership_type);
CREATE INDEX idx_members_group_id ON public.members(group_id);
CREATE INDEX idx_work_logs_date ON public.work_logs(date);
CREATE INDEX idx_work_log_assignments_member_id ON public.work_log_assignments(member_id);