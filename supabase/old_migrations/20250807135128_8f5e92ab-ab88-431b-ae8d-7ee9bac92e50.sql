-- Create the membership_category enum type that's missing
CREATE TYPE public.membership_category AS ENUM (
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>gen<PERSON><PERSON>',
  'Kinder',
  'Familie',
  'Student'
);

-- Recreate the trigger function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    membership_category
  )
  VALUES (
    new.id, 
    new.raw_user_meta_data ->> 'first_name', 
    new.raw_user_meta_data ->> 'last_name',
    (new.raw_user_meta_data ->> 'birth_date')::date,
    new.raw_user_meta_data ->> 'street',
    new.raw_user_meta_data ->> 'house_number',
    new.raw_user_meta_data ->> 'postal_code',
    new.raw_user_meta_data ->> 'city',
    new.email,
    (new.raw_user_meta_data ->> 'membership_category')::membership_category
  );
  R<PERSON><PERSON><PERSON> new;
END;
$$;

-- Create the trigger that connects user registration to profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();