-- First check if the enum exists and create it if needed
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'membership_category') THEN
        CREATE TYPE public.membership_category AS ENUM (
            'Kind',
            'Jugendlicher', 
            'Student',
            '<PERSON><PERSON><PERSON><PERSON>ener',
            'Familie'
        );
    END IF;
END $$;

-- Drop and recreate the function with explicit schema reference
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
  -- Add some logging to help debug
  RAISE LOG 'Creating profile for user: %', NEW.id;
  
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    membership_category
  )
  VALUES (
    NEW.id, 
    COALESCE(NEW.raw_user_meta_data ->> 'first_name', 'Unknown'),
    COALESCE(NEW.raw_user_meta_data ->> 'last_name', 'Unknown'),
    CASE 
      WHEN NEW.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
      THEN (NEW.raw_user_meta_data ->> 'birth_date')::date
      ELSE CURRENT_DATE
    END,
    COALESCE(NEW.raw_user_meta_data ->> 'street', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'house_number', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'postal_code', ''),
    COALESCE(NEW.raw_user_meta_data ->> 'city', ''),
    NEW.email,
    COALESCE((NEW.raw_user_meta_data ->> 'membership_category')::public.membership_category, 'Erwachsener'::public.membership_category)
  );
  
  RAISE LOG 'Profile created successfully for user: %', NEW.id;
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
    RAISE;
END;
$$;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();