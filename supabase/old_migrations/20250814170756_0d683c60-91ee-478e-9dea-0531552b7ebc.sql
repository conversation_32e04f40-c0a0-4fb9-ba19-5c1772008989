-- <PERSON><PERSON><PERSON> den ersten Super-Ad<PERSON> hinzu (ersetze USER_ID mit Ihrer tatsächlichen User-ID)
-- Sie können Ihre User-ID nach der Registrierung in der Supabase Auth-Tabelle finden

INSERT INTO meta_admin_permissions (user_id, role, granted_by, granted_at)
VALUES (
  'YOUR_USER_ID_HERE'::uuid, 
  'SUPER_ADMIN'::meta_admin_role,
  'YOUR_USER_ID_HERE'::uuid,
  now()
);

-- Optional: Weitere Meta-Admin-Rollen hinzufügen
-- INSERT INTO meta_admin_permissions (user_id, role, granted_by, granted_at)
-- VALUES ('OTHER_USER_ID', 'SUPPORT_ADMIN', 'YOUR_USER_ID_HERE', now());