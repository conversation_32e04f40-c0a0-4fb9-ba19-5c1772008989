
-- 1) En<PERSON> für Zusagen
create type if not exists public.rsvp_status as enum ('yes','no','maybe');

-- 2) Teams
create table if not exists public.teams (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  description text,
  league text,
  captain_account_id uuid references public.accounts(id) on delete set null,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
alter table public.teams enable row level security;

-- 3) Team-Mitglieder
create table if not exists public.team_members (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  account_id uuid not null references public.accounts(id) on delete cascade,
  role text not null default 'player',
  joined_at timestamptz not null default now(),
  unique (team_id, account_id)
);
alter table public.team_members enable row level security;

-- 4) <PERSON>piele
create table if not exists public.team_games (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  opponent text not null,
  match_date date not null,
  start_time time not null,
  location text,
  home boolean not null default true,
  result text,
  notes text,
  created_by uuid,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
alter table public.team_games enable row level security;

-- 5) Trainings
create table if not exists public.team_trainings (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  training_date date not null,
  start_time time not null,
  duration_minutes integer,
  location text,
  notes text,
  created_by uuid,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
alter table public.team_trainings enable row level security;

-- 6) RSVPs Spiele
create table if not exists public.team_game_rsvps (
  id uuid primary key default gen_random_uuid(),
  game_id uuid not null references public.team_games(id) on delete cascade,
  account_id uuid not null references public.accounts(id) on delete cascade,
  status public.rsvp_status not null,
  comment text,
  updated_at timestamptz not null default now(),
  unique (game_id, account_id)
);
alter table public.team_game_rsvps enable row level security;

-- 7) RSVPs Trainings
create table if not exists public.team_training_rsvps (
  id uuid primary key default gen_random_uuid(),
  training_id uuid not null references public.team_trainings(id) on delete cascade,
  account_id uuid not null references public.accounts(id) on delete cascade,
  status public.rsvp_status not null,
  comment text,
  updated_at timestamptz not null default now(),
  unique (training_id, account_id)
);
alter table public.team_training_rsvps enable row level security;

-- 8) Team-Chat / Nachrichten
create table if not exists public.team_messages (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  author_id uuid not null references public.accounts(id) on delete cascade,
  content text not null,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
alter table public.team_messages enable row level security;

-- 9) Helferfunktionen (Security Definer) für RLS
create or replace function public.is_team_member(_team_id uuid, _user_id uuid)
returns boolean
language sql
stable
security definer
set search_path = public
as $$
  select exists (
    select 1
    from public.team_members tm
    join public.accounts a on a.id = tm.account_id
    where tm.team_id = _team_id
      and a.user_id = _user_id
  );
$$;

create or replace function public.is_team_member_by_game(_game_id uuid, _user_id uuid)
returns boolean
language sql
stable
security definer
set search_path = public
as $$
  select public.is_team_member(g.team_id, _user_id)
  from public.team_games g
  where g.id = _game_id;
$$;

create or replace function public.is_team_member_by_training(_training_id uuid, _user_id uuid)
returns boolean
language sql
stable
security definer
set search_path = public
as $$
  select public.is_team_member(t.team_id, _user_id)
  from public.team_trainings t
  where t.id = _training_id;
$$;

-- 10) RLS-Policies

-- Teams
create policy if not exists "Admins manage teams" on public.teams
for all
using (public.has_role(auth.uid(), 'admin'))
with check (public.has_role(auth.uid(), 'admin'));

create policy if not exists "Members can view their teams" on public.teams
for select
using (public.is_team_member(id, auth.uid()));

create policy if not exists "Members can update their teams" on public.teams
for update
using (public.is_team_member(id, auth.uid()))
with check (public.is_team_member(id, auth.uid()));

create policy if not exists "Members can delete their teams" on public.teams
for delete
using (public.is_team_member(id, auth.uid()));

-- Team-Mitglieder
create policy if not exists "Admins manage team_members" on public.team_members
for all
using (public.has_role(auth.uid(), 'admin'))
with check (public.has_role(auth.uid(), 'admin'));

create policy if not exists "Team members can view team_members" on public.team_members
for select
using (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can manage roster" on public.team_members
for insert
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can update roster" on public.team_members
for update
using (public.is_team_member(team_id, auth.uid()))
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can remove roster" on public.team_members
for delete
using (public.is_team_member(team_id, auth.uid()));

-- Spiele
create policy if not exists "Admins manage team_games" on public.team_games
for all
using (public.has_role(auth.uid(), 'admin'))
with check (public.has_role(auth.uid(), 'admin'));

create policy if not exists "Team members can view games" on public.team_games
for select
using (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can manage games" on public.team_games
for insert
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can update games" on public.team_games
for update
using (public.is_team_member(team_id, auth.uid()))
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can delete games" on public.team_games
for delete
using (public.is_team_member(team_id, auth.uid()));

-- Trainings
create policy if not exists "Admins manage team_trainings" on public.team_trainings
for all
using (public.has_role(auth.uid(), 'admin'))
with check (public.has_role(auth.uid(), 'admin'));

create policy if not exists "Team members can view trainings" on public.team_trainings
for select
using (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can manage trainings" on public.team_trainings
for insert
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can update trainings" on public.team_trainings
for update
using (public.is_team_member(team_id, auth.uid()))
with check (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members can delete trainings" on public.team_trainings
for delete
using (public.is_team_member(team_id, auth.uid()));

-- RSVPs: Spiele
create policy if not exists "Team members view game RSVPs" on public.team_game_rsvps
for select
using (public.is_team_member_by_game(game_id, auth.uid()));

create policy if not exists "Members set their own game RSVP" on public.team_game_rsvps
for insert
with check (public.is_team_member_by_game(game_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

create policy if not exists "Members update their own game RSVP" on public.team_game_rsvps
for update
using (public.is_team_member_by_game(game_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()))
with check (public.is_team_member_by_game(game_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

create policy if not exists "Members delete their own game RSVP" on public.team_game_rsvps
for delete
using (public.is_team_member_by_game(game_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

-- RSVPs: Trainings
create policy if not exists "Team members view training RSVPs" on public.team_training_rsvps
for select
using (public.is_team_member_by_training(training_id, auth.uid()));

create policy if not exists "Members set their own training RSVP" on public.team_training_rsvps
for insert
with check (public.is_team_member_by_training(training_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

create policy if not exists "Members update their own training RSVP" on public.team_training_rsvps
for update
using (public.is_team_member_by_training(training_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()))
with check (public.is_team_member_by_training(training_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

create policy if not exists "Members delete their own training RSVP" on public.team_training_rsvps
for delete
using (public.is_team_member_by_training(training_id, auth.uid()) and public.account_belongs_to_user(account_id, auth.uid()));

-- Nachrichten
create policy if not exists "Team members view messages" on public.team_messages
for select
using (public.is_team_member(team_id, auth.uid()));

create policy if not exists "Team members add messages" on public.team_messages
for insert
with check (public.is_team_member(team_id, auth.uid()) and public.account_belongs_to_user(author_id, auth.uid()));

create policy if not exists "Authors can update their messages" on public.team_messages
for update
using (public.is_team_member(team_id, auth.uid()) and public.account_belongs_to_user(author_id, auth.uid()))
with check (public.is_team_member(team_id, auth.uid()) and public.account_belongs_to_user(author_id, auth.uid()));

create policy if not exists "Authors can delete their messages" on public.team_messages
for delete
using (public.is_team_member(team_id, auth.uid()) and public.account_belongs_to_user(author_id, auth.uid()));

-- 11) Trigger für updated_at
create trigger if not exists set_updated_at_teams
before update on public.teams
for each row execute procedure public.update_updated_at_column();

create trigger if not exists set_updated_at_team_games
before update on public.team_games
for each row execute procedure public.update_updated_at_column();

create trigger if not exists set_updated_at_team_trainings
before update on public.team_trainings
for each row execute procedure public.update_updated_at_column();

create trigger if not exists set_updated_at_team_messages
before update on public.team_messages
for each row execute procedure public.update_updated_at_column();

-- 12) Indizes
create index if not exists idx_team_members_team on public.team_members (team_id);
create index if not exists idx_team_members_account on public.team_members (account_id);
create index if not exists idx_team_games_team_date on public.team_games (team_id, match_date);
create index if not exists idx_team_trainings_team_date on public.team_trainings (team_id, training_date);
create index if not exists idx_team_messages_team_created on public.team_messages (team_id, created_at);
create index if not exists idx_game_rsvps_game on public.team_game_rsvps (game_id);
create index if not exists idx_game_rsvps_account on public.team_game_rsvps (account_id);
create index if not exists idx_training_rsvps_training on public.team_training_rsvps (training_id);
create index if not exists idx_training_rsvps_account on public.team_training_rsvps (account_id);
