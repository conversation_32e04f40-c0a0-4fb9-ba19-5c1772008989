-- Insert demo Member accounts  
INSERT INTO public.accounts (first_name, last_name, birth_date, address_street, address_house_number, address_postal_code, address_city, email, phone, account_type, membership_type) VALUES
('<PERSON>', '<PERSON>', '1985-03-15', 'Hauptstraße', '12', '10115', 'Berlin', '<EMAIL>', '+49 30 ********', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
('<PERSON>', '<PERSON>', '1992-07-22', 'Gartenstraße', '8', '20095', 'Hamburg', '<EMAIL>', '+49 40 ********', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
('<PERSON>', '<PERSON>', '1988-11-05', 'Rosenweg', '15', '80331', '<PERSON><PERSON><PERSON>', '<EMAIL>', '+49 89 ********', 'Member', '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
('<PERSON>', '<PERSON>', '1976-09-12', 'Linden<PERSON><PERSON>', '22', '50667', '<PERSON><PERSON><PERSON>', 'micha<PERSON>.<PERSON><PERSON>@email.com', '+49 221 ********', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
('<PERSON>', '<PERSON>', '1995-04-18', '<PERSON><PERSON><PERSON>ra<PERSON>', '7', '70173', 'Stuttgart', 'julia.be<PERSON>@email.com', '+49 711 ********', '<PERSON>', '<PERSON>rwa<PERSON><PERSON>'),
('<PERSON>', '<PERSON>', '1983-12-03', '<PERSON>irkenweg', '33', '40212', 'Düsseldorf', '<EMAIL>', '+49 211 12345606', '<PERSON>', '<PERSON>rwachsener'),
('Lisa', 'Hoffmann', '1990-06-25', 'Kastanienstraße', '18', '04109', 'Leipzig', '<EMAIL>', '+49 341 12345607', 'Member', 'Erwachsener'),
('Andreas', 'Klein', '1987-08-14', 'Tannenweg', '9', '01067', 'Dresden', '<EMAIL>', '+49 351 12345608', 'Member', 'Erwachsener'),
('Emma', 'Richter', '2010-02-28', 'Ahornstraße', '25', '60311', 'Frankfurt', '<EMAIL>', '+49 69 ********', 'Member', 'Jugendlicher'),
('Max', 'Braun', '2012-05-10', 'Ulmenweg', '14', '30159', 'Hannover', '<EMAIL>', '+49 511 ********', 'Member', 'Jugendlicher'),
('Sophie', 'Zimmermann', '2008-09-17', 'Buchenstraße', '31', '90402', 'Nürnberg', '<EMAIL>', '+49 911 ********', 'Member', 'Jugendlicher'),
('Leon', 'Krüger', '2011-01-20', 'Eschenweg', '6', '68159', 'Mannheim', '<EMAIL>', '+49 621 ********', 'Member', 'Jugendlicher');

-- Insert demo Admin accounts
INSERT INTO public.accounts (first_name, last_name, birth_date, address_street, address_house_number, address_postal_code, address_city, email, phone, account_type) VALUES
('Petra', 'Hartmann', '1975-03-08', 'Verwaltungsstraße', '1', '10117', 'Berlin', '<EMAIL>', '+49 30 ********', 'Admin'),
('Klaus', 'Schulz', '1982-11-15', 'Büroweg', '3', '20148', 'Hamburg', '<EMAIL>', '+49 40 ********', 'Admin');

-- Insert demo Trainer account
INSERT INTO public.accounts (first_name, last_name, birth_date, address_street, address_house_number, address_postal_code, address_city, email, phone, account_type) VALUES
('Marco', 'Sport', '1979-06-12', 'Trainingsplatz', '5', '80802', 'München', '<EMAIL>', '+49 89 ********', 'Trainer');