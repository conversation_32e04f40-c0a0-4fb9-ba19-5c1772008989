-- Update RLS policy for membership_types to allow broader access for admin pages
DROP POLICY IF EXISTS "Everyone can view active membership types" ON public.membership_types;

-- Create a more permissive policy that allows viewing membership types
-- This is needed for admin interfaces to work properly
CREATE POLICY "Allow viewing membership types for authenticated users" 
ON public.membership_types 
FOR SELECT 
USING (auth.uid() IS NOT NULL);