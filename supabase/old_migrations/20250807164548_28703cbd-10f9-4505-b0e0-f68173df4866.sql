-- Add start_date and end_date columns to court_blocks table
ALTER TABLE public.court_blocks 
ADD COLUMN start_date date,
ADD COLUMN end_date date;

-- Migrate existing data: copy block_date to both start_date and end_date
UPDATE public.court_blocks 
SET start_date = block_date, 
    end_date = block_date 
WHERE start_date IS NULL OR end_date IS NULL;

-- Make the new columns required
ALTER TABLE public.court_blocks 
ALTER COLUMN start_date SET NOT NULL,
ALTER COLUMN end_date SET NOT NULL;

-- Drop the old block_date column
ALTER TABLE public.court_blocks 
DROP COLUMN block_date;

-- Add check constraint to ensure end_date >= start_date
ALTER TABLE public.court_blocks 
ADD CONSTRAINT check_valid_date_range 
CHECK (end_date >= start_date);