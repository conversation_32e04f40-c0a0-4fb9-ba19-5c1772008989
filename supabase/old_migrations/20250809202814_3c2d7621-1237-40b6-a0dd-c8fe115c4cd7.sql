
-- 1) Enum für Kündigungs-Status
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'cancellation_status') THEN
    CREATE TYPE public.cancellation_status AS ENUM ('pending', 'approved', 'rejected');
  END IF;
END$$;

-- 2) accounts um user_id erweitern (Zuordnung Auth-User -> Account)
ALTER TABLE public.accounts
ADD COLUMN IF NOT EXISTS user_id uuid REFERENCES auth.users(id) ON DELETE SET NULL;

-- Eindeutige Zuordnung pro User (mehrere NULLs erlaubt, aber max. 1 nicht-NULL)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'accounts_user_id_unique'
  ) THEN
    ALTER TABLE public.accounts
    ADD CONSTRAINT accounts_user_id_unique UNIQUE (user_id);
  END IF;
END$$;

-- 3) Helper-Funktionen (SECURITY DEFINER) für RLS-Ausdrücke
-- <PERSON>r<PERSON><PERSON>, ob ein Account zu einem User gehört (bypasst künftige RLS auf accounts)
CREATE OR REPLACE FUNCTION public.account_belongs_to_user(_account_id uuid, _user_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path TO public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.accounts a
    WHERE a.id = _account_id
      AND a.user_id = _user_id
  );
$$;

-- Prüft, ob ein User der "Billing Member" der Gruppe ist
CREATE OR REPLACE FUNCTION public.is_billing_member_for_group(_group_id uuid, _user_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path TO public
AS $$
  SELECT EXISTS (
    SELECT 1
    FROM public.groups g
    JOIN public.accounts a ON a.id = g.billing_member_id
    WHERE g.id = _group_id
      AND a.user_id = _user_id
  );
$$;

-- 4) Tabelle für Kündigungen
CREATE TABLE IF NOT EXISTS public.membership_cancellations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  account_id uuid REFERENCES public.accounts(id) ON DELETE SET NULL,
  group_id uuid REFERENCES public.groups(id) ON DELETE SET NULL,
  effective_date date NOT NULL,
  reason text,
  status public.cancellation_status NOT NULL DEFAULT 'pending',
  requested_at timestamptz NOT NULL DEFAULT now(),
  processed_at timestamptz,
  processed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  notes text
);

ALTER TABLE public.membership_cancellations ENABLE ROW LEVEL SECURITY;

-- Policies für Kündigungen
-- Admin: Vollzugriff
DROP POLICY IF EXISTS "Admin manage all cancellations" ON public.membership_cancellations;
CREATE POLICY "Admin manage all cancellations"
  ON public.membership_cancellations
  FOR ALL
  USING (public.has_role(auth.uid(), 'admin'::public.app_role))
  WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));

-- Nutzer: eigene Anträge sehen
DROP POLICY IF EXISTS "Users can view own cancellations" ON public.membership_cancellations;
CREATE POLICY "Users can view own cancellations"
  ON public.membership_cancellations
  FOR SELECT
  USING (user_id = auth.uid());

-- Nutzer: eigene Anträge erstellen
-- Optional: group_id darf nur gesetzt werden, wenn Nutzer Billing Member dieser Gruppe ist
DROP POLICY IF EXISTS "Users can create own cancellations" ON public.membership_cancellations;
CREATE POLICY "Users can create own cancellations"
  ON public.membership_cancellations
  FOR INSERT
  WITH CHECK (
    user_id = auth.uid()
    AND (
      group_id IS NULL
      OR public.is_billing_member_for_group(group_id, auth.uid())
    )
  );

-- Nutzer: eigene Anträge bearbeiten/withdraw, solange sie pending sind
DROP POLICY IF EXISTS "Users can update pending own cancellations" ON public.membership_cancellations;
CREATE POLICY "Users can update pending own cancellations"
  ON public.membership_cancellations
  FOR UPDATE
  USING (user_id = auth.uid() AND status = 'pending'::public.cancellation_status)
  WITH CHECK (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can delete pending own cancellations" ON public.membership_cancellations;
CREATE POLICY "Users can delete pending own cancellations"
  ON public.membership_cancellations
  FOR DELETE
  USING (user_id = auth.uid() AND status = 'pending'::public.cancellation_status);

-- 5) Gruppen- und Gruppenmitglieder-Policies für Self-Service des Billing Members
-- Hinweis: Aktuell existiert eine sehr permissive Policy "Allow all operations on groups/group_members".
-- Wir ersetzen diese durch restriktive Policies mit Admin- und Billing-Member-Regeln.

-- groups
DROP POLICY IF EXISTS "Allow all operations on groups" ON public.groups;

-- Admin darf alles
DROP POLICY IF EXISTS "Admins manage groups" ON public.groups;
CREATE POLICY "Admins manage groups"
  ON public.groups
  FOR ALL
  USING (public.has_role(auth.uid(), 'admin'::public.app_role))
  WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));

-- Billing Member: Gruppe sehen/bearbeiten
-- WICHTIG: kein Funktionsaufruf, der die Tabelle groups erneut abfragt (keine Rekursion).
-- Wir nutzen account_belongs_to_user() nur gegen accounts und den Spaltenwert billing_member_id.
DROP POLICY IF EXISTS "Billing member can view group" ON public.groups;
CREATE POLICY "Billing member can view group"
  ON public.groups
  FOR SELECT
  USING (public.account_belongs_to_user(billing_member_id, auth.uid()));

DROP POLICY IF EXISTS "Billing member can update group" ON public.groups;
CREATE POLICY "Billing member can update group"
  ON public.groups
  FOR UPDATE
  USING (public.account_belongs_to_user(billing_member_id, auth.uid()))
  WITH CHECK (public.account_belongs_to_user(billing_member_id, auth.uid()));

-- group_members
DROP POLICY IF EXISTS "Allow all operations on group_members" ON public.group_members;

-- Admin darf alles
DROP POLICY IF EXISTS "Admins manage group_members" ON public.group_members;
CREATE POLICY "Admins manage group_members"
  ON public.group_members
  FOR ALL
  USING (public.has_role(auth.uid(), 'admin'::public.app_role))
  WITH CHECK (public.has_role(auth.uid(), 'admin'::public.app_role));

-- Billing Member: volle Verwaltung der Mitglieder seiner Gruppe
DROP POLICY IF EXISTS "Billing member manage members of own group" ON public.group_members;
CREATE POLICY "Billing member manage members of own group"
  ON public.group_members
  FOR ALL
  USING (public.is_billing_member_for_group(group_id, auth.uid()))
  WITH CHECK (public.is_billing_member_for_group(group_id, auth.uid()));

-- Account-Inhaber: eigene Mitgliedschaften in Gruppen sehen
DROP POLICY IF EXISTS "Account owner can view own group membership rows" ON public.group_members;
CREATE POLICY "Account owner can view own group membership rows"
  ON public.group_members
  FOR SELECT
  USING (public.account_belongs_to_user(account_id, auth.uid()));
