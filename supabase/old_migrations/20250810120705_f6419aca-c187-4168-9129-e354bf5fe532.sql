-- Backfill acting_account_id and booked_for_account_id from the creator's account (first by created_at)
WITH account_map AS (
  SELECT 
    b.id AS booking_id,
    a.id AS account_id
  FROM public.bookings b
  LEFT JOIN LATERAL (
    SELECT id
    FROM public.accounts
    WHERE user_id = b.created_by
    ORDER BY created_at ASC
    LIMIT 1
  ) a ON TRUE
)
UPDATE public.bookings b
SET 
  acting_account_id = COALESCE(b.acting_account_id, am.account_id),
  booked_for_account_id = COALESCE(b.booked_for_account_id, am.account_id)
FROM account_map am
WHERE b.id = am.booking_id
  AND (b.acting_account_id IS NULL OR b.booked_for_account_id IS NULL);

-- Backfill start_at and end_at from booking_date + start_time/end_time when missing
UPDATE public.bookings b
SET 
  start_at = COALESCE(b.start_at, (b.booking_date::timestamp + b.start_time)::timestamptz),
  end_at   = COALESCE(b.end_at,   (b.booking_date::timestamp + b.end_time)::timestamptz)
WHERE (b.start_at IS NULL OR b.end_at IS NULL)
  AND b.booking_date IS NOT NULL
  AND b.start_time IS NOT NULL
  AND b.end_time IS NOT NULL;
