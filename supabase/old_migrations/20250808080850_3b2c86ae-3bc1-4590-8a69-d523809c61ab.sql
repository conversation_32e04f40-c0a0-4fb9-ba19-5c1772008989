-- Insert demo Member accounts
INSERT INTO public.accounts (first_name, last_name, birth_date, street, house_number, postal_code, city, email, phone, account_type, membership_type, annual_fee, payment_status, iban) VALUES
('<PERSON>', '<PERSON>', '1985-03-15', 'Hauptstraße', '12', '10115', 'Berlin', '<EMAIL>', '+49 30 ********', 'Member', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 180.00, 'paid', '**********************'),
('<PERSON>', '<PERSON>', '1992-07-22', 'Gartenstraße', '8', '20095', 'Hamburg', '<EMAIL>', '+49 40 ********', 'Member', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 180.00, 'paid', '**********************'),
('<PERSON>', '<PERSON>', '1988-11-05', '<PERSON>weg', '15', '80331', '<PERSON><PERSON><PERSON>', '<EMAIL>', '+49 89 ********', 'Member', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 180.00, 'pending', '**********************'),
('<PERSON>', '<PERSON>', '1976-09-12', '<PERSON>allee', '22', '50667', '<PERSON><PERSON>ln', 'mi<PERSON><PERSON>.fi<PERSON>@email.com', '+49 221 ********', '<PERSON>', '<PERSON>rwa<PERSON><PERSON>', 180.00, 'paid', '<PERSON>89370400440532013003'),
('<PERSON>', '<PERSON>', '1995-04-18', '<PERSON>iche<PERSON>ra<PERSON>', '7', '70173', '<PERSON>', '<EMAIL>', '+49 711 12345605', 'Member', 'Erwachsener', 180.00, 'paid', 'DE89370400440532013004'),
('David', 'Wagner', '1983-12-03', 'Birkenweg', '33', '40212', 'Düsseldorf', '<EMAIL>', '+49 211 12345606', 'Member', 'Erwachsener', 180.00, 'overdue', 'DE89370400440532013005'),
('Lisa', 'Hoffmann', '1990-06-25', 'Kastanienstraße', '18', '04109', 'Leipzig', '<EMAIL>', '+49 341 12345607', 'Member', 'Erwachsener', 180.00, 'paid', 'DE89370400440532013006'),
('Andreas', 'Klein', '1987-08-14', 'Tannenweg', '9', '01067', 'Dresden', '<EMAIL>', '+49 351 12345608', 'Member', 'Erwachsener', 180.00, 'paid', 'DE89370400440532013007'),
('Emma', 'Richter', '2010-02-28', 'Ahornstraße', '25', '60311', 'Frankfurt', '<EMAIL>', '+49 69 12345609', 'Member', 'Jugendlicher', 90.00, 'paid', 'DE89370400440532013008'),
('Max', 'Braun', '2012-05-10', 'Ulmenweg', '14', '30159', 'Hannover', '<EMAIL>', '+49 511 ********', 'Member', 'Jugendlicher', 90.00, 'pending', '**********************'),
('Sophie', 'Zimmermann', '2008-09-17', 'Buchenstraße', '31', '90402', 'Nürnberg', '<EMAIL>', '+49 911 ********', 'Member', 'Jugendlicher', 90.00, 'paid', '**********************'),
('Leon', 'Krüger', '2011-01-20', 'Eschenweg', '6', '68159', 'Mannheim', '<EMAIL>', '+49 621 ********', 'Member', 'Jugendlicher', 90.00, 'paid', '**********************');

-- Insert demo Admin accounts
INSERT INTO public.accounts (first_name, last_name, birth_date, street, house_number, postal_code, city, email, phone, account_type) VALUES
('Petra', 'Hartmann', '1975-03-08', 'Verwaltungsstraße', '1', '10117', 'Berlin', '<EMAIL>', '+49 30 ********', 'Admin'),
('Klaus', 'Schulz', '1982-11-15', 'Büroweg', '3', '20148', 'Hamburg', '<EMAIL>', '+49 40 ********', 'Admin');

-- Insert demo Trainer account
INSERT INTO public.accounts (first_name, last_name, birth_date, street, house_number, postal_code, city, email, phone, account_type) VALUES
('Marco', 'Sport', '1979-06-12', 'Trainingsplatz', '5', '80802', 'München', '<EMAIL>', '+49 89 ********', 'Trainer');