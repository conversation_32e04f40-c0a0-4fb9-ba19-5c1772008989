-- Update the membership_type enum to include the new German membership types
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Kinder';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Jugendliche';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Studenten';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Erwachsene';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Senioren';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Paare';
ALTER TYPE membership_type ADD VALUE IF NOT EXISTS 'Familien';