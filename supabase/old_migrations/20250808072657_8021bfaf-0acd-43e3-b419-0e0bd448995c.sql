-- Add status and approval fields to activities table
ALTER TABLE public.activities 
ADD COLUMN status text NOT NULL DEFAULT 'approved',
ADD COLUMN requested_by uuid REFERENCES auth.users(id),
ADD COLUMN approved_by uuid REFERENCES auth.users(id),
ADD COLUMN approved_at timestamp with time zone,
ADD COLUMN created_by uuid REFERENCES auth.users(id);

-- Create activity_registrations table for member sign-ups
CREATE TABLE public.activity_registrations (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  activity_id uuid NOT NULL REFERENCES public.activities(id) ON DELETE CASCADE,
  member_id uuid NOT NULL REFERENCES public.members(id) ON DELETE CASCADE,
  registered_at timestamp with time zone NOT NULL DEFAULT now(),
  status text NOT NULL DEFAULT 'registered',
  notes text,
  UNIQUE(activity_id, member_id)
);

-- Enable RLS on activity_registrations
ALTER TABLE public.activity_registrations ENABLE ROW LEVEL SECURITY;

-- Create policies for activity_registrations
CREATE POLICY "Allow all operations on activity_registrations" 
ON public.activity_registrations 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Add index for better performance
CREATE INDEX idx_activity_registrations_activity_id ON public.activity_registrations(activity_id);
CREATE INDEX idx_activity_registrations_member_id ON public.activity_registrations(member_id);