
-- 1) Schema-Erweiterung: created_by & FK
ALTER TABLE public.bookings
  ADD COLUMN IF NOT EXISTS created_by uuid;

DO $$
BEGIN
  ALTER TABLE public.bookings
    ADD CONSTRAINT bookings_created_by_fkey
    FOREIGN KEY (created_by) REFERENCES public.profiles(id) ON DELETE SET NULL;
EXCEPTION
  WHEN duplicate_object THEN NULL;
END $$;

-- 2) Doppelbuchungen verhindern (Trigger statt CHECK/UNIQUE, wirkt nur auf neue Inserts/Updates)
CREATE OR REPLACE FUNCTION public.prevent_overlapping_bookings()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $func$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM public.bookings b
    WHERE b.court_id = NEW.court_id
      AND b.booking_date = NEW.booking_date
      -- Zeit-Überlappung prüfen (NICHT: Ende <= Start ODER Start >= Ende)
      AND NOT (NEW.end_time <= b.start_time OR NEW.start_time >= b.end_time)
      AND (TG_OP = 'INSERT' OR b.id <> NEW.id)
  ) THEN
    RAISE EXCEPTION 'Zeitfenster überschneidet sich mit bestehender Buchung';
  END IF;
  RETURN NEW;
END;
$func$;

DROP TRIGGER IF EXISTS trg_prevent_overlapping_bookings ON public.bookings;

CREATE TRIGGER trg_prevent_overlapping_bookings
BEFORE INSERT OR UPDATE ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION public.prevent_overlapping_bookings();

-- 3) updated_at automatisch pflegen
DROP TRIGGER IF EXISTS set_bookings_updated_at ON public.bookings;

CREATE TRIGGER set_bookings_updated_at
BEFORE UPDATE ON public.bookings
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- 4) RLS-Policies anpassen
-- Alte "Allow all operations on bookings" Policy entfernen
DROP POLICY IF EXISTS "Allow all operations on bookings" ON public.bookings;

-- Alle authentifizierten Nutzer dürfen Buchungen sehen
CREATE POLICY "Authenticated users can view all bookings"
  ON public.bookings
  FOR SELECT
  TO authenticated
  USING (true);

-- Nutzer dürfen eigene Buchungen anlegen (created_by = auth.uid())
CREATE POLICY "Users can create their own bookings"
  ON public.bookings
  FOR INSERT
  TO authenticated
  WITH CHECK (created_by = auth.uid());

-- Nutzer dürfen eigene Buchungen löschen
CREATE POLICY "Users can delete their own bookings"
  ON public.bookings
  FOR DELETE
  TO authenticated
  USING (created_by = auth.uid());

-- Admins dürfen alles aktualisieren
CREATE POLICY "Admins can update any booking"
  ON public.bookings
  FOR UPDATE
  TO authenticated
  USING (has_role(auth.uid(), 'admin'::app_role))
  WITH CHECK (has_role(auth.uid(), 'admin'::app_role));

-- Admins dürfen alles löschen
CREATE POLICY "Admins can delete any booking"
  ON public.bookings
  FOR DELETE
  TO authenticated
  USING (has_role(auth.uid(), 'admin'::app_role));
