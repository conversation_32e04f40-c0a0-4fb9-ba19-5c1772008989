-- Create clubs table for multi-tenant structure
CREATE TABLE public.clubs (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name text NOT NULL,
  slug text NOT NULL UNIQUE,
  custom_domain text UNIQUE,
  subdomain text UNIQUE,
  logo_url text,
  description text,
  settings jsonb DEFAULT '{}',
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Enable RLS on clubs
ALTER TABLE public.clubs ENABLE ROW LEVEL SECURITY;

-- Create club_memberships table to link users to clubs
CREATE TABLE public.club_memberships (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL,
  club_id uuid NOT NULL REFERENCES public.clubs(id) ON DELETE CASCADE,
  role text NOT NULL DEFAULT 'member',
  joined_at timestamp with time zone NOT NULL DEFAULT now(),
  is_active boolean NOT NULL DEFAULT true,
  UNIQUE(user_id, club_id)
);

-- Enable RLS on club_memberships
ALTER TABLE public.club_memberships ENABLE ROW LEVEL SECURITY;

-- Create policies for clubs
CREATE POLICY "Super admins can manage all clubs" 
ON public.clubs 
FOR ALL 
USING (has_role(auth.uid(), 'super_admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'super_admin'::app_role));

CREATE POLICY "Club admins can view their club" 
ON public.clubs 
FOR SELECT 
USING (
  id IN (
    SELECT cm.club_id 
    FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
    AND cm.role IN ('admin', 'super_admin')
    AND cm.is_active = true
  )
);

CREATE POLICY "Active clubs are viewable by members" 
ON public.clubs 
FOR SELECT 
USING (
  is_active = true AND 
  id IN (
    SELECT cm.club_id 
    FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
    AND cm.is_active = true
  )
);

-- Create policies for club_memberships
CREATE POLICY "Super admins can manage all memberships" 
ON public.club_memberships 
FOR ALL 
USING (has_role(auth.uid(), 'super_admin'::app_role))
WITH CHECK (has_role(auth.uid(), 'super_admin'::app_role));

CREATE POLICY "Club admins can manage their club memberships" 
ON public.club_memberships 
FOR ALL 
USING (
  club_id IN (
    SELECT cm.club_id 
    FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
    AND cm.role = 'admin'
    AND cm.is_active = true
  )
)
WITH CHECK (
  club_id IN (
    SELECT cm.club_id 
    FROM public.club_memberships cm 
    WHERE cm.user_id = auth.uid() 
    AND cm.role = 'admin'
    AND cm.is_active = true
  )
);

CREATE POLICY "Users can view their own memberships" 
ON public.club_memberships 
FOR SELECT 
USING (user_id = auth.uid());

-- Add club_id to existing tables for multi-tenancy
ALTER TABLE public.accounts ADD COLUMN club_id uuid REFERENCES public.clubs(id);
ALTER TABLE public.courts ADD COLUMN club_id uuid REFERENCES public.clubs(id);
ALTER TABLE public.bookings ADD COLUMN club_id uuid REFERENCES public.clubs(id);
ALTER TABLE public.members ADD COLUMN club_id uuid REFERENCES public.clubs(id);
ALTER TABLE public.activities ADD COLUMN club_id uuid REFERENCES public.clubs(id);

-- Create function to get current tenant context
CREATE OR REPLACE FUNCTION public.get_current_club_id()
RETURNS uuid
LANGUAGE sql
STABLE
AS $$
  SELECT COALESCE(
    current_setting('app.current_club_id', true)::uuid,
    null
  );
$$;

-- Create trigger for updated_at
CREATE TRIGGER update_clubs_updated_at
  BEFORE UPDATE ON public.clubs
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample clubs for development
INSERT INTO public.clubs (name, slug, subdomain, description) VALUES
('Tennis Club München', 'tcm', 'tcm', 'Premium Tennis Club in München'),
('Berliner Tennis Verein', 'btv', 'btv', 'Traditioneller Tennis Verein in Berlin'),
('Hamburg Tennis Club', 'htc', 'htc', 'Exklusiver Tennis Club in Hamburg');