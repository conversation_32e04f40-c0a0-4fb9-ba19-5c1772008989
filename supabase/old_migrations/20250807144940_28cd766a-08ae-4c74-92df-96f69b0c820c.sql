-- Update Courts table with missing fields
ALTER TABLE public.courts 
ADD COLUMN IF NOT EXISTS name TEXT NOT NULL DEFAULT 'Court ' || number::text,
ADD COLUMN IF NOT EXISTS type TEXT NOT NULL DEFAULT 'Indoor' CHECK (type IN ('Indoor', 'Outdoor')),
ADD COLUMN IF NOT EXISTS club_id UUID, -- We'll need to create clubs table later if not exists
ADD COLUMN IF NOT EXISTS description TEXT;

-- Update the default name to be more descriptive
UPDATE public.courts SET name = 'Court ' || number::text WHERE name = 'Court ' || number::text;

-- Update Bookings table with missing fields
ALTER TABLE public.bookings
ADD COLUMN IF NOT EXISTS member_id UUID, -- References members table
ADD COLUMN IF NOT EXISTS status TEXT NOT NULL DEFAULT 'geplant' CHECK (status IN ('geplant', 'storniert')),
ADD COLUMN IF NOT EXISTS created_by UUID; -- References members table

-- Combine booking_date, start_time, end_time into proper datetime fields
ALTER TABLE public.bookings
ADD COLUMN IF NOT EXISTS start_datetime TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS end_datetime TIMESTAMP WITH TIME ZONE;

-- Update existing bookings to use the new datetime fields
UPDATE public.bookings 
SET 
  start_datetime = (booking_date + start_time)::timestamp with time zone,
  end_datetime = (booking_date + end_time)::timestamp with time zone
WHERE start_datetime IS NULL;

-- Create clubs table if it doesn't exist (for the club relation)
CREATE TABLE IF NOT EXISTS public.clubs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  address TEXT,
  phone TEXT,
  email TEXT,
  website TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on clubs table
ALTER TABLE public.clubs ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for clubs
CREATE POLICY IF NOT EXISTS "Allow all operations on clubs" 
ON public.clubs 
FOR ALL 
USING (true) 
WITH CHECK (true);

-- Add foreign key constraints
ALTER TABLE public.courts 
ADD CONSTRAINT IF NOT EXISTS fk_courts_club 
FOREIGN KEY (club_id) REFERENCES public.clubs(id);

ALTER TABLE public.bookings 
ADD CONSTRAINT IF NOT EXISTS fk_bookings_member 
FOREIGN KEY (member_id) REFERENCES public.members(id);

ALTER TABLE public.bookings 
ADD CONSTRAINT IF NOT EXISTS fk_bookings_created_by 
FOREIGN KEY (created_by) REFERENCES public.members(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_courts_club_id ON public.courts(club_id);
CREATE INDEX IF NOT EXISTS idx_courts_type ON public.courts(type);
CREATE INDEX IF NOT EXISTS idx_bookings_member_id ON public.bookings(member_id);
CREATE INDEX IF NOT EXISTS idx_bookings_court_id ON public.bookings(court_id);
CREATE INDEX IF NOT EXISTS idx_bookings_start_datetime ON public.bookings(start_datetime);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON public.bookings(status);

-- Create trigger for updated_at on clubs
CREATE TRIGGER IF NOT EXISTS update_clubs_updated_at
  BEFORE UPDATE ON public.clubs
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();