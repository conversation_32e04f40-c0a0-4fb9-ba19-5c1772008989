-- Create the membership_category enum type first
CREATE TYPE public.membership_category AS ENUM (
  '<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>', 
  'Student',
  '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>amilie'
);

-- Drop and recreate the function with proper enum handling
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (
    id, 
    first_name, 
    last_name, 
    birth_date,
    street,
    house_number,
    postal_code,
    city,
    email, 
    membership_category
  )
  VALUES (
    new.id, 
    new.raw_user_meta_data ->> 'first_name', 
    new.raw_user_meta_data ->> 'last_name',
    CASE 
      WHEN new.raw_user_meta_data ->> 'birth_date' IS NOT NULL 
      THEN (new.raw_user_meta_data ->> 'birth_date')::date
      ELSE NULL
    END,
    new.raw_user_meta_data ->> 'street',
    new.raw_user_meta_data ->> 'house_number',
    new.raw_user_meta_data ->> 'postal_code',
    new.raw_user_meta_data ->> 'city',
    new.email,
    CASE 
      WHEN new.raw_user_meta_data ->> 'membership_category' IS NOT NULL 
      THEN (new.raw_user_meta_data ->> 'membership_category')::public.membership_category
      ELSE 'Erwachsener'::public.membership_category
    END
  );
  RETURN new;
END;
$$;

-- Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();