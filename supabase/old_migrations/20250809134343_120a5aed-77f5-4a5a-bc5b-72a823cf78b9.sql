-- Flexible Group Billing Migration (retry with guards)

-- 1) Add billing_member_id to accounts
ALTER TABLE public.accounts
  ADD COLUMN IF NOT EXISTS billing_member_id uuid;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'accounts_billing_member_fk'
      AND table_name = 'accounts'
  ) THEN
    ALTER TABLE public.accounts
      ADD CONSTRAINT accounts_billing_member_fk
      FOREIGN KEY (billing_member_id)
      REFERENCES public.accounts(id)
      ON DELETE SET NULL;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_accounts_billing_member
  ON public.accounts (billing_member_id);

-- 2) Add billing_member_id to groups
ALTER TABLE public.groups
  ADD COLUMN IF NOT EXISTS billing_member_id uuid;

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints
    WHERE constraint_name = 'groups_billing_member_fk'
      AND table_name = 'groups'
  ) THEN
    ALTER TABLE public.groups
      ADD CONSTRAINT groups_billing_member_fk
      FOREIGN KEY (billing_member_id)
      REFERENCES public.accounts(id)
      ON DELETE SET NULL;
  END IF;
END $$;

CREATE INDEX IF NOT EXISTS idx_groups_billing_member
  ON public.groups (billing_member_id);

-- 3) Create group_members table (many-to-many between groups and accounts)
CREATE TABLE IF NOT EXISTS public.group_members (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id uuid NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
  account_id uuid NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
  role text NOT NULL DEFAULT 'member',
  added_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (group_id, account_id)
);

-- Enable RLS
ALTER TABLE public.group_members ENABLE ROW LEVEL SECURITY;

-- Policies: permissive for now (consistent with existing tables)
DO $$ BEGIN
  CREATE POLICY "Allow all operations on group_members"
  ON public.group_members
  FOR ALL
  USING (true)
  WITH CHECK (true);
EXCEPTION WHEN duplicate_object THEN
  NULL;
END $$;