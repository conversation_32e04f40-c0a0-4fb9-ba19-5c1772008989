-- Add membership_category column to fee_types table to link fees to membership types
ALTER TABLE public.fee_types 
ADD COLUMN membership_category membership_category;

-- Update existing fee types with appropriate membership categories
UPDATE public.fee_types 
SET membership_category = 'Erwachsener'
WHERE name LIKE '%Erwachsene%';

UPDATE public.fee_types 
SET membership_category = 'Jugendlicher'
WHERE name LIKE '%Jugend%';

-- Add index for better performance
CREATE INDEX idx_fee_types_membership_category ON public.fee_types(membership_category);

-- Update some sample data to show the relationship
UPDATE public.fee_types 
SET membership_category = 'Erwachsener', description = 'Vollmitgliedschaft für Erwachsene ab 18 Jahren'
WHERE name = 'Jahresmitgliedschaft Erwachsene';

UPDATE public.fee_types 
SET membership_category = 'Jugendlicher', description = 'Jahresmitgliedschaft für Jugendliche bis 18 Jahre'
WHERE name = 'Jugendmitgliedschaft';